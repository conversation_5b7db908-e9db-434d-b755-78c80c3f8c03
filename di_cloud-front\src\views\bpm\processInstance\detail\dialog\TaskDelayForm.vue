<template>
  <Dialog v-model="dialogVisible" title="申请延期" width="600">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="110px"
    >
      <!-- <div class="form-header">
        <el-button type="primary" link @click="viewProcessDetail" :disabled="!formData.lcid">
          <el-icon><Document /></el-icon>查看原流程详情
        </el-button>
      </div> -->
      <el-form-item label="标题" prop="bt">
        <el-input v-model="formData.bt" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="原办理时限" prop="yblsx">
        <el-input v-model="formData.yblsx" placeholder="原办理时限" disabled />
      </el-form-item>
      <el-form-item label="延期原因" prop="yqyr">
        <el-input v-model="formData.yqyr" type="textarea" placeholder="请输入延期原因" />
      </el-form-item>
      <el-form-item label="拟延期天数" prop="yqts">
        <el-input-number v-model="formData.yqts" :min="1" :max="90" />
      </el-form-item>
      <!-- <el-form-item label="证明材料" prop="zmcl">
        <el-upload
          action=""
          :http-request="uploadFile"
          :limit="5"
          :file-list="fileList"
          :on-exceed="handleExceed"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">请上传证明材料，支持PDF、Word、Excel、图片等格式</div>
          </template>
        </el-upload>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as UserApi from '@/api/system/user'
import * as TaskApi from '@/api/bpm/task'
import { ref } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useRouter } from 'vue-router'
import { Document } from '@element-plus/icons-vue'

defineOptions({ name: 'TaskDelayForm' })

const router = useRouter() // 路由
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const formData = ref({
  id: '',
  userIds: [] as string[],
  bt: '', // 标题
  yblsx: '', // 原办理时限
  yqyr: '', // 延期原因
  yqts: 1, // 延期天数
  zmcl: [] as any[], // 证明材料
  lcid: '', // 流程ID
  lcdycode: '', // 流程定义Xcode
  userid: '', // 发起人id
  reason: '', // 保留原有字段
  processName: '', // 流程实例名称
  processTitle: '' // 流程实例标题
})
const formRules = ref({
  bt: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  yqyr: [{ required: true, message: '延期原因不能为空', trigger: 'blur' }],
  yqts: [{ required: true, message: '延期天数不能为空', trigger: 'blur' }],
  reason: [{ required: true, message: '延期理由不能为空', trigger: 'blur' }]
})
const userList = ref<any[]>([]) // 用户列表
const fileList = ref<any[]>([]) // 上传的文件列表

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (taskId: string, processInstanceId: string, currentDeadline?: string) => {
  dialogVisible.value = true
  resetForm()
  // 保存任务ID和流程实例ID
  formData.value.id = taskId
  formData.value.lcid = processInstanceId
  
  // 保存当前办理时限
  if (currentDeadline) {
    formData.value.yblsx = currentDeadline
    console.log('设置原办理时限:', currentDeadline)
  }
  
  // 根据岗位获得用户列表
  try {
    // 使用UserApi而不是直接fetch
    userList.value = await UserApi.getUserListByPostId({code: "anzy"})
    formData.value.userIds = userList.value.map((item) => item.id)
  } catch (error) {
    console.error('获取用户列表失败:', error)
    message.error('获取用户列表失败')
  }
  
  try {
    // 使用ProcessInstanceApi而不是直接fetch
    const processInstance = await ProcessInstanceApi.getProcessInstance(processInstanceId)
    if (processInstance) {
      // 流程实例ID已经在上面设置了
      formData.value.lcdycode = processInstance.processDefinitionKey || ''
      formData.value.userid = processInstance.startUserId || ''
      
      // 保存流程实例名称，用于构建标题
      formData.value.processName = processInstance.name || '流程'
      // 保存流程实例标题，用于构建申请延期表单标题
      formData.value.processTitle = processInstance.processInstanceTitle || processInstance.name || '流程'
      
      // 设置表单标题
      formData.value.bt = `申请延长"${processInstance.processInstanceTitle || processInstance.name || '流程'}"办理时限`
      
      console.log('获取到流程实例详情:', processInstance)
    } else {
      throw new Error('获取流程实例详情失败')
    }
  } catch (error) {
    console.error('获取流程实例信息失败:', error)
    message.error('获取流程信息失败')
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: '',
    reason: '',
    userIds: userList.value.map((item) => item.id),
    bt: '',
    yblsx: '', // 原办理时限
    yqyr: '',
    yqts: 1,
    zmcl: [],
    lcid: '',
    lcdycode: '',
    userid: '',
    processName: '',
    processTitle: ''
  }
  fileList.value = []
  formRef.value?.resetFields()
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      formLoading.value = true
      try {
        // 按照接口要求准备提交数据
        const submitData = {
          parentProcessInstanceId: formData.value.lcid, // 流程实例ID
          parentProcessInstanceTaskId: formData.value.id, // 任务ID
          applyDelayDays: formData.value.yqts, // 拟申请延期天数
          applyDelayReason: formData.value.yqyr, // 申请延期原因
          processDefinitionKey: 'wtxssqyq_bpm', // 流程定义标识 - 固定值
          processInstanceTitle: `${formData.value.processTitle}的申请延期表单`, // 流程标题
          parentProcessFormKey:"blsx", //主表单办理时限字段
          variables: {
              "bt": formData.value.bt, // 标题
              "yqyy": formData.value.yqyr, // 延期原因
              "yqts": formData.value.yqts, // 延期天数
              "yblsx": formData.value.yblsx, // 原办理时限
              // "zmcl": formData.value.zmcl.map(item => item.name), // 证明材料
              "lcid": formData.value.lcid, // 流程实例ID
              "ejdwjwsjyj":"",//二级单位纪委书记意见
              "jtspyj":"",//集团二室/案管专员审批意见
              "jtjwfsjspyj":""//集团纪委副书记审批意见
            }
        }
        console.log('申请延期提交参数:', submitData)
        
        // 使用TaskApi.applyTaskDelay接口
        await TaskApi.applyTaskDelay(submitData)
        message.success('申请延期成功')
        dialogVisible.value = false
        emit('success')
      } catch (error) {
        console.error('申请延期失败:', error)
        message.error('申请延期失败')
      } finally {
        formLoading.value = false
      }
    }
  })
}

// 文件上传相关方法
const uploadFile = async (params) => {
  try {
    formLoading.value = true
    // 这里应该调用您的文件上传API
    // const response = await FileApi.upload(params.file)
    // 模拟上传成功
    console.log('文件上传参数:', params)
    const fileInfo = {
      name: params.file.name,
      url: URL.createObjectURL(params.file)
    }
    fileList.value.push(fileInfo)
    formData.value.zmcl.push(fileInfo)
    message.success('文件上传成功')
  } catch (error) {
    console.error('文件上传失败:', error)
    message.error('文件上传失败')
  } finally {
    formLoading.value = false
  }
}

const handleExceed = () => {
  message.warning('最多只能上传5个文件')
}

const handleRemove = (file) => {
  const index = fileList.value.findIndex(item => item.url === file.url)
  if (index !== -1) {
    fileList.value.splice(index, 1)
    formData.value.zmcl.splice(index, 1)
  }
}

const beforeUpload = (file) => {
  // 文件类型和大小验证
  const isValidType = /\.(jpg|jpeg|png|gif|bmp|pdf|doc|docx|xls|xlsx)$/i.test(file.name)
  if (!isValidType) {
    message.error('请上传正确格式的文件')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB')
    return false
  }
  return true
}

/** 查看流程详情 */
const viewProcessDetail = () => {
  if (!formData.value.lcid) {
    message.warning('无法获取流程实例ID')
    return
  }
  
  // 关闭当前弹窗
  dialogVisible.value = false
  
  // 跳转到流程实例详情页面
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: formData.value.lcid
    }
  })
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script> 
