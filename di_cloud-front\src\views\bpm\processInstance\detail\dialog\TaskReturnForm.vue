<template>
  <Dialog v-model="dialogVisible" title="退回任务" width="500" @close="handleClose">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="110px"
    >
      <el-form-item label="退回节点" prop="targetTaskDefinitionKey">
        <el-select v-model="formData.targetTaskDefinitionKey" clearable style="width: 100%">
          <el-option
            v-for="item in returnList"
            :key="item.taskDefinitionKey"
            :label="item.name"
            :value="item.taskDefinitionKey"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退回理由" prop="reason">
        <el-input v-model="formData.reason" clearable placeholder="请输入退回理由" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" name="TaskRollbackDialogForm" setup>
import * as TaskApi from '@/api/bpm/task'

const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const formData = ref({
  id: '',
  targetTaskDefinitionKey: undefined,
  reason: '',
  taskDefinitionKey: ''
})
const formRules = ref({
  targetTaskDefinitionKey: [{ required: true, message: '必须选择退回节点', trigger: 'change' }],
  reason: [{ required: true, message: '退回理由不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref
const returnList = ref([] as any)
/** 打开弹窗 */
const open = async (id: string, task?: any) => {
  returnList.value = await TaskApi.getTaskListByReturn(id)
  if (returnList.value.length === 0) {
    message.warning('当前没有可退回的节点')
    return false
  }
  dialogVisible.value = true
  resetForm()
  formData.value.id = id
  
  // 如果提供了task参数，保存其taskDefinitionKey
  if (task && task.taskDefinitionKey) {
    formData.value.taskDefinitionKey = task.taskDefinitionKey
    console.log('已保存当前任务的taskDefinitionKey:', task.taskDefinitionKey)
  } else {
    console.warn('未能获取当前任务的taskDefinitionKey')
  }
}
defineExpose({ open }) // 提供 openModal 方法，用于打开弹窗

/** 处理关闭对话框事件 */
const handleClose = () => {
  dialogVisible.value = false
  setTimeout(() => {
    resetForm()
  }, 200)
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    await TaskApi.returnTask(formData.value)
    message.success('退回成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: '',
    targetTaskDefinitionKey: undefined,
    reason: '',
    taskDefinitionKey: ''
  }
  formRef.value?.resetFields()
}
</script>
