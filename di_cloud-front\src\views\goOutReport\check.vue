<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-card class="box-card">
      <!-- <template #header>
        <div class="card-header">
          <span class="card-title"></span>
        </div>
      </template> -->

      <el-form
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        class="search-form"
      >
        <el-form-item label="报备时间" prop="timeRange">
          <el-date-picker
            v-model="queryParams.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            class="!w-240px"
          />
        </el-form-item>
        
        <el-form-item label="二级单位" prop="secondaryDept">
          <el-select
            v-model="queryParams.secondaryDept"
            placeholder="请选择单位"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dept in deptOptions"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="报备人员" prop="reporter">
          <el-input
            v-model="queryParams.reporter"
            placeholder="请输入报备人员"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> 查询
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报备审核表格 -->
    <el-card class="box-card history-card">
      <el-table v-loading="loading" :data="reportHistory" style="width: 100%" border stripe>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="startDate" label="开始日期" width="110" />
        <el-table-column prop="endDate" label="结束日期" width="110" />
        <el-table-column prop="secondaryDept" label="所属二级单位" min-width="120" />
        <el-table-column prop="reporter" label="报备人员" width="100" />
        <el-table-column prop="location" label="外出地点" min-width="120" />
        <el-table-column prop="reason" label="外出事由" min-width="180" :show-overflow-tooltip="true" />
        <el-table-column prop="createTime" label="提交时间" width="160" />
      </el-table>
      
      <!-- 空数据提示 -->
      <el-empty v-if="reportHistory.length === 0 && !loading" description="暂无报备记录" />
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="queryParams.pageSize"
          :current-page="queryParams.pageNo"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 审批对话框 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="审批外出报备"
      width="500px"
      append-to-body
    >
      <el-form 
        :model="approveForm" 
        ref="approveFormRef" 
        label-width="100px"
      >
        <el-form-item label="审批结果" prop="result" :rules="[{ required: true, message: '请选择审批结果', trigger: 'change' }]">
          <el-radio-group v-model="approveForm.result">
            <el-radio label="approved">通过</el-radio>
            <el-radio label="rejected">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见" prop="comment">
          <el-input 
            v-model="approveForm.comment" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入审批意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approveDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitApprove">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getManagerTripReportPage, getSecondaryDepartments } from '@/api/bpm/processInstance'

// 类型定义
interface ReportRecord {
  id?: string
  startDate: string
  endDate: string
  place: string
  notes: string
  createTime: number | string
  userId?: number
  userName?: string | null
  deptId?: number
  deptName?: string | null
  location?: string
  reason?: string
  secondaryDept?: string
  reporter?: string
  status: 'pending' | 'approved' | 'rejected'
}

interface ApproveForm {
  id: string
  result: 'approved' | 'rejected'
  comment: string
}

// 部门选项接口
interface Department {
  id: string
  name: string
}

// 查询参数
const queryFormRef = ref()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  timeRange: [] as string[],
  secondaryDept: '',
  reporter: ''
})

// 二级单位选项
const deptOptions = ref<Department[]>([])

// 数据
const loading = ref(false)
const reportHistory = ref<ReportRecord[]>([])
const total = ref(0)

// 审批相关
const approveDialogVisible = ref(false)
const approveFormRef = ref()
const approveForm = reactive<ApproveForm>({
  id: '',
  result: 'approved',
  comment: ''
})
const currentRecord = ref<ReportRecord | null>(null)

// 获取状态标签样式
const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  const statusMap = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 获取二级单位列表
const getDepartments = async () => {
  try {
    const res = await getSecondaryDepartments()
    if (res) {
      deptOptions.value = res || []
    } else {
      ElMessage.error('获取单位列表失败')
    }
  } catch (error) {
    console.error('获取单位列表失败:', error)
    ElMessage.error('获取单位列表失败')
  }
}

// 获取历史记录
const getReportHistory = async () => {
  loading.value = true
  try {
    const params: any = {
      pageNo: queryParams.pageNo.toString(),
      pageSize: queryParams.pageSize.toString(),
      deptId: queryParams.secondaryDept
    }
    
    // 如果选择了时间范围，添加到查询参数
    if (queryParams.timeRange && queryParams.timeRange.length === 2) {
      params.startDate = queryParams.timeRange[0]
      params.endDate = queryParams.timeRange[1]
    }
    
    const res = await getManagerTripReportPage(params)
    
    if (res) {
      // 处理数据格式适配
      reportHistory.value = (res.list || []).map(item => {
        const record: ReportRecord = {
          ...item,
          location: item.place,
          reason: item.notes,
          secondaryDept: item.deptName || '-',
          reporter: item.userName || '-',
          status: 'pending', // 默认状态，实际应根据后端返回调整
          createTime: item.createTime ? new Date(item.createTime).toLocaleString() : '-'
        };
        return record;
      })
      total.value = res.total || 0
    } else {
      ElMessage.error('获取报备列表失败')
      reportHistory.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取报备列表失败:', error)
    ElMessage.error('获取报备列表失败')
    reportHistory.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNo = 1
  getReportHistory()
}

// 重置按钮操作
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNo = page
  getReportHistory()
}

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryParams.pageNo = 1
  getReportHistory()
}

// 查看详情
const handleDetail = (row: ReportRecord) => {
  ElMessageBox.alert(
    `
    开始日期：${row.startDate}<br/>
    结束日期：${row.endDate}<br/>
    所属单位：${row.secondaryDept}<br/>
    报备人员：${row.reporter}<br/>
    外出地点：${row.location}<br/>
    外出事由：${row.reason}<br/>
    提交时间：${row.createTime}<br/>
    审核状态：${getStatusLabel(row.status)}
    `, 
    '报备详情', 
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定'
    }
  )
}

// 打开审批对话框
const handleApprove = (row: ReportRecord) => {
  currentRecord.value = row
  approveForm.id = row.id || ''
  approveForm.result = 'approved'
  approveForm.comment = ''
  approveDialogVisible.value = true
}

// 提交审批
const submitApprove = () => {
  approveFormRef.value.validate((valid: boolean) => {
    if (valid) {
      // 这里应该调用后端API提交审批
      console.log('提交审批', approveForm)
      
      // 模拟审批成功
      ElMessage.success('审批操作成功')
      
      // 更新本地数据
      const index = reportHistory.value.findIndex(item => item.id === approveForm.id)
      if (index !== -1) {
        reportHistory.value[index].status = approveForm.result
      }
      
      approveDialogVisible.value = false
    }
  })
}

// 页面初始化
onMounted(() => {
  getDepartments()
  getReportHistory()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: bold;
  font-size: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.history-card {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
