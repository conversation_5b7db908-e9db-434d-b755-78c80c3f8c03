import request from '@/config/axios'

// BPM 流程分类 VO
export interface CategoryVO {
  id: number // 分类编号
  name: string // 分类名
  code: string // 分类标志
  status: number // 分类状态
  sort: number // 分类排序
}

// BPM 流程分类 API
export const CategoryApi = {
  // 查询流程分类分页
  getCategoryPage: async (params: any) => {
    return await request.get({ url: `/bpm/category/page`, params })
  },

  // 查询流程分类列表
  getCategorySimpleList: async () => {
    return await request.get({ url: `/bpm/category/simple-list` })
  },
  // 查询流程分类列表ByCode
  getCategorySimpleListByCode: async (code) => {
    const url = `/bpm/category/simple-list-byCode?code=`+code;
    return await request.get({ url: url })
  },
  // 查询流程分类详情
  getCategory: async (id: number) => {
    return await request.get({ url: `/bpm/category/get?id=` + id })
  },

  // 新增流程分类
  createCategory: async (data: CategoryVO) => {
    return await request.post({ url: `/bpm/category/create`, data })
  },

  // 修改流程分类
  updateCategory: async (data: CategoryVO) => {
    return await request.put({ url: `/bpm/category/update`, data })
  },

  // 删除流程分类
  deleteCategory: async (id: number) => {
    return await request.delete({ url: `/bpm/category/delete?id=` + id })
  }
}

export const categoryRoute={
  //categoryCode : "",
  getCategoryByRouteParam: async (route) => {
   // console.info("route",route)
    //console.info("routeJSON:",JSON.stringify(route))
    let categoryCode=null;
    if (route.query) {
      if (route.query.category) {
        categoryCode = route.query.category
        //console.info("categoryCode:",categoryCode)
        return categoryCode
      }
    }
    if(!route.meta){
      return null;
    }
    if (route.meta.query){
      if(route.meta.query.category){
        categoryCode = route.meta.query.category
       // console.info("categoryCode:",categoryCode)
       return categoryCode
      }
    }
   // console.info("categoryCode:",null)
  },
 /* getRouteNameByCategory:  async (route) => {
    let routeName = "";
    const categoryCode = await this.getCategoryByRouteParam(route)
      //await this.getCategoryByRouteParam(route)
    console.info("categoryCode:",categoryCode)
    if (categoryCode ==="oa_xf"){
      routeName = "BpmProcessInstanceCreateByXF";
    }
    if (categoryCode ==="oa_xs"){
      routeName = "BpmProcessInstanceCreateByXS";
    }


    return routeName
  }*/
}
