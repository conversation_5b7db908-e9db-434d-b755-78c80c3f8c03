<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <!-- <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">我的报备记录</span>
        </div>
      </template>

      <el-form
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        class="search-form"
      >
        <el-form-item label="报备时间" prop="timeRange">
          <el-date-picker
            v-model="queryParams.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            class="!w-240px"
          />
        </el-form-item>
        
        <el-form-item label="外出地点" prop="location">
          <el-input
            v-model="queryParams.location"
            placeholder="请输入外出地点"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> 查询
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card> -->

    <!-- 报备历史表格 -->
    <el-card class="box-card history-card">
      <el-table v-loading="loading" :data="reportHistory" style="width: 100%" border stripe>
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="startDate" label="开始日期" width="120" />
        <el-table-column prop="endDate" label="结束日期" width="120" />
        <el-table-column prop="location" label="外出地点" min-width="120" />
        <el-table-column prop="reason" label="外出事由" min-width="180" :show-overflow-tooltip="true" />
        <el-table-column prop="createTime" label="提交时间" width="160" />
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleDetail(scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 空数据提示 -->
      <el-empty v-if="reportHistory.length === 0 && !loading" description="暂无报备记录" />
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="queryParams.pageSize"
          :current-page="queryParams.pageNo"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMyTripReportPage } from '@/api/bpm/processInstance'

// 类型定义
interface ReportRecord {
  id?: string
  startDate: string
  endDate: string
  place: string
  notes: string
  createTime: number | string
  userId?: number
  userName?: string
  deptId?: number
  deptName?: string
  location?: string
  reason?: string
}

// 查询参数
const queryFormRef = ref()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  timeRange: [] as string[],
  location: ''
})

// 数据
const loading = ref(false)
const reportHistory = ref<ReportRecord[]>([])
const total = ref(0)

// 获取历史记录
const getReportHistory = async () => {
  loading.value = true
  try {
    const res = await getMyTripReportPage({
      pageNo: queryParams.pageNo.toString(),
      pageSize: queryParams.pageSize.toString()
    })
    
    if (res) {
      // 处理数据格式适配
      reportHistory.value = (res.list || []).map(item => ({
        ...item,
        location: item.place,
        reason: item.notes,
        createTime: item.createTime ? new Date(item.createTime).toLocaleString() : ''
      }))
      total.value = res.total || 0
    } else {
      ElMessage.error('获取报备列表失败')
      reportHistory.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取报备列表失败:', error)
    ElMessage.error('获取报备列表失败')
    reportHistory.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNo = 1
  getReportHistory()
}

// 重置按钮操作
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNo = page
  getReportHistory()
}

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryParams.pageNo = 1
  getReportHistory()
}

// 查看详情
const handleDetail = (row: ReportRecord) => {
  ElMessageBox.alert(
    `
    开始日期：${row.startDate}<br/>
    结束日期：${row.endDate}<br/>
    外出地点：${row.location}<br/>
    外出事由：${row.reason}<br/>
    提交时间：${row.createTime}
    `, 
    '报备详情', 
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定'
    }
  )
}

// 页面初始化
onMounted(() => {
  getReportHistory()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: bold;
  font-size: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.history-card {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
