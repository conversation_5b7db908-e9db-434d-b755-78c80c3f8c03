<template>
  <el-card shadow="never">
    <el-skeleton 
      :loading="loading" 
      animated  
      v-for="(type) in newsType"
      :key="`type-${type.value}`"
      class="news-type-section"
    >
      <div class="category-header">
        <span class="category-title">{{ type.label }}</span>
        <el-link type="primary" :underline="false" @click="handleMore(type.value)" class="more-link">
          {{ t('action.more') }}
        </el-link>
      </div>
      <el-divider class="category-divider" />
      <el-row class="news-category-container">
        <template v-if="getNewsByCategory(type.value).length > 0">
          <el-col
            v-for="(item, index) in getNewsByCategory(type.value)"
            :key="`card-${index}`"
            :xl="8"
            :lg="8"
            :md="8"
            :sm="24"
            :xs="24"
            class="form-layout"
            @click="handleDetail(item)"
          >
            <el-card shadow="hover" class="mr-5px mt-5px news-card" :class="'hover-style'">
              <div class="flex items-center card-header" :class="'font-hover-style'">
                <div class="icon-container" v-if='item.icon'>
                  <img src="@/assets/svgs/hot.svg" alt="热门" class="hot-icon"/> 
                </div>
                <span class="text-16px title-text">{{ item.name }}</span>
              </div>
              <div class="mt-12px flex justify-between text-12px text-gray-400 card-footer" :class="'font-hover-style'">
                <span>{{ item.personal }}</span>
                <span>{{ formatTime(item.time, 'yyyy-MM-dd') }}</span>
              </div>
            </el-card>
          </el-col>
        </template>
        <template v-else>
          <div class="empty-news-container">
            <el-empty description="暂无新闻" :image-size="80" />
          </div>
        </template>
      </el-row>
    </el-skeleton>
  </el-card>
</template>

<script lang="ts" setup>
import { formatTime } from '@/utils'
import type { Project } from '../types'

interface NewsType {
  label: string
  value: string | number
}

const props = defineProps({
  loading: {
    type: Boolean,
    required: true
  },
  newsType: {
    type: Array as PropType<NewsType[]>,
    required: true
  },
  projects: {
    type: Array as PropType<Project[]>,
    required: true
  }
})

const emit = defineEmits(['view-more', 'view-detail'])
const { t } = useI18n()

/**
 * 按类别筛选新闻
 */
const getNewsByCategory = (category: string | number) => {
  return props.projects.filter((v) => v.news_category === category).slice(0, 3)
}

/**
 * 查看更多新闻
 */
const handleMore = (category: string | number) => {
  emit('view-more', category)
}

/**
 * 查看新闻详情
 */
const handleDetail = (item: Project) => {
  emit('view-detail', item)
}
</script>

<style scoped>
.news-type-section {
  margin-bottom: 24px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.category-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 12px;
}

.category-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: #409EFF;
  border-radius: 2px;
}

.more-link {
  font-size: 14px;
}

.category-divider {
  margin: 8px 0 16px;
}

.hover-style:hover {
  border: 2px solid #409EFF;
  box-shadow: 0 0 4px rgba(64, 158, 255, 0.4);
  transform: translateZ(0);
}

.hover-style {
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.font-hover-style:hover {
  color: #303133;
}

.icon-container {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  flex-shrink: 0;
}

.hot-icon {
  width: 15px;
  height: 15px;
  object-fit: contain;
}

.title-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 2.5em;
  line-height: 1.25;
}

.news-card {
  height: 120px;
  width: 100%;
  min-height: unset;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.card-header {
  flex: 0 0 auto;
}

.card-content {
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 1.5;
}

.card-footer {
  flex: 0 0 auto;
}

.form-layout {
  margin-bottom: 20px;
  box-sizing: border-box;
}

.news-category-container {
  min-height: 160px;
  display: flex;
  flex-wrap: wrap;
}

.empty-news-container {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  color: #909399;
}
</style> 