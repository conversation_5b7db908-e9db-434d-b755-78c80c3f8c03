import { generateUUID } from '@/utils'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'

export const useTemplateDownloadRule = () => {
  const label = '模板文件下载'
  const name = 'TemplateDownload'
  return {
    icon: 'icon-download',
    label,
    name,
    rule() {
      const field = generateUUID()
      
      return {
        type: name,
        field: field,
        title: label,
        info: '',
        $required: false,
        props: {
          field: field,
          fileName: '模板文件',
          filePath: '',
          buttonText: '',
          buttonType: 'primary',
          buttonSize: 'default',
          showIcon: true
        }
      }
    },
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        {
          type: 'input',
          field: 'field',
          title: '字段名称',
          info: '表单提交时的字段名',
          props: {
            placeholder: '请输入字段名称'
          }
        },
        {
          type: 'input',
          field: 'fileName',
          title: '文件名称',
          value: '模板文件',
          props: {
            placeholder: '显示的文件名称'
          }
        },
        {
          type: 'input',
          field: 'filePath',
          title: '文件路径',
          value: '',
          props: {
            placeholder: '例如：/templates/example.xlsx'
          },
          info: '相对路径，将与当前域名拼接'
        },
        {
          type: 'input',
          field: 'buttonText',
          title: '按钮文字',
          value: '',
          props: {
            placeholder: '按钮上显示的文字，留空则只显示图标'
          },
          info: '设置为空时将只显示图标而不显示文字'
        },
        {
          type: 'select',
          field: 'buttonType',
          title: '按钮类型',
          value: 'primary',
          options: [
            { label: '主要按钮', value: 'primary' },
            { label: '成功按钮', value: 'success' },
            { label: '警告按钮', value: 'warning' },
            { label: '危险按钮', value: 'danger' },
            { label: '信息按钮', value: 'info' },
            { label: '文本按钮', value: 'text' }
          ]
        },
        {
          type: 'select',
          field: 'buttonSize',
          title: '按钮大小',
          value: 'default',
          options: [
            { label: '大', value: 'large' },
            { label: '默认', value: 'default' },
            { label: '小', value: 'small' }
          ]
        },
        {
          type: 'switch',
          field: 'showIcon',
          title: '显示图标',
          value: true
        }
      ])
    }
  }
} 