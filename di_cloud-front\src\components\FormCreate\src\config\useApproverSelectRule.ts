import { generateUUID } from '@/utils'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'

export const useApproverSelectRule = () => {
  const label = '审批人选择'
  const name = 'ApproverSelect'
  return {
    icon: 'icon-user-o',
    label,
    name,
    rule() {
      return {
        type: name,
        field: generateUUID(),
        title: label,
        info: '',
        $required: true,
        props: {
          clearable: true,
          placeholder: '请选择审批人'
        }
      }
    },
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        {
          type: 'switch',
          field: 'clearable',
          title: '是否可清空',
          value: true
        },
        {
          type: 'input',
          field: 'placeholder',
          title: '占位符',
          value: '请选择审批人'
        }
      ])
    }
  }
} 