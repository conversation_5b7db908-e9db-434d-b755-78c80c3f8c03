<template>
  <div :class="prefixCls" class="relative h-full custom-red-bg">
    <div class="relative mx-auto h-full flex">
      <!-- 左侧容器，包含背景图和欢迎语 -->
      <div :class="`${prefixCls}__left flex-1 relative p-30px lt-xl:hidden overflow-hidden`">
        <!-- 左上角的 logo -->
        <div class="relative flex items-center text-white mb-20px">
          <img alt="光明食品集团" class="h-80px" src="/gmlogoWhite.png" />
        </div>
        <!-- 左边的欢迎语 -->
        <div class="h-[calc(100%-240px)] flex items-center justify-center">
          <div class="text-center">
            <div class="text-white font-bold mb-8 welcome-text leading-tight">
              忠诚干净担当
            </div>
            <div class="text-white font-bold welcome-text leading-tight welcome-text-indent">
              敢于善于斗争
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧容器，包含登录表单 -->
      <div class="relative flex-1 p-30px lt-sm:p-10px overflow-hidden">
        <!-- 右边的登录界面 -->
        <Transition appear enter-active-class="animate__animated animate__fadeInRight">
          <div class="m-auto h-[calc(100%-60px)] w-[100%] flex items-center justify-center at-2xl:max-w-500px at-lg:max-w-500px at-md:max-w-500px at-xl:max-w-500px">
            <!-- 账号登录 -->
            <LoginForm class="m-auto h-auto p-20px rounded-3xl bg-white shadow-lg lt-xl:rounded-3xl lt-xl:bg-white custom-login-form" />
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>import { underlineToHump } from '@/utils'
import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { LoginForm } from './components'
import { useI18n } from 'vue-i18n'


defineOptions({ name: 'Login' })

const { t } = useI18n()
const appStore = useAppStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('login')
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-login;

.custom-red-bg {
  background: var(--login-primary-color); /* 使用CSS变量 */
  background-size: cover;
  background-position: center;
}

.welcome-text {
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
  line-height: 1.1;
  font-weight: 500;
  letter-spacing: 0.1em;
  font-family: '方正行楷简体', 'FangZheng XingKai Simplified', '华文行楷', 'STXingkai', '毛泽东字体', '毛体', '行草体', '草书', 'FangSong', 'KaiTi', 'serif';
  white-space: nowrap;
  font-size: 5rem; /* 固定字体大小，约64px */
}

.welcome-text-indent {
  padding-left: 1em;
}


.#{$prefix-cls} {
  overflow: hidden;
  height: 100vh;

  &__left {
    background: transparent; /* 移除原有背景，使用整体红色背景 */
    color: white;
  }
}

/* 自定义登录表单样式 */
.custom-login-form {
  :deep(.el-button--primary) {
    background-color: var(--login-primary-color);
    border-color: var(--login-primary-color);
    
    &:hover {
      background-color: var(--login-primary-hover);
      border-color: var(--login-primary-hover);
    }
    
    &:active {
      background-color: var(--login-primary-active);
      border-color: var(--login-primary-active);
    }
    
    &:focus {
      background-color: var(--login-primary-color);
      border-color: var(--login-primary-color);
      box-shadow: 0 0 0 2px var(--login-primary-hover);
    }
  }
  
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--login-primary-color);
    }
    
    &.is-focus {
      border-color: var(--login-primary-color);
      box-shadow: 0 0 0 1px var(--login-primary-color);
    }
  }
  
  :deep(.el-form-item__label) {
    color: #333;
    font-weight: 500;
  }
  
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: var(--login-primary-color);
    border-color: var(--login-primary-color);
  }
  
  :deep(.el-checkbox__label) {
    color: var(--login-primary-color) !important;
    font-weight: 500;
  }
  
  :deep(.el-link--primary) {
    color: var(--login-primary-color) !important;
    text-decoration: none !important;
    
    &:hover {
      color: var(--login-primary-hover) !important;
      text-decoration: underline !important;
      text-decoration-color: var(--login-primary-color) !important;
      text-underline-offset: 2px;
    }
    
    &:focus {
      color: var(--login-primary-color) !important;
      text-decoration: underline !important;
      text-decoration-color: var(--login-primary-color) !important;
      text-underline-offset: 2px;
    }
    
    &:active {
      color: var(--login-primary-active) !important;
      text-decoration: none !important;
    }
    
    /* 移除Element Plus默认样式 */
    &.el-link {
      border-bottom: none !important;
      
      &:hover {
        border-bottom: none !important;
      }
      
      &:after {
        border-bottom: none !important;
      }
    }
  }
}

@media (max-width: 1200px) {
  .#{$prefix-cls}__left {
    display: none;
  }
  
  .custom-red-bg {
    background: var(--login-primary-color); /* 小屏幕时也保持红色背景 */
  }
  
  .custom-login-form {
    /* 在小屏幕时确保表单可见性 */
    background: white !important;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
}
</style>

<style lang="scss">
.dark .login-form {
  .el-divider__text {
    background-color: var(--login-bg-color);
  }

  .el-card {
    background-color: var(--login-bg-color);
  }
}
</style>
