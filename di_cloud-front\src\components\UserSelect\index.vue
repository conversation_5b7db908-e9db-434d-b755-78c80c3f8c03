<template>
  <div class="user-select-container">
    <div class="user-select-layout">
      <!-- 左侧部门树 -->
      <div class="department-tree-container">
        <div class="panel-header">部门列表</div>
        <el-input
          v-model="deptSearchKeyword"
          placeholder="搜索部门"
          clearable
          prefix-icon="Search"
          class="dept-search-input"
          @input="filterDeptTree"
        />
        <div class="tree-actions">
          <el-button type="text" @click="clearDeptSelect">全部部门</el-button>
        </div>
        <el-tree
          ref="deptTreeRef"
          :data="deptTreeData"
          :props="{ label: 'name', children: 'children' }"
          node-key="id"
          highlight-current
          :filter-node-method="filterNode"
          @node-click="handleDeptSelect"
          class="dept-tree"
          :current-node-key="selectedDeptId === null ? undefined : selectedDeptId"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <span>{{ node.label }}</span>
              <span class="user-count" v-if="getDeptUserCount(data.id) > 0">
                ({{ getDeptUserCount(data.id) }})
              </span>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 右侧用户列表 -->
      <div class="user-list-container">
        <div class="panel-header">
          <span>{{ selectedDeptName ? `${selectedDeptName}下的用户` : '所有用户' }}</span>
        </div>
        <el-input
          v-model="userSearchKeyword"
          placeholder="搜索用户"
          clearable
          prefix-icon="Search"
          class="user-search-input"
        />
        <div class="user-list">
          <el-checkbox
            v-model="selectAllUsers"
            :indeterminate="isIndeterminate"
            @change="handleSelectAllChange"
            class="select-all-checkbox"
          >
            全选
          </el-checkbox>
          
          <el-checkbox-group v-model="selectedUserIds" @change="handleUserSelectionChange">
            <div v-for="user in filteredUsers" :key="user.id" class="user-item">
              <el-checkbox :label="user.id" class="user-checkbox">
                <div class="user-info">
                  <span class="user-name">{{ user.nickname }}</span>
                  <span class="user-dept" v-if="user.deptName">({{ user.deptName }})</span>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
          
          <div v-if="filteredUsers.length === 0" class="no-data">
            无匹配用户
          </div>
        </div>
      </div>
    </div>

    <!-- 审批方式选择 -->
    <div class="approve-method-container" v-if="finalSelectedUsers.length > 1">
      <div class="approve-method-title">审批方式：</div>
      <el-radio-group v-model="approveMethod" @change="handleApproveMethodChange">
        <el-radio :label="4">依次审批</el-radio>
        <el-radio :label="2">会签</el-radio>
        <el-radio :label="3">或签</el-radio>
      </el-radio-group>
      <div class="approve-method-desc" v-if="approveMethod === 4">
        <el-icon><InfoFilled /></el-icon> 
        <span>审批人将按照选择的顺序依次审批，前一个人审批通过后，下一个人才可以审批</span>
      </div>
      <div class="approve-method-desc" v-else-if="approveMethod === 2">
        <el-icon><InfoFilled /></el-icon> 
        <span>所有审批人必须全部审批通过，否则流程无法继续</span>
      </div>
      <div class="approve-method-desc" v-else-if="approveMethod === 3">
        <el-icon><InfoFilled /></el-icon> 
        <span>任意一位审批人同意即可通过，任意一位审批人拒绝即被拒绝</span>
      </div>
    </div>
    
    <!-- 已选用户展示区 -->
    <div class="selected-users-container" v-if="finalSelectedUsers.length > 0">
      <div class="selected-users-title">已选择的审批人 ({{ finalSelectedUsers.length }}人)：</div>
      <div class="selected-users-list">
        <el-tag
          v-for="user in finalSelectedUsers"
          :key="user.id"
          closable
          @close="removeUser(user.id)"
          class="user-tag"
        >
          {{ user.nickname }}
          <span v-if="user.deptName" class="user-tag-info">({{ user.deptName }})</span>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// 定义枚举
export enum ApproveMethodType {
  /**
   * 随机挑选一人审批
   */
  RANDOM_SELECT_ONE_APPROVE = 1,

  /**
   * 多人会签(按通过比例)
   */
  APPROVE_BY_RATIO = 2,

  /**
   * 多人或签(通过只需一人，拒绝只需一人)
   */
  ANY_APPROVE = 3,
  
  /**
   * 多人依次审批
   */
  SEQUENTIAL_APPROVE = 4
}
</script>

<script lang="ts" setup>
import { ref, watch, onMounted, computed, nextTick } from 'vue'
import { handleTree } from '@/utils/tree'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import request from '@/config/axios'
import { InfoFilled } from '@element-plus/icons-vue'

// 定义用户类型
interface UserType {
  id: number | string
  nickname: string
  deptName?: string
  deptId?: number | string
  [key: string]: any
}

// 定义部门类型
interface DeptType {
  id: number | string
  name: string
  parentId: number | string
  sort: number
  children?: DeptType[]
  [key: string]: any
}

defineOptions({ name: 'UserSelect' })

const props = defineProps({
  modelValue: {
    type: Array as () => (number | string)[],
    default: () => []
  },
  defaultApproveMethod: {
    type: Number,
    default: ApproveMethodType.SEQUENTIAL_APPROVE
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'approveMethodChange'])

// 部门树相关
const deptTreeRef = ref()
const deptTreeData = ref<DeptType[]>([])
const deptSearchKeyword = ref('')
const selectedDeptId = ref<string | number | null>(null)
const selectedDeptName = ref('')

// 用户列表相关
const userList = ref<UserType[]>([])
const deptUserMap = ref<Map<number | string, UserType[]>>(new Map())
const userSearchKeyword = ref('')
const selectedUserIds = ref<(number | string)[]>([])
const selectAllUsers = ref(false)
const isIndeterminate = ref(false)

// 最终选择的用户列表
const finalSelectedUsers = ref<UserType[]>([])

// 审批方式
const approveMethod = ref<ApproveMethodType>(props.defaultApproveMethod)

// 获取部门下用户数量
const getDeptUserCount = (deptId: number | string): number => {
  return deptUserMap.value.get(deptId)?.length || 0
}

// 过滤部门树
const filterNode = (value: string, data: DeptType) => {
  if (!value) return true
  return data.name.includes(value)
}

const filterDeptTree = () => {
  if (deptTreeRef.value) {
    deptTreeRef.value.filter(deptSearchKeyword.value)
  }
}

// 加载部门树
const loadDeptTree = async () => {
  try {
    console.log('开始加载部门树')
    
    const deptOptions = await DeptApi.getSimpleDeptList()
    console.log('获取到部门列表，原始数量:', deptOptions.length)
    
    // 处理成树形结构
    const treeData = handleTree(deptOptions, 'id')
    console.log('处理成树形结构后，顶级部门数量:', treeData.length)
    
    // 记录每个部门的完整路径，用于调试
    const logDeptTree = (depts: DeptType[], path = '') => {
      depts.forEach(dept => {
        const currentPath = path ? `${path} > ${dept.name}` : dept.name
        console.log(`部门: ${currentPath}, ID: ${dept.id}`)
        
        if (dept.children && dept.children.length > 0) {
          logDeptTree(dept.children, currentPath)
        }
      })
    }
    
    logDeptTree(treeData)
    
    deptTreeData.value = treeData
    console.log('部门树加载完成')
    
    return treeData
  } catch (error) {
    console.error('加载部门树失败:', error)
    return []
  }
}

// 递归查找特定ID的部门
const findDeptById = (depts: DeptType[], id: number | string): DeptType | null => {
  for (const dept of depts) {
    if (String(dept.id) === String(id)) {
      return dept
    }
    
    if (dept.children && dept.children.length > 0) {
      const found = findDeptById(dept.children, id)
      if (found) {
        return found
      }
    }
  }
  
  return null
}

// 处理部门选择
const handleDeptSelect = async (dept: DeptType) => {
  console.log('-------------------------------------------')
  console.log('选择部门:', dept.name, 'ID:', dept.id)
  
  // 查找完整的部门对象
  const fullDept = findDeptById(deptTreeData.value, dept.id)
  if (fullDept) {
    console.log('找到完整部门对象:', fullDept.name, 'ID:', fullDept.id)
  } else {
    console.warn('未找到完整部门对象, 使用选中的部门对象')
  }
  
  // 更新选中的部门ID和名称
  selectedDeptId.value = dept.id
  selectedDeptName.value = dept.name
  
  // 加载部门用户
  const success = await loadDeptUsers(dept.id, dept.name)
  if (!success) {
    console.warn(`未能从现有用户中找到部门 ${dept.id} 的用户，可能需要刷新用户列表`)
    // 如果未能找到用户，需要修复部门-用户映射
    fixDeptUserMapping()
  }
  
  // 强制更新用户列表
  nextTick(() => {
    console.log('nextTick中更新用户列表和复选框状态')
    // 在下一个tick强制更新，确保DOM已更新
    updateFilteredUsersList()
    updateCheckboxState()
    
    // 再次检查筛选后的用户列表
    console.log('筛选后的用户列表长度:', filteredUsers.value.length)
    if (filteredUsers.value.length === 0) {
      console.warn('筛选后没有用户，可能存在映射问题')
    } else {
      console.log('筛选后的用户列表:', filteredUsers.value.map(u => `${u.nickname} (ID: ${u.id})`).join(', '))
    }
  })
}

// 专门用于加载部门用户的函数
const loadDeptUsers = async (deptId: number | string, deptName: string) => {
  try {
    console.log(`开始加载部门 ${deptId} 的用户列表`)
    
    // 从用户列表中筛选该部门的用户
    if (userList.value.length > 0) {
      console.log(`从现有用户列表中筛选部门 ${deptId} 的用户`)
      const filteredUsers = userList.value.filter(user => 
        String(user.deptId) === String(deptId)
      )
      
      if (filteredUsers.length > 0) {
        console.log(`在用户列表中找到 ${filteredUsers.length} 个用户属于部门 ${deptId}`)
        deptUserMap.value.set(deptId, filteredUsers)
        return true
      } else {
        console.warn(`在用户列表中未找到部门 ${deptId} 的用户，可能需要加载`)
      }
    }
    
    return false
  } catch (error) {
    console.error(`加载部门 ${deptId} 用户时出错:`, error)
    return false
  }
}

// 清除部门选择
const clearDeptSelect = () => {
  console.log('清除部门选择')
  selectedDeptId.value = null
  selectedDeptName.value = ''
  
  // 强制更新用户列表
  updateFilteredUsersList()
  
  // 更新选择状态
  updateCheckboxState()
}

// 强制更新用户列表
const updateFilteredUsersList = () => {
  // 此函数目的是强制更新filteredUsers计算属性
  // 通过修改一个会影响filteredUsers的响应式变量来实现
  userSearchKeyword.value = userSearchKeyword.value + ''
}

// 计算当前显示的用户列表
const filteredUsers = computed(() => {
  let users: UserType[] = []
  
  console.log('重新计算filteredUsers, 当前选中部门ID:', selectedDeptId.value)
  
  // 如果有选中的部门，显示该部门下的用户
  if (selectedDeptId.value) {
    const deptId = selectedDeptId.value
    users = deptUserMap.value.get(deptId) || []
    console.log(`从部门映射中获取部门 ${deptId} 的用户, 找到 ${users.length} 个`)
    
    // 调试：如果没有找到用户，进一步检查
    if (users.length === 0) {
      console.warn(`部门 ${deptId} 下没有用户，检查部门-用户映射是否正确构建`)
      
      // 手动查找用户
      const matchingUsers = userList.value.filter(u => String(u.deptId) === String(deptId))
      console.log(`手动查找部门 ${deptId} 的用户，找到 ${matchingUsers.length} 个`)
      
      if (matchingUsers.length > 0) {
        console.warn('部门-用户映射可能有问题，手动找到了匹配的用户')
        // 更新映射
        deptUserMap.value.set(deptId, matchingUsers)
        users = matchingUsers
      }
    }
  } else {
    // 否则显示所有用户
    users = userList.value
    console.log('未选择部门，显示所有用户, 数量:', users.length)
  }
  
  // 应用搜索过滤
  if (userSearchKeyword.value) {
    const beforeFilter = users.length
    users = users.filter(user => 
      user.nickname.includes(userSearchKeyword.value)
    )
    console.log(`应用用户搜索过滤，关键词: "${userSearchKeyword.value}", 过滤前: ${beforeFilter}, 过滤后: ${users.length}`)
  }
  
  console.log('最终筛选结果，用户数量:', users.length)
  return users
})

// 更新复选框状态
const updateCheckboxState = () => {
  const users = filteredUsers.value
  if (users.length === 0) {
    selectAllUsers.value = false
    isIndeterminate.value = false
    return
  }
  
  // 计算当前显示的用户中有多少被选中
  const selectedCount = users.filter(user => 
    selectedUserIds.value.some(id => idEquals(id, user.id))
  ).length
  
  selectAllUsers.value = selectedCount === users.length
  isIndeterminate.value = selectedCount > 0 && selectedCount < users.length
}

// 处理全选/取消全选
const handleSelectAllChange = (val: boolean) => {
  const users = filteredUsers.value
  
  if (val) {
    // 全选：将当前显示的所有用户添加到选中列表
    users.forEach(user => {
      if (!selectedUserIds.value.some(id => idEquals(id, user.id))) {
        selectedUserIds.value.push(user.id)
      }
    })
  } else {
    // 取消全选：从选中列表中移除当前显示的所有用户
    selectedUserIds.value = selectedUserIds.value.filter(id => 
      !users.some(user => idEquals(user.id, id))
    )
  }
  
  isIndeterminate.value = false
  updateFinalSelectedUsers()
}

// 处理用户选择变更
const handleUserSelectionChange = () => {
  updateCheckboxState()
  updateFinalSelectedUsers()
}

// 判断两个ID是否相等
const idEquals = (id1: any, id2: any): boolean => {
  return String(id1) === String(id2)
}

// 处理审批方式变化
const handleApproveMethodChange = (val: ApproveMethodType) => {
  approveMethod.value = val
  emit('approveMethodChange', val)
}

// 更新最终选择的用户列表
const updateFinalSelectedUsers = () => {
  const users: UserType[] = []
  const userSet = new Set<string>()
  
  selectedUserIds.value.forEach(userId => {
    const user = userList.value.find(u => idEquals(u.id, userId))
    if (user && !userSet.has(String(userId))) {
      userSet.add(String(userId))
      users.push(user)
    }
  })
  
  finalSelectedUsers.value = users
  
  // 如果只有一个用户，自动设置为依次审批
  if (users.length <= 1) {
    approveMethod.value = ApproveMethodType.SEQUENTIAL_APPROVE
  }
  
  // 更新绑定值
  const userIds = users.map(user => user.id)
  emit('update:modelValue', userIds)
  emit('change', userIds, approveMethod.value)
}

// 移除选定的用户
const removeUser = (userId: number | string) => {
  selectedUserIds.value = selectedUserIds.value.filter(id => !idEquals(id, userId))
  updateCheckboxState()
  updateFinalSelectedUsers()
}

// 递归从对象中查找用户列表
const findUsersList = (obj: any): any[] => {
  // 如果是数组，且首个元素看起来像用户（有id和nickname），则返回该数组
  if (Array.isArray(obj) && obj.length > 0 && obj[0] && obj[0].id && obj[0].nickname) {
    return obj
  }
  
  // 如果是对象，递归检查每个属性
  if (obj && typeof obj === 'object') {
    // 首先检查常见的属性名
    const commonKeys = ['list', 'data', 'result', 'items', 'records', 'rows', 'content']
    for (const key of commonKeys) {
      if (obj[key]) {
        const result = findUsersList(obj[key])
        if (result.length > 0) {
          return result
        }
      }
    }
    
    // 检查所有其他属性
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && obj[key]) {
        const result = findUsersList(obj[key])
        if (result.length > 0) {
          return result
        }
      }
    }
  }
  
  return []
}

// 加载用户列表
const loadUserList = async () => {
  try {
    console.log('开始加载用户列表')
    
    // 尝试使用简单用户列表，减少不必要的数据加载
    console.log('使用简单用户列表接口')
    const simpleUsers = await UserApi.getSimpleUserList()
    console.log(`获取到简单用户列表，数量:`, simpleUsers.length)
    
    if (simpleUsers.length === 0) {
      console.warn('简单用户列表为空，可能是API问题')
      return []
    }
    
    // 处理用户数据，确保有正确的部门信息
    console.log('处理用户与部门的关联信息')
    const usersWithDetails: UserType[] = []
    
    // 收集所有用户的部门ID
    const deptIds = new Set<string | number>()
    simpleUsers.forEach(user => {
      if (user.deptId) deptIds.add(user.deptId)
    })
    
    console.log(`用户关联了 ${deptIds.size} 个不同部门`)
    
    // 获取所有部门信息
    const deptList = await DeptApi.getSimpleDeptList()
    console.log(`获取到 ${deptList.length} 个部门信息`)
    
    // 创建部门映射，方便查询
    const deptMap = new Map<string | number, any>()
    deptList.forEach(dept => {
      if (dept.id) deptMap.set(dept.id, dept)
    })
    
    // 为用户添加部门信息
    simpleUsers.forEach(user => {
      const userDeptId = user.deptId
      const dept = userDeptId ? deptMap.get(userDeptId as string | number) : null
      usersWithDetails.push({
        ...user,
        deptName: dept?.name || '',
        deptId: userDeptId
      })
      
      if (dept) {
        console.log(`用户 ${user.nickname} (ID: ${user.id}) 的部门信息:`, 
                   `部门ID: ${dept.id}, 部门名称: ${dept.name}`)
      } else if (userDeptId) {
        console.warn(`未找到用户 ${user.nickname} (ID: ${user.id}) 的部门信息，部门ID: ${userDeptId}`)
      }
    })
    
    console.log('用户处理完成，共有用户:', usersWithDetails.length)
    userList.value = usersWithDetails
    
    // 构建部门-用户映射
    console.log('构建部门-用户映射')
    const deptMap2 = new Map<number | string, UserType[]>()
    
    // 遍历用户，构建部门-用户映射
    usersWithDetails.forEach(user => {
      if (user.deptId) {
        if (!deptMap2.has(user.deptId)) {
          deptMap2.set(user.deptId, [])
        }
        
        // 确保用户被添加到对应部门的列表中
        const deptUsers = deptMap2.get(user.deptId)
        if (deptUsers) {
          deptUsers.push(user)
        }
      } else {
        console.warn(`用户 ${user.nickname} (ID: ${user.id}) 没有部门ID，无法添加到部门-用户映射`)
      }
    })
    
    // 更新映射
    deptUserMap.value = deptMap2
    console.log(`部门-用户映射构建完成，共有 ${deptMap2.size} 个部门`)
    
    return usersWithDetails
  } catch (error) {
    console.error('加载用户列表失败:', error)
    return []
  }
}

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && Array.isArray(newVal)) {
      // 更新选中的用户ID列表
      selectedUserIds.value = [...newVal]
      updateCheckboxState()
      updateFinalSelectedUsers()
    } else {
      // 处理空值的情况
      selectedUserIds.value = []
      finalSelectedUsers.value = []
      selectAllUsers.value = false
      isIndeterminate.value = false
    }
  }
)

// 监听 userSearchKeyword 变化
watch(userSearchKeyword, () => {
  nextTick(() => {
    updateCheckboxState()
  })
})

// 监听 defaultApproveMethod 变化
watch(
  () => props.defaultApproveMethod,
  (newVal) => {
    if (newVal) {
      approveMethod.value = newVal
    }
  }
)

// 直接修复部门-用户映射
const fixDeptUserMapping = () => {
  console.log('开始修复部门-用户映射')
  
  // 检查用户列表中的每个用户，确保他们都被添加到对应部门
  let fixCount = 0
  
  userList.value.forEach(user => {
    if (user.deptId) {
      // 检查用户是否已在部门映射中
      const deptUsers = deptUserMap.value.get(user.deptId)
      
      if (!deptUsers) {
        // 部门不存在，创建新数组
        deptUserMap.value.set(user.deptId, [user])
        fixCount++
        console.log(`修复: 为部门 ${user.deptId} 创建新的用户列表，添加用户 ${user.nickname}`)
      } else {
        // 部门存在，检查用户是否存在
        const userExists = deptUsers.some(u => idEquals(u.id, user.id))
        
        if (!userExists) {
          deptUsers.push(user)
          fixCount++
          console.log(`修复: 将用户 ${user.nickname} 添加到已存在的部门 ${user.deptId}`)
        }
      }
    }
  })
  
  console.log(`部门-用户映射修复完成，修复了 ${fixCount} 个映射关系`)
  
  // 打印修复后的映射情况
  console.log('修复后的部门-用户映射:')
  deptUserMap.value.forEach((users, deptId) => {
    console.log(`部门ID ${deptId} 下有 ${users.length} 个用户:`, 
               users.map(u => `${u.nickname} (ID: ${u.id})`).join(', '))
  })
}

// 初始化
onMounted(async () => {
  try {
    console.log('============ 组件初始化开始 ============')
    
    // 先加载部门树，因为我们需要部门信息来加载用户
    console.log('第一步：加载部门树')
    const depts = await loadDeptTree()
    console.log(`加载了 ${depts.length} 个顶级部门`)
    
    // 然后加载所有用户
    console.log('第二步：加载所有用户信息')
    const users = await loadUserList()
    console.log(`加载了 ${users.length} 个用户`)
    
    // 检查部门-用户映射
    console.log('第三步：验证部门-用户映射')
    console.log(`部门-用户映射中有 ${deptUserMap.value.size} 个部门`)
    
    // 检查部门-用户映射是否正确
    let totalMappedUsers = 0
    deptUserMap.value.forEach((users, deptId) => {
      console.log(`部门ID ${deptId} 下有 ${users.length} 个用户`)
      totalMappedUsers += users.length
    })
    console.log(`部门-用户映射中共有 ${totalMappedUsers} 个用户`)
    
    // 修复部门-用户映射
    console.log('第四步：修复部门-用户映射')
    fixDeptUserMapping()
    
    // 初始化选择的用户
    console.log('第五步：初始化选中的用户')
    if (props.modelValue && Array.isArray(props.modelValue) && props.modelValue.length > 0) {
      selectedUserIds.value = [...props.modelValue]
      updateCheckboxState()
      updateFinalSelectedUsers()
      console.log(`初始化选中了 ${finalSelectedUsers.value.length} 个用户`)
    }
    
    // 触发一次用户列表计算
    console.log('第六步：触发初始用户列表计算')
    updateFilteredUsersList()
    
    console.log('============ 组件初始化完成 ============')
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
})

// 获取当前选择的用户和审批方式
const getCurrentSelection = () => {
  return {
    userIds: finalSelectedUsers.value.map(user => user.id),
    users: finalSelectedUsers.value,
    approveMethod: approveMethod.value
  }
}

// 暴露方法供父组件调用
defineExpose({
  getCurrentSelection
})
</script>

<style lang="scss" scoped>
.user-select-container {
  width: 100%;
}

.user-select-layout {
  display: flex;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  height: 350px;
  overflow: hidden;
}

.department-tree-container,
.user-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.department-tree-container {
  width: 35%;
  border-right: 1px solid #dcdfe6;
}

.user-list-container {
  width: 65%;
}

.panel-header {
  padding: 10px 15px;
  font-weight: bold;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.dept-search-input,
.user-search-input {
  margin: 10px;
}

.dept-tree {
  flex: 1;
  overflow: auto;
  padding: 0 5px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  
  .user-count {
    font-size: 12px;
    color: #909399;
  }
}

.user-list {
  flex: 1;
  overflow: auto;
  padding: 10px;
}

.select-all-checkbox {
  margin-bottom: 10px;
  font-weight: bold;
}

.user-item {
  margin-bottom: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  
  .user-name {
    font-weight: 500;
  }
  
  .user-dept {
    margin-left: 5px;
    font-size: 12px;
    color: #909399;
  }
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.selected-users-container {
  margin-top: 16px;
  border-top: 1px solid #eee;
  padding-top: 12px;
}

.selected-users-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.selected-users-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.user-tag {
  display: flex;
  align-items: center;
  
  .user-tag-info {
    margin-left: 4px;
    font-size: 12px;
    opacity: 0.8;
  }
}

.tree-actions {
  display: flex;
  justify-content: flex-end;
  padding: 0 10px;
  margin-bottom: 5px;
}

.approve-method-container {
  margin-top: 16px;
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fafafa;
}

.approve-method-title {
  font-weight: bold;
  margin-bottom: 12px;
  color: #303133;
}

.approve-method-desc {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  padding-left: 24px;
  display: flex;
  align-items: flex-start;
  
  .el-icon {
    margin-right: 4px;
    font-size: 14px;
    color: #409EFF;
    margin-top: 1px;
  }
}
</style> 