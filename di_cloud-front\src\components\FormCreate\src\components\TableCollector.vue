<!-- 表格数据收集组件 -->
<template>
  <div class="table-collector">
    <div class="table-toolbar" v-if="props.showToolbar">
      <el-button-group>
        <el-button 
          type="primary" 
          @click="exportData" 
          :disabled="tableData.length === 0"
        >
          <el-icon><Download /></el-icon> 导出Excel/CSV
        </el-button>
        <el-button 
          v-if="!isReadonly"
          type="success" 
          @click="handleImport"
        >
          <el-icon><Upload /></el-icon> 导入Excel/CSV
        </el-button>
      </el-button-group>
      <input
        ref="fileInput"
        type="file"
        accept=".xlsx,.xls,.csv"
        style="display: none"
        @change="importFile"
      />
    </div>

    <el-table :data="tableData" border style="width: 100%">
      <!-- 序号列 -->
      <el-table-column
        type="index"
        label="序号"
        width="60"
        align="center"
        :index="1"
      />
      
      <!-- 动态生成表格列 -->
      <el-table-column
        v-for="(column, index) in safeColumns"
        :key="index"
        :prop="column.prop"
        :label="column.required ? `${column.label} *` : column.label"
        :width="getColumnWidth(column)"
      >
        <template #default="scope">
          <!-- 只读模式下直接显示文本 -->
          <template v-if="isReadonly">
            <span>{{ scope.row[column.prop] }}</span>
          </template>
          
          <!-- 编辑模式下显示控件 -->
          <template v-else>
            <!-- 根据列类型渲染不同的输入控件 -->
            <el-input 
              v-if="!column.type || column.type === 'input'" 
              v-model="scope.row[column.prop]" 
              :placeholder="column.placeholder || '请输入'"
              @change="(val) => handleChange(val, column, scope.row)"
              :maxlength="column.maxLength || (column.prop === 'phone' ? 11 : undefined)"
              :show-word-limit="!!column.maxLength || column.prop === 'phone'"
            />
            <el-select 
              v-else-if="column.type === 'select'" 
              v-model="scope.row[column.prop]" 
              :placeholder="column.placeholder || '请选择'"
              style="width: 100%"
              @change="(val) => handleChange(val, column, scope.row)"
            >
              <el-option 
                v-for="(option, optionIndex) in getSafeOptions(column.options)"
                :key="optionIndex" 
                :label="option.label" 
                :value="option.value" 
              />
            </el-select>
            <el-date-picker 
              v-else-if="column.type === 'date'" 
              v-model="scope.row[column.prop]" 
              type="date" 
              :placeholder="column.placeholder || '请选择日期'"
              style="width: 100%"
              @change="(val) => handleChange(val, column, scope.row)"
            />
            <el-input-number 
              v-else-if="column.type === 'number'" 
              v-model="scope.row[column.prop]" 
              :placeholder="column.placeholder || '请输入数字'"
              @change="(val) => handleChange(val, column, scope.row)"
            />
            <span v-else>{{ scope.row[column.prop] }}</span>
          </template>
        </template>
      </el-table-column>
      
      <!-- 操作列 -->
      <el-table-column label="操作" width="120" v-if="showOperation && !isReadonly">
        <template #default="scope">
          <el-button
            type="danger"
            size="small"
            @click="removeRow(scope.$index)"
            :disabled="isDeleteDisabled(scope.$index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 添加行按钮 -->
    <div class="add-row-button" v-if="showAddButton && !isReadonly">
      <el-button 
        type="primary" 
        @click="addRow" 
        :disabled="isAddDisabled()"
      >
        <el-icon><Plus /></el-icon> 添加参会人员
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch, onMounted, nextTick } from 'vue'
import { Plus, Download, Upload } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 动态导入xlsx，确保在需要时才加载
// 如果组件报错，请先安装xlsx库: npm install xlsx

defineOptions({ name: 'TableCollector' })

// 接收父组件参数
interface Column {
  prop: string
  label: string
  type: 'input' | 'select' | 'date' | 'number' | 'text'
  width?: string
  placeholder?: string
  options?: { label: string; value: any }[]
  defaultValue?: any
  field?: string // 用于自定义字段名
  required?: boolean // 是否必填
  maxLength?: number // 最大长度
  regExp?: string // 验证正则表达式
  regExpMessage?: string // 验证失败提示消息
}

interface Props {
  columns: Column[]
  value?: any[]
  minRows?: number
  maxRows?: number
  showOperation?: boolean
  showAddButton?: boolean
  showToolbar?: boolean // 是否显示导入导出工具栏
  processFormData?: boolean // 是否处理formVariables中的数据
  formCreateInject?: any
  fieldName?: string // 后端字段名称
  readonly?: boolean // 是否为只读模式
}

const props = withDefaults(defineProps<Props>(), {
  value: () => [],
  minRows: 1,
  maxRows: undefined,
  showOperation: true,
  showAddButton: true,
  showToolbar: true, // 默认显示工具栏
  processFormData: true, // 默认处理formVariables中的数据
  fieldName: '', // 默认为空，使用原始字段名
  readonly: false // 默认不是只读模式
})

// 添加formCreate所需的emit
const emit = defineEmits([
  'update:modelValue', 
  'change',
  // formCreate事件
  'fc.updateValue',
  'fc.el',
  'update'
])

// 表格数据
const tableData = ref<any[]>([])

// 安全的列定义，确保始终是数组
const safeColumns = computed(() => {
  return Array.isArray(props.columns) ? props.columns : []
})

// 获取列宽度的方法，为手机号列提供更宽的默认宽度
const getColumnWidth = (column: Column) => {
  // 如果自定义了宽度，使用自定义宽度
  if (column.width) {
    return column.width;
  }
  
  // 为手机号列提供更宽的默认值
  if (column.prop === 'phone' || column.prop === 'attendeePhone') {
    return '160px'; // 手机号列默认宽度
  }
  
  // 其他列使用默认宽度
  return undefined;
}

// 确保选项是数组的辅助函数
const getSafeOptions = (options) => {
  if (!options) return []
  if (Array.isArray(options)) return options
  if (typeof options === 'object') {
    try {
      // 如果是对象，尝试转换为数组
      return Object.keys(options).map(key => ({
        label: options[key],
        value: key
      }))
    } catch (e) {
      console.warn('TableCollector: Failed to convert options object to array', e)
      return []
    }
  }
  return []
}

// 创建空行数据
const createEmptyRow = () => {
  const row = {}
  if (Array.isArray(props.columns)) {
    props.columns.forEach(column => {
      if (column.prop) {
        // 设置默认值
        row[column.prop] = column.defaultValue !== undefined ? column.defaultValue : ''
      }
    })
  }
  return row
}

// 添加行
const addRow = () => {
  // 检查是否已达到最大行数
  if (props.maxRows !== undefined && tableData.value.length >= props.maxRows) {
    console.warn('TableCollector: 参会人员数量已达到最大限制', props.maxRows)
    ElMessage.warning(`参会人员数量已达到最大限制(${props.maxRows}人)`)
    return
  }
  
  try {
    // 创建并添加空行
    const newRow = createEmptyRow()
    console.warn('TableCollector addRow: 添加新行', newRow);
    
    // 使用push方法而不是展开运算符
    tableData.value.push(newRow)
    
    // 确保发送变更事件
    emitChange();
    
    // 由于可能存在异步处理，延迟确认一次数据已更新
    setTimeout(() => {
      console.warn('TableCollector addRow: 延迟检查表格数据', tableData.value);
      if (tableData.value && tableData.value.length > 0) {
        emitChange();
      }
    }, 100);
  } catch (e) {
    console.error('TableCollector: 添加参会人员失败', e)
    ElMessage.error('添加参会人员失败')
  }
}

// 删除行
const removeRow = (index: number) => {
  // 检查是否已达到最小行数
  if (props.minRows !== undefined && tableData.value.length <= props.minRows) {
    console.warn('TableCollector: 参会人员数量已达到最小限制', props.minRows)
    ElMessage.warning(`参会人员数量不能少于${props.minRows}人`)
    return
  }
  
  try {
    // 检查索引是否有效
    if (index >= 0 && index < tableData.value.length) {
      // 使用splice方法删除指定行
      tableData.value.splice(index, 1)
      emitChange()
    } else {
      console.warn('TableCollector: 无效的参会人员索引', index)
    }
  } catch (e) {
    console.error('TableCollector: 删除参会人员失败', e)
    ElMessage.error('删除参会人员失败')
  }
}

// 用于内部存储自定义字段名
const customFieldName = ref(props.fieldName || '')

// 全局异常处理函数，用于诊断问题
const printValueRecursively = (value: any, path = '') => {
  try {
    if (!value || typeof value !== 'object') {
      console.debug(`TableCollector Debug(${path}): 不是对象或为空`, value);
      return;
    }

    console.debug(`TableCollector Debug(${path}): 开始检查对象`, JSON.stringify(value).substring(0, 1000));
    
    // 检查是否是数组
    if (Array.isArray(value)) {
      console.debug(`TableCollector Debug(${path}): 是数组，长度=${value.length}`);
      if (value.length > 0 && typeof value[0] === 'object') {
        console.debug(`TableCollector Debug(${path}[0]): 第一个元素是对象`, value[0]);
        
        // 检查关键属性
        if (value[0].attendeeName !== undefined || value[0].name !== undefined) {
          console.debug(`TableCollector Debug(${path}): 疑似参会人员数组`, value);
        }
      }
      return;
    }
    
    // 检查formVariables路径
    if (value.formVariables) {
      console.debug(`TableCollector Debug(${path}.formVariables): 存在formVariables`, value.formVariables);
      printValueRecursively(value.formVariables, `${path}.formVariables`);
    }
    
    // 检查attendees路径
    if (value.attendees) {
      console.debug(`TableCollector Debug(${path}.attendees): 存在attendees`, value.attendees);
      printValueRecursively(value.attendees, `${path}.attendees`);
    }
    
    // 尝试分析其他所有属性
    for (const key in value) {
      if (typeof value[key] === 'object' && value[key] !== null) {
        printValueRecursively(value[key], path ? `${path}.${key}` : key);
      }
    }
  } catch (e) {
    console.error(`TableCollector Debug Error(${path}):`, e);
  }
};

// 添加辅助函数进行安全访问，避免TypeScript类型错误
const hasProperty = (obj: any, key: string): boolean => {
  if (!obj || typeof obj !== 'object') return false;
  return key in obj;
};

// 添加辅助函数检查和调整列定义
const checkAndAdjustColumns = () => {
  if (!Array.isArray(props.columns) || props.columns.length === 0) {
    console.warn('TableCollector: 未提供有效的列定义');
    return;
  }
  
  // 检查表格数据
  if (tableData.value.length === 0) {
    console.warn('TableCollector: 当前没有数据，无法调整列定义');
    return;
  }
  
  // 获取第一行数据，用于检查字段
  const firstRow = tableData.value[0];
  if (!firstRow || typeof firstRow !== 'object') {
    console.warn('TableCollector: 数据格式不正确');
    return;
  }
  
  // 记录数据中的字段
  const dataFields = Object.keys(firstRow);
  console.warn('TableCollector: 数据中的字段:', dataFields);
  
  // 检查列定义中的prop是否存在于数据中
  props.columns.forEach(col => {
    if (col.prop && !dataFields.includes(col.prop)) {
      console.warn(`TableCollector: 列 "${col.label || col.prop}" 的prop "${col.prop}" 在数据中不存在`);
      
      // 尝试查找可能匹配的字段
      const possibleFields = dataFields.filter(field => 
        field.toLowerCase().includes(col.prop.toLowerCase()) || 
        col.prop.toLowerCase().includes(field.toLowerCase())
      );
      
      if (possibleFields.length > 0) {
        console.warn(`TableCollector: "${col.prop}" 可能匹配的字段:`, possibleFields);
      }
    }
  });
};

// 添加列映射辅助函数
const getColumnMappings = () => {
  // 记录列映射关系，用于调试和转换
  const mappings: Record<string, string> = {};
  
  if (Array.isArray(props.columns)) {
    props.columns.forEach(col => {
      if (col.prop) {
        const targetField = col.field || col.prop;
        mappings[col.prop] = targetField;
      }
    });
  }
  
  return mappings;
};

// 添加文件输入引用定义
const fileInput = ref<HTMLInputElement | null>(null);

// 添加自动列映射功能，确保数据字段与表格列匹配
const autoMapColumns = (data: any[]) => {
  // 如果没有数据或列定义，直接返回
  if (!Array.isArray(data) || data.length === 0 || !Array.isArray(props.columns) || props.columns.length === 0) {
    return;
  }
  
  console.warn('TableCollector autoMapColumns: 开始自动映射列...');
  
  // 获取第一行数据，用于映射
  const firstRow = data[0];
  if (!firstRow || typeof firstRow !== 'object') {
    return;
  }
  
  // 获取数据中的字段
  const dataFields = Object.keys(firstRow);
  console.warn('TableCollector autoMapColumns: 数据中的字段:', dataFields);
  
  // 获取表格列中的prop
  const columnProps = props.columns.map(col => col.prop);
  console.warn('TableCollector autoMapColumns: 表格列prop:', columnProps);
  
  // 如果没有明显问题，返回
  const allPropsInData = columnProps.every(prop => dataFields.includes(prop));
  if (allPropsInData) {
    console.warn('TableCollector autoMapColumns: 所有列prop都在数据中存在，无需映射');
    return;
  }
  
  // 创建临时映射表，用于后续转换
  const fieldMapping: Record<string, string> = {};
  
  // 尝试为每个不匹配的列找到可能的映射
  props.columns.forEach(col => {
    if (!col.prop || dataFields.includes(col.prop)) {
      return; // 跳过已匹配的列
    }
    
    // 尝试找到可能匹配的字段（模糊匹配）
    const possibleFields = dataFields.filter(field => {
      // 尝试不同的匹配方法
      
      // 1. 忽略大小写完全匹配
      if (field.toLowerCase() === col.prop.toLowerCase()) {
        return true;
      }
      
      // 2. 包含关系匹配
      if (field.toLowerCase().includes(col.prop.toLowerCase()) || 
          col.prop.toLowerCase().includes(field.toLowerCase())) {
        return true;
      }
      
      // 3. 特殊字段名规则匹配（例如：name <-> attendeeName，phone <-> attendeePhone）
      const specialPattern = col.prop.toLowerCase().replace(/^attendee/, '');
      if (field.toLowerCase().endsWith(specialPattern) || 
          field.toLowerCase().startsWith(specialPattern)) {
        return true;
      }
      
      return false;
    });
    
    // 如果找到了可能的匹配，使用第一个
    if (possibleFields.length === 1) {
      fieldMapping[col.prop] = possibleFields[0];
      console.warn(`TableCollector autoMapColumns: 映射列 "${col.prop}" -> 数据字段 "${possibleFields[0]}"`);
    } else if (possibleFields.length > 1) {
      console.warn(`TableCollector autoMapColumns: 列 "${col.prop}" 有多个可能的匹配:`, possibleFields);
    }
  });
  
  // 如果有映射，应用到表格数据
  if (Object.keys(fieldMapping).length > 0) {
    console.warn('TableCollector autoMapColumns: 应用字段映射:', fieldMapping);
    
    // 转换所有行数据
    tableData.value = data.map(row => {
      const newRow: Record<string, any> = { ...row };
      
      // 应用映射
      Object.entries(fieldMapping).forEach(([colProp, dataField]) => {
        if (dataField in row) {
          newRow[colProp] = row[dataField];
        }
      });
      
      return newRow;
    });
    
    // 记录转换结果
    console.warn('TableCollector autoMapColumns: 数据转换完成，第一行结果:', tableData.value[0]);
    return true; // 返回true表示进行了映射
  }
  
  return false; // 返回false表示没有进行映射
};

// 修改初始化表格数据方法，添加字段映射功能
const initializeData = (sourceValue: any = props.value) => {
  console.warn('TableCollector: 初始化数据开始 ===============================');
  console.warn('TableCollector: 初始化源类型:', typeof sourceValue);
  console.warn('TableCollector: 初始化源:', sourceValue);
  
  // 解析逻辑直接简化为：如果是数组就使用，如果不是数组就尝试简单提取一下
  let dataToUse: any = sourceValue;
  
  // 检查是否直接就是数组
  if (Array.isArray(sourceValue)) {
    console.warn('TableCollector: 初始化源直接是数组，直接使用');
    dataToUse = sourceValue;
  } 
  // 如果是对象，尝试提取数据
  else if (sourceValue && typeof sourceValue === 'object') {
    console.warn('TableCollector: 初始化源是对象，尝试提取数据');
    
    // 通过日志输出sourceValue的结构
    try {
      console.warn('初始化源结构:', JSON.stringify(sourceValue, null, 2).substring(0, 1000) + '...');
    } catch (e) {
      console.warn('初始化源结构太复杂无法序列化', Object.keys(sourceValue));
    }
    
    // 1. 首先检查attendees字段（常见的参会人员数据字段）
    if (sourceValue.attendees) {
      console.warn('初始化源中发现attendees字段');
      
      if (Array.isArray(sourceValue.attendees)) {
        console.warn('TableCollector: 从初始化源.attendees找到数组');
        dataToUse = sourceValue.attendees;
      } else if (
        typeof sourceValue.attendees === 'object' && 
        sourceValue.attendees.attendees && 
        Array.isArray(sourceValue.attendees.attendees)
      ) {
        console.warn('TableCollector: 从初始化源.attendees.attendees找到数组');
        dataToUse = sourceValue.attendees.attendees;
      }
    }
    
    // 2. 如果没找到且有自定义字段名，尝试使用自定义字段名
    if (!Array.isArray(dataToUse) && customFieldName.value && sourceValue[customFieldName.value]) {
      console.warn(`尝试使用自定义字段名 ${customFieldName.value}`);
      
      if (Array.isArray(sourceValue[customFieldName.value])) {
        console.warn(`TableCollector: 从初始化源.${customFieldName.value}找到数组`);
        dataToUse = sourceValue[customFieldName.value];
      } else if (
        typeof sourceValue[customFieldName.value] === 'object' && 
        sourceValue[customFieldName.value][customFieldName.value] && 
        Array.isArray(sourceValue[customFieldName.value][customFieldName.value])
      ) {
        console.warn(`TableCollector: 从初始化源.${customFieldName.value}.${customFieldName.value}找到数组`);
        dataToUse = sourceValue[customFieldName.value][customFieldName.value];
      }
    }
    
    // 3. 如果还没找到数组，尝试一些常见字段
    if (!Array.isArray(dataToUse)) {
      // 常见可能包含参会人员数据的字段
      const possibleFields = ['participants', 'users', 'members', 'people'];
      
      for (const field of possibleFields) {
        if (sourceValue[field]) {
          if (Array.isArray(sourceValue[field])) {
            console.warn(`TableCollector: 从初始化源.${field}找到数组`);
            dataToUse = sourceValue[field];
            break;
          } else if (
            typeof sourceValue[field] === 'object' && 
            sourceValue[field][field] && 
            Array.isArray(sourceValue[field][field])
          ) {
            console.warn(`TableCollector: 从初始化源.${field}.${field}找到数组`);
            dataToUse = sourceValue[field][field];
            break;
          }
        }
      }
    }
    
    // 4. 最后，检查formVariables字段
    if (!Array.isArray(dataToUse) && 'formVariables' in sourceValue && sourceValue.formVariables) {
      console.warn('尝试从formVariables中提取数据');
      
      const formVars: any = sourceValue.formVariables;
      if (formVars && typeof formVars === 'object') {
        // 检查formVariables中是否包含attendees
        if ('attendees' in formVars && formVars.attendees) {
          console.warn('从formVariables.attendees中提取数据');
          
          // 可能是attendees嵌套结构
          if (typeof formVars.attendees === 'object' && 
              'attendees' in formVars.attendees && 
              formVars.attendees.attendees) {
            if (Array.isArray(formVars.attendees.attendees)) {
              console.warn('找到formVariables.attendees.attendees数组');
              dataToUse = formVars.attendees.attendees;
            }
          }
          
          // 可能是attendees直接是数组
          if (!Array.isArray(dataToUse) && Array.isArray(formVars.attendees)) {
            console.warn('找到formVariables.attendees数组');
            dataToUse = formVars.attendees;
          }
        }
        
        // 尝试其他可能的字段名
        if (!Array.isArray(dataToUse)) {
          for (const key in formVars) {
            if (Array.isArray(formVars[key])) {
              // 检查第一个元素是否包含参会人员特征
              const firstItem = formVars[key][0];
              if (firstItem && typeof firstItem === 'object' && 
                  ('name' in firstItem || 'attendeeName' in firstItem || 
                   'phone' in firstItem || 'attendeePhone' in firstItem)) {
                console.warn(`找到formVariables.${key}数组，可能是参会人员数据`);
                dataToUse = formVars[key];
                break;
              }
            }
          }
        }
      }
    }
  }
  
  // 检查最终解析的数据
  if (Array.isArray(dataToUse)) {
    try {
      console.warn('TableCollector: 最终解析到的数组数据:', JSON.stringify(dataToUse));
    } catch (e) {
      console.warn('TableCollector: 最终解析到的数组数据(无法序列化):', dataToUse.length, '条记录');
    }
  } else {
    console.warn('TableCollector: 未能解析到有效的数组数据', dataToUse);
    dataToUse = [];
  }
  
  // 确保是数组
  if (!Array.isArray(dataToUse)) {
    console.warn('TableCollector: 未找到有效的参会人员数组，使用空数组');
    dataToUse = [];
  }
  
  // 在复制数据前尝试字段映射
  const mappingApplied = dataToUse.length > 0 ? autoMapColumns(dataToUse) : false;
  
  // 如果已经应用了映射，不需要进一步处理
  if (mappingApplied) {
    console.warn('TableCollector: 已应用字段映射，跳过默认处理');
    
    // 如果解析后没有数据但需要默认行
    if (tableData.value.length === 0 && props.minRows && props.minRows > 0) {
      for (let i = 0; i < props.minRows; i++) {
        tableData.value.push(createEmptyRow());
      }
    }
  } else {
    // 如果没有应用映射，使用默认处理
    // 确保每个元素是对象
    tableData.value = dataToUse.map(item => {
      if (typeof item === 'object' && item !== null) {
        return { ...item };
      }
      return createEmptyRow();
    });
    
    // 确保每行都有所有必要的列属性
    tableData.value = tableData.value.map(row => {
      const newRow = { ...row };
      if (Array.isArray(props.columns)) {
        props.columns.forEach(col => {
          if (col.prop && !(col.prop in newRow)) {
            newRow[col.prop] = col.defaultValue !== undefined ? col.defaultValue : '';
          }
        });
      }
      return newRow;
    });
    
    // 如果解析后没有数据但需要默认行
    if (tableData.value.length === 0 && props.minRows && props.minRows > 0) {
      for (let i = 0; i < props.minRows; i++) {
        tableData.value.push(createEmptyRow());
      }
    }
  }
  
  // 通知上层组件数据已更新
  emitChange();
  
  console.warn('TableCollector: 初始化数据完成 ===============================');
}

// 修改formatDataForBackend函数中有问题的代码部分
const formatDataForBackend = () => {
  try {
    console.warn('TableCollector formatDataForBackend: 开始格式化数据...');
    console.warn('TableCollector formatDataForBackend: 当前数据:', tableData.value);
    console.warn('TableCollector formatDataForBackend: 自定义字段名:', customFieldName.value);
    
    // 确保tableData是数组
    if (!Array.isArray(tableData.value)) {
      console.warn('TableCollector formatDataForBackend: tableData不是数组，返回空数组');
      return [];
    }
    
    // 基本格式化，复制数据
    let formattedData = tableData.value.filter(row => {
      // 过滤掉完全空行
      return Object.values(row).some(value => value !== '' && value !== null && value !== undefined);
    }).map(row => {
      // 创建新对象，避免引用原对象
      return { ...row };
    });
    
    // 输出格式化后的数据
    console.warn('TableCollector formatDataForBackend: 基本格式化后:', formattedData);
    
    // 获取props中可能的配置信息
    const customField = customFieldName.value; // 使用新变量名，避免可能的命名冲突
    let submitFormat = null;
    
    if (props.formCreateInject?.rule?.props) {
      const ruleProps = props.formCreateInject.rule.props;
      
      // 检查是否有processFormData配置
      if ('processFormData' in ruleProps && typeof ruleProps.processFormData === 'boolean') {
        console.warn('TableCollector formatDataForBackend: 检测到processFormData配置:', ruleProps.processFormData);
      }
      
      // 检查是否有submitFormat配置
      if ('submitFormat' in ruleProps && typeof ruleProps.submitFormat === 'string') {
        submitFormat = ruleProps.submitFormat;
        console.warn('TableCollector formatDataForBackend: 检测到submitFormat配置:', submitFormat);
      }
    }
    
    // 如果有自定义提交格式，应用相应的格式
    if (submitFormat === 'attendees.attendees') {
      console.warn('TableCollector formatDataForBackend: 使用attendees.attendees提交格式');
      
      // 创建双层嵌套结构
      const result = {
        attendees: {
          attendees: formattedData
        }
      };
      console.warn('TableCollector formatDataForBackend: 最终格式化数据:', result);
      return result;
    } 
    // 如果有自定义字段名，使用自定义字段名
    else if (customField) {
      console.warn(`TableCollector formatDataForBackend: 使用自定义字段名 ${customField}`);
      
      // 创建带自定义字段名的对象（使用动态属性方式）
      const result: Record<string, any> = {};
      result[customField] = formattedData;
      
      console.warn('TableCollector formatDataForBackend: 最终格式化数据:', result);
      return result;
    } 
    // 默认返回数组格式
    else {
      console.warn('TableCollector formatDataForBackend: 使用默认数组格式');
      console.warn('TableCollector formatDataForBackend: 最终格式化数据:', formattedData);
      return formattedData;
    }
  } catch (error) {
    console.error('TableCollector formatDataForBackend: 格式化数据出错', error);
    return [];
  }
};

// 添加验证行数据的函数
const validateRowData = (row: any, showMessage = true) => {
  let isValid = true;
  
  // 遍历所有列定义
  if (Array.isArray(props.columns)) {
    for (const column of props.columns) {
      // 跳过没有属性名的列
      if (!column.prop) continue;
      
      const value = row[column.prop];
      const fieldLabel = column.label || column.prop;
      
      // 必填项验证
      if (column.required && (value === undefined || value === null || value === '')) {
        isValid = false;
        if (showMessage) {
          ElMessage.warning(`${fieldLabel}不能为空`);
        }
        continue; // 一个字段验证失败后继续验证其他字段
      }
      
      // 如果有值且有正则表达式，则进行正则验证
      if (value && column.regExp) {
        try {
          const regExp = new RegExp(column.regExp);
          if (!regExp.test(String(value))) {
            isValid = false;
            if (showMessage) {
              ElMessage.warning(column.regExpMessage || `${fieldLabel}格式不正确`);
            }
          }
        } catch (e) {
          console.error('TableCollector: 正则表达式验证失败', e);
        }
      }
      
      // 针对特定字段类型的验证
      if (value && column.prop === 'phone' && !validatePhone(value)) {
        isValid = false;
        if (showMessage) {
          ElMessage.warning('请输入正确的手机号格式');
        }
      }
    }
  }
  
  return isValid;
}

// 修改handleChange方法添加验证逻辑
const handleChange = (value, column, row) => {
  // 如果修改的是有正则验证的字段，进行验证
  if (column.regExp) {
    try {
      const regExp = new RegExp(column.regExp);
      if (value && !regExp.test(String(value))) {
        ElMessage.warning(column.regExpMessage || `${column.label}格式不正确`);
      }
    } catch (e) {
      console.error('TableCollector: 正则表达式验证失败', e);
    }
  }
  
  // 如果是手机号字段，使用专门的验证函数
  if (column.prop === 'phone' && value && !validatePhone(value)) {
    ElMessage.warning('请输入正确的手机号格式');
  }
  
  // 通知上层组件值已更新
  emitChange();
}

// 修复emitChange方法中的类型问题，并确保新添加的数据可以正确回显
const emitChange = () => {
  try {
    if (!tableData.value) return;
    console.warn('TableCollector emitChange: 发送变更事件, 数据长度:', tableData.value.length);
    
    // 安全克隆数据，避免引用问题
    const safeData = tableData.value.map(row => ({ ...row }));
    console.warn('TableCollector emitChange: 准备发送的数据:', safeData);
    
    // 格式化数据用于后端
    const dataForBackend = formatDataForBackend();
    
    // 检查数据是否丢失
    if (Array.isArray(dataForBackend)) {
      console.warn('TableCollector emitChange: 后端数据是数组格式，长度:', dataForBackend.length);
    } else if (dataForBackend && typeof dataForBackend === 'object') {
      console.warn('TableCollector emitChange: 后端数据是对象格式:', Object.keys(dataForBackend));
    } else {
      console.warn('TableCollector emitChange: 后端数据格式未知:', dataForBackend);
    }
    
    // 检查输入数据和输出数据的字段
    if (safeData.length > 0 && Array.isArray(dataForBackend) && dataForBackend.length > 0) {
      console.warn('TableCollector emitChange: 输入首行数据字段:', Object.keys(safeData[0]));
      console.warn('TableCollector emitChange: 输出首行数据字段:', Object.keys(dataForBackend[0]));
    } else if (safeData.length > 0 && typeof dataForBackend === 'object' && !Array.isArray(dataForBackend)) {
      // 尝试在嵌套对象中找到数组
      const deepArray = findFirstArrayInObject(dataForBackend);
      if (deepArray && deepArray.length > 0) {
        console.warn('TableCollector emitChange: 找到嵌套数组首行数据字段:', Object.keys(deepArray[0]));
      }
    }
    
    // 向FormCreate发送更新事件
    emit('change', dataForBackend);
    emit('update:modelValue', dataForBackend);
    
    // 如果有FormCreate注入，使用API更新值
    if (props.formCreateInject && 
        hasProperty(props.formCreateInject, 'api') && 
        hasProperty(props.formCreateInject.api, 'setValue') && 
        typeof props.formCreateInject.api.setValue === 'function') {
      
      if (hasProperty(props.formCreateInject, 'rule') && 
          hasProperty(props.formCreateInject.rule, 'field')) {
        const field = props.formCreateInject.rule.field;
        if (field) {
          props.formCreateInject.api.setValue(field, dataForBackend);
          console.warn('TableCollector emitChange: 通过FormCreate API设置了值');
        }
      }
    }
  } catch (e) {
    console.error('TableCollector emitChange: 发送变更事件出错', e);
  }
};

// 辅助函数 - 在对象中查找第一个数组
const findFirstArrayInObject = (obj: any): any[] | null => {
  if (!obj || typeof obj !== 'object') return null;
  
  // 直接是数组则返回
  if (Array.isArray(obj)) return obj;
  
  // 递归查找所有属性
  for (const key in obj) {
    if (Array.isArray(obj[key])) {
      return obj[key];
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      const found = findFirstArrayInObject(obj[key]);
      if (found) return found;
    }
  }
  
  return null;
};

// 监听 value 变化并处理数据
watch(
  () => props.value,
  (newVal: any) => {  // 显式指定为any类型，避免TypeScript类型推断错误
    try {
      console.warn('TableCollector watch: 检测到value变化');
      console.warn('新值:', newVal);
      
      // 检查是否需要自动设置为只读模式
      if (newVal && !props.readonly) {
        console.warn('TableCollector: 检测到数据回显，尝试判断是否需要设置只读模式');
        
        // 通过检查FormCreate的配置判断是否为回显场景
        if (props.formCreateInject && 
            hasProperty(props.formCreateInject, 'api') && 
            typeof props.formCreateInject.api.getOption === 'function') {
          
          const option = props.formCreateInject.api.getOption();
          if (option && option.global) {
            const formMode = option.global.formMode;
            console.warn('TableCollector: 当前表单模式:', formMode);
            
            // 详情模式自动设置为只读 - 修改条件，只有明确是回显模式时才设置只读
            if (formMode === 'details' || formMode === 'preview' || formMode === 'readonly' || formMode === 'view') {
              console.warn('TableCollector: 表单处于回显模式，自动启用只读');
              isComponentReadonly.value = true;
            } else {
              // 显式设置非只读状态，确保新表单可编辑
              console.warn('TableCollector: 表单处于编辑模式，禁用只读');
              isComponentReadonly.value = false;
            }
          }
        }
        
        // 如果有明显的回显特征数据，自动设置为只读模式
        if (newVal && typeof newVal === 'object') {
          // 判断是否包含只读标志
          if (hasProperty(newVal, 'readonly') && newVal.readonly === true) {
            console.warn('TableCollector: 数据中包含readonly标志，自动启用只读');
            isComponentReadonly.value = true;
          }
          
          // 判断是否包含viewMode标志
          if (hasProperty(newVal, 'viewMode') && 
              (newVal.viewMode === true || newVal.viewMode === 'detail' || newVal.viewMode === 'view')) {
            console.warn('TableCollector: 数据中包含viewMode标志，自动启用只读');
            isComponentReadonly.value = true;
          }
          
          // 检查常见的回显数据特征
          if (hasProperty(newVal, 'processInstanceId') || 
              hasProperty(newVal, 'instanceId') || 
              hasProperty(newVal, 'detailData')) {
            console.warn('TableCollector: 数据中包含实例ID或详情数据，可能是回显场景');
            // 延迟检查，因为表单状态可能在数据加载后设置
            setTimeout(() => {
              checkReadonlyMode(true);
            }, 500);
          }
        }
      }
      
      // 优先判断常见情况：FormCreate API返回的 attendees 结构
      if (newVal && typeof newVal === 'object' && hasProperty(newVal, 'attendees')) {
        // 解决常见情况：API返回 { attendees: [{ ... }] } 直接使用
        if (Array.isArray(newVal.attendees)) {
          console.warn('发现直接的attendees数组结构，长度:', newVal.attendees.length);
          tableData.value = [...newVal.attendees].map(item => ensureRowHasAllColumns(item));
          emitChange();
          return;
        }
        
        // 解决嵌套情况：API返回 { attendees: { attendees: [{ ... }] } } 使用内部数组
        if (typeof newVal.attendees === 'object' && 
            hasProperty(newVal.attendees, 'attendees') && 
            Array.isArray(newVal.attendees.attendees)) {
          console.warn('发现嵌套的attendees结构，长度:', newVal.attendees.attendees.length);
          tableData.value = [...newVal.attendees.attendees].map(item => ensureRowHasAllColumns(item));
          emitChange();
          return;
        }
      }
      
      // 记录值的类型信息，用于调试
      if (newVal === undefined) {
        console.warn('新值为undefined');
      } else if (newVal === null) {
        console.warn('新值为null');
      } else {
        console.warn('新值类型:', typeof newVal);
        console.warn('新值是否为数组:', Array.isArray(newVal));
        
        if (typeof newVal === 'object' && !Array.isArray(newVal)) {
          const keys = Object.keys(newVal || {});
          console.warn('新值的键:', keys.join(', '));
          
          if (keys.includes('formVariables')) {
            console.warn('检测到formVariables键');
            const formVars: any = newVal.formVariables;  // 显式指定为any类型
            
            if (formVars && typeof formVars === 'object') {
              console.warn('formVariables的键:', Object.keys(formVars).join(', '));
              
              // 检查formVariables中是否包含attendees
              if (Object.keys(formVars).includes('attendees')) {
                const attendees: any = formVars.attendees;  // 显式指定为any类型
                
                if (attendees) {
                  console.warn('发现attendees, 类型:', typeof attendees);
                  
                  // 处理嵌套的attendees结构
                  if (typeof attendees === 'object' && !Array.isArray(attendees) &&
                      Object.keys(attendees).includes('attendees')) {
                    const nestedAttendees: any = attendees.attendees;  // 显式指定为any类型
                    
                    if (Array.isArray(nestedAttendees)) {
                      console.warn('找到嵌套的attendees数组, 长度:', nestedAttendees.length);
                      tableData.value = nestedAttendees.map(item => ensureRowHasAllColumns({...item}));
                      emitChange();
                      return;
                    }
                  }
                  
                  // 处理直接的attendees数组
                  if (Array.isArray(attendees)) {
                    console.warn('找到直接的attendees数组, 长度:', attendees.length);
                    tableData.value = attendees.map(item => ensureRowHasAllColumns({...item}));
                    emitChange();
                    return;
                  }
                }
              }
              
              // 尝试检查formVariables中的所有键，找到可能是参会人员的数组
              for (const key of Object.keys(formVars)) {
                const value: any = formVars[key];  // 显式指定为any类型
                
                if (Array.isArray(value) && value.length > 0) {
                  // 检查数组的第一个元素是否有参会人员相关的字段
                  const firstItem: any = value[0];  // 显式指定为any类型
                  
                  if (firstItem && typeof firstItem === 'object') {
                    const hasAttendeeField = Object.keys(firstItem).some(field => 
                      ['name', 'attendeeName', 'phone', 'attendeePhone', 'unit', 'position']
                        .includes(field));
                    
                    if (hasAttendeeField) {
                      console.warn(`在formVariables.${key}中找到可能的参会人员数组, 长度:`, value.length);
                      tableData.value = value.map(item => ensureRowHasAllColumns({...item}));
                      emitChange();
                      return;
                    }
                  }
                }
              }
            }
          }
        }
        
        // 如果直接是数组，直接使用
        if (Array.isArray(newVal)) {
          console.warn('值直接是数组, 长度:', newVal.length);
          tableData.value = newVal.map(item => ensureRowHasAllColumns({...item}));
          emitChange();
          return;
        }
      }
      
      // 如果到这里还没处理，调用通用的初始化逻辑
      initializeData(newVal);
    } catch (e) {
      console.error('TableCollector: 处理value变化时出错', e);
      // 初始化为空表格
      tableData.value = props.minRows 
        ? Array(props.minRows).fill(null).map(() => createEmptyRow()) 
        : [createEmptyRow()];
      emitChange();
    }
  },
  { deep: true, immediate: false }
)

// 辅助函数：确保一行数据包含所有列
const ensureRowHasAllColumns = (row: any) => {
  const newRow = { ...row };
  if (Array.isArray(props.columns)) {
    props.columns.forEach(col => {
      if (col.prop && !(col.prop in newRow)) {
        newRow[col.prop] = col.defaultValue !== undefined ? col.defaultValue : '';
      }
    });
  }
  return newRow;
}

// 监听 columns 变化
watch(
  () => props.columns,
  (columns) => {
    try {
      if (Array.isArray(columns) && columns.length > 0) {
        // 更新每行数据，确保包含所有列
        tableData.value = tableData.value.map(row => {
          const newRow = { ...row }
          columns.forEach(col => {
            if (col.prop && !(col.prop in newRow)) {
              newRow[col.prop] = col.defaultValue !== undefined ? col.defaultValue : ''
            }
          })
          return newRow
        })
        emitChange()
      }
    } catch (e) {
      console.error('TableCollector: 处理columns变化时出错', e)
    }
  },
  { deep: true }
)

// 监听只读状态变化
watch(
  () => props.readonly,
  (newReadonly) => {
    try {
      console.warn('TableCollector: 检测到readonly属性变化:', newReadonly);
      if (newReadonly === true) {
        // 如果外部设置为只读，直接应用
        isComponentReadonly.value = true;
      } else if (newReadonly === false && isComponentReadonly.value === true) {
        // 如果外部取消只读，但组件内部是只读状态
        // 此时需要检查是否可以取消只读
        
        // 首先检查表单模式
        if (props.formCreateInject && 
            hasProperty(props.formCreateInject, 'api') && 
            typeof props.formCreateInject.api.getOption === 'function') {
          
          const option = props.formCreateInject.api.getOption();
          if (option && option.global) {
            const formMode = option.global.formMode;
            console.warn('TableCollector readonly watch: 当前表单模式:', formMode);
            
            // 如果是明确的只读模式，不允许取消
            if (formMode === 'details' || formMode === 'preview' || formMode === 'readonly') {
              console.warn('TableCollector: 表单处于回显模式，不允许取消只读');
              isComponentReadonly.value = true;
              return;
            }
          }
        }
        
        // 检查URL参数
        try {
          const urlParams = new URLSearchParams(window.location.search);
          const mode = urlParams.get('mode');
          if (mode === 'view' || mode === 'detail' || mode === 'readonly') {
            console.warn('TableCollector: URL参数表明这是只读模式，不允许取消只读');
            isComponentReadonly.value = true;
            return;
          }
        } catch (e) {
          console.warn('TableCollector: 检查URL参数出错', e);
        }
        
        // 如果没有明确限制，允许取消只读
        console.warn('TableCollector: 外部设置取消只读，应用变更');
        isComponentReadonly.value = false;
      }
    } catch (e) {
      console.error('TableCollector: 处理readonly变化时出错', e);
    }
  },
  { immediate: true } // 确保组件初始化时也检查
)

// 删除行按钮禁用状态
const isDeleteDisabled = (index: number) => {
  // 安全地检查minRows是否定义
  const minRowsNum = Number(props.minRows)
  return !isNaN(minRowsNum) && minRowsNum > 0 && tableData.value.length <= minRowsNum
}

// 添加行按钮禁用状态
const isAddDisabled = () => {
  // 安全地检查maxRows是否定义
  const maxRowsNum = Number(props.maxRows)
  return !isNaN(maxRowsNum) && maxRowsNum > 0 && tableData.value.length >= maxRowsNum
}

// 添加一个公共方法，用于对整个表格数据进行验证
const validateAllData = () => {
  // 确保数据是一个有效的数组
  const safeData = Array.isArray(tableData.value) ? tableData.value : [];
  
  // 如果数据为空且设置了最小行数
  if (safeData.length === 0) {
    if (props.minRows && props.minRows > 0) {
      ElMessage.warning(`请至少添加${props.minRows}条参会人员数据`);
      return false;
    }
  }
  
  // 验证每行数据
  let isValid = true;
  for (let i = 0; i < safeData.length; i++) {
    // 验证并显示消息，index+1作为行号显示
    if (!validateRowData(safeData[i], true)) {
      ElMessage.warning(`第${i + 1}行数据有误，请检查`);
      isValid = false;
      break;
    }
  }
  
  return isValid;
}

// 修改导出数据函数，确保正确使用xlsx库
const exportData = () => {
  try {
    if (tableData.value.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    console.log('开始导出数据，尝试使用xlsx库...')
    
    // 直接导入xlsx库而不是使用require
    import('xlsx').then(XLSX => {
      console.log('xlsx库导入成功，开始创建Excel文件')
      
      // 构建Excel工作表数据
      const headers = safeColumns.value.map(col => col.label || col.prop)
      const rows = tableData.value.map(row => {
        return safeColumns.value.map(col => {
          return row[col.prop] !== undefined ? row[col.prop] : ''
        })
      })
      
      // 创建一个工作簿
      const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows])
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '参会人员信息')
      
      // 设置列宽
      const wscols = safeColumns.value.map(col => {
        if (col.prop === 'phone' || col.prop === 'attendeePhone') {
          return { wch: 20 } // 手机号列宽
        }
        return { wch: 15 } // 默认列宽
      })
      worksheet['!cols'] = wscols

      console.log('Excel工作表创建完成，准备导出文件')
      
      // 导出Excel文件
      XLSX.writeFile(workbook, '参会人员信息.xlsx')
      ElMessage.success('Excel文件导出成功')
    }).catch(err => {
      console.error('xlsx库导入失败，详细错误:', err)
      
      // 尝试使用全局XLSX对象
      if (typeof window !== 'undefined' && (window as any).XLSX) {
        console.log('尝试使用全局XLSX对象')
        const XLSX = (window as any).XLSX
        
        // 构建Excel工作表数据
        const headers = safeColumns.value.map(col => col.label || col.prop)
        const rows = tableData.value.map(row => {
          return safeColumns.value.map(col => {
            return row[col.prop] !== undefined ? row[col.prop] : ''
          })
        })
        
        // 创建一个工作簿
        const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows])
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, '参会人员信息')
        
        // 设置列宽
        const wscols = safeColumns.value.map(col => {
          if (col.prop === 'phone' || col.prop === 'attendeePhone') {
            return { wch: 20 } // 手机号列宽
          }
          return { wch: 15 } // 默认列宽
        })
        worksheet['!cols'] = wscols
        
        // 导出Excel文件
        XLSX.writeFile(workbook, '参会人员信息.xlsx')
        ElMessage.success('Excel文件导出成功(通过全局对象)')
        return
      }
      
      console.warn('无法使用xlsx库，退回到CSV格式导出', err)
      // 退回到CSV导出
      // 构建CSV内容
      let csvContent = ''
      
      // 添加表头
      const headers = safeColumns.value.map(col => col.label || col.prop)
      csvContent += headers.join(',') + '\n'
      
      // 添加数据行
      tableData.value.forEach(row => {
        const rowValues = safeColumns.value.map(col => {
          // 确保值不含逗号和换行符，否则用引号包裹
          const value = row[col.prop] !== undefined ? String(row[col.prop]).replace(/"/g, '""') : ''
          return value.includes(',') || value.includes('\n') ? `"${value}"` : value
        })
        csvContent += rowValues.join(',') + '\n'
      })
      
      // 创建Blob对象
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      
      // 创建下载链接
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', '参会人员信息.csv')
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      ElMessage.success('CSV文件导出成功 (xlsx库不可用)')
    })
  } catch (e) {
    console.error('TableCollector: 导出数据失败', e)
    ElMessage.error('导出失败')
  }
}

// 修改导入文件函数，兼容xlsx库是否存在
const importFile = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const file = input.files?.[0];
  if (!file) return;
  
  // 检查文件类型
  const fileType = file.name.split('.').pop()?.toLowerCase();
  if (!fileType || !['xlsx', 'xls', 'csv'].includes(fileType)) {
    ElMessage.error('请上传Excel或CSV文件');
    return;
  }
  
  // 确认导入操作
  ElMessageBox.confirm(
    '导入将覆盖当前数据，是否继续？',
    '导入确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    if (['xlsx', 'xls'].includes(fileType || '')) {
      // 使用动态导入替代require
      import('xlsx').then(XLSX => {
        // 使用xlsx库解析Excel文件
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>) => {
          try {
            const data = e.target?.result;
            if (!data) {
              ElMessage.warning('文件内容为空');
              return;
            }
            
            // 解析Excel内容
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
            const rows = XLSX.utils.sheet_to_json(firstSheet, { header: 1 }) as any[][];
            
            if (!Array.isArray(rows) || rows.length < 2) {
              ElMessage.warning('文件内容为空或格式不正确');
              return;
            }
            
            processImportedRows(rows);
          } catch (e) {
            console.error('TableCollector: 解析导入文件失败', e);
            ElMessage.error('解析Excel文件失败');
          }
        };
        reader.readAsArrayBuffer(file);
      }).catch(err => {
        ElMessage.warning('加载xlsx库失败，请使用CSV格式导入');
        console.warn('加载xlsx库失败', err);
        
        // 退回到CSV模式
        if (fileType === 'csv') {
          readCsvFile(file);
        } else {
          ElMessage.error('当前环境无法解析Excel文件，请导出为CSV格式后重试');
        }
      });
    } else if (fileType === 'csv') {
      // CSV文件处理
      readCsvFile(file);
    }
  }).catch(() => {
    // 用户取消导入
  }).finally(() => {
    // 重置文件输入，以便可以重复选择同一文件
    if (fileInput.value) {
      fileInput.value.value = '';
    }
  });
}

// 添加处理CSV文件的函数
const readCsvFile = (file: File) => {
  const reader = new FileReader();
  reader.onload = (e: ProgressEvent<FileReader>) => {
    try {
      const content = e.target?.result as string;
      if (!content) {
        ElMessage.warning('文件内容为空');
        return;
      }
      
      // 解析CSV内容
      const lines = content.toString().split('\n');
      if (lines.length < 2) {
        ElMessage.warning('文件内容为空或格式不正确');
        return;
      }
      
      // 转换为rows格式，与Excel导入保持一致
      const rows = lines.map(line => line.split(',').map(cell => cell.trim()));
      processImportedRows(rows);
    } catch (e) {
      console.error('TableCollector: 解析CSV文件失败', e);
      ElMessage.error('解析CSV文件失败');
    }
  };
  reader.readAsText(file);
}

// 添加处理导入数据的共用函数
const processImportedRows = (rows: any[][]) => {
  if (!Array.isArray(rows) || rows.length < 2) {
    ElMessage.warning('文件内容为空或格式不正确');
    return;
  }
  
  // 解析表头
  const headers = rows[0];
  
  // 将表头与列定义匹配
  const columnMap: Record<number, string> = {};
  safeColumns.value.forEach(col => {
    const index = headers.findIndex(h => {
      // 去除可能的空格，并进行不区分大小写的比较
      const headerName = typeof h === 'string' ? h.trim().toLowerCase() : '';
      const colLabel = (col.label || col.prop).toLowerCase();
      return headerName === colLabel || headerName === col.prop.toLowerCase();
    });
    if (index >= 0) {
      columnMap[index] = col.prop;
    }
  });
  
  // 解析数据行
  const importedData: Record<string, any>[] = [];
  for (let i = 1; i < rows.length; i++) {
    if (!rows[i] || !rows[i].length) continue;
    
    const row = createEmptyRow();
    const rowData = rows[i];
    
    // 填充解析到的值
    Object.keys(columnMap).forEach(indexStr => {
      const index = Number(indexStr);
      const prop = columnMap[index];
      const value = rowData[index];
      if (value !== undefined && value !== null && value !== '') {
        row[prop] = value;
      }
    });
    
    // 只添加非空行
    if (Object.values(row).some(v => v !== '' && v !== null && v !== undefined)) {
      importedData.push(row);
    }
  }
  
  // 更新表格数据
  if (importedData.length > 0) {
    tableData.value = importedData;
    emitChange();
    ElMessage.success(`成功导入${importedData.length}条数据`);
  } else {
    ElMessage.warning('未解析到有效数据');
  }
}

// 添加回handleImport函数，这个函数在之前的修改中被误删了
// 触发文件选择
const handleImport = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

// 添加内部响应式只读状态，可以在组件内部动态控制
const isComponentReadonly = ref(props.readonly);

// 添加设置只读模式的方法 - 在这里定义函数
const setComponentReadonly = (readonly: boolean = true) => {
  console.warn(`TableCollector: 手动${readonly ? '启用' : '禁用'}只读模式`);
  isComponentReadonly.value = readonly;
};

// 计算属性：是否为只读模式（考虑组件属性和内部状态）
const isReadonly = computed(() => {
  return props.readonly || isComponentReadonly.value;
});

// 添加手机号验证函数
const validatePhone = (value: string): boolean => {
  if (!value) return true; // 允许为空
  const phoneRegExp = /^1[3-9]\d{9}$/;
  return phoneRegExp.test(value);
};

// 添加检查只读模式的函数
const checkReadonlyMode = (forceCheck = false) => {
  // 已经是只读模式，无需再检查
  if (isComponentReadonly.value && !forceCheck) {
    console.warn('TableCollector checkReadonlyMode: 已经是只读模式，跳过检查');
    return;
  }
  
  // 如果明确设置了props.readonly，则尊重显式配置
  if (props.readonly === true) {
    console.warn('TableCollector checkReadonlyMode: props.readonly=true，启用只读模式');
    isComponentReadonly.value = true;
    return;
  }
  
  // 检查URL参数
  try {
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get('mode');
    if (mode === 'view' || mode === 'detail' || mode === 'readonly') {
      console.warn('TableCollector checkReadonlyMode: URL参数表明这是只读模式');
      isComponentReadonly.value = true;
      return;
    }
  } catch (e) {
    console.warn('TableCollector checkReadonlyMode: 检查URL参数出错', e);
  }
  
  // 检查FormCreate配置
  if (props.formCreateInject) {
    try {
      if (hasProperty(props.formCreateInject, 'api') && 
          typeof props.formCreateInject.api.getOption === 'function') {
        const option = props.formCreateInject.api.getOption();
        if (option) {
          console.warn('TableCollector checkReadonlyMode: 表单配置:', option);
          
          // 检查全局配置中的模式
          if (option.global) {
            const formMode = option.global.formMode || option.global.mode;
            console.warn('TableCollector checkReadonlyMode: 表单全局模式:', formMode);
            
            if (formMode === 'details' || formMode === 'preview' || formMode === 'readonly' || formMode === 'view') {
              console.warn('TableCollector checkReadonlyMode: 根据全局配置启用只读模式');
              isComponentReadonly.value = true;
              return;
            } else {
              // 如果明确不是回显模式，设置为可编辑
              console.warn('TableCollector checkReadonlyMode: 表单不是回显模式，保持可编辑状态');
              isComponentReadonly.value = false;
              return;
            }
          }
          
          // 检查顶层配置
          const formMode = option.formMode || option.mode;
          if (formMode === 'details' || formMode === 'preview' || formMode === 'readonly' || formMode === 'view') {
            console.warn('TableCollector checkReadonlyMode: 根据顶层配置启用只读模式');
            isComponentReadonly.value = true;
            return;
          } else if (formMode === 'create' || formMode === 'edit' || formMode === 'update') {
            // 明确是编辑模式的情况
            console.warn('TableCollector checkReadonlyMode: 根据顶层配置确认是编辑模式，保持可编辑状态');
            isComponentReadonly.value = false;
            return;
          }
        }
      }
      
      // 检查组件的rule配置
      if (hasProperty(props.formCreateInject, 'rule')) {
        const rule = props.formCreateInject.rule;
        if (rule && rule.props) {
          const ruleProps = rule.props;
          
          // 如果明确设置了readonly或者disabled
          if ((hasProperty(ruleProps, 'readonly') && ruleProps.readonly === true) || 
              (hasProperty(ruleProps, 'disabled') && ruleProps.disabled === true)) {
            console.warn('TableCollector checkReadonlyMode: 规则中明确设置了readonly或disabled属性');
            isComponentReadonly.value = true;
            return;
          }
        }
      }
    } catch (e) {
      console.warn('TableCollector checkReadonlyMode: 检查FormCreate配置出错', e);
    }
  }
  
  // 默认保持可编辑状态，除非明确是只读模式
  console.warn('TableCollector checkReadonlyMode: 未检测到明确的只读模式特征，保持可编辑状态');
  isComponentReadonly.value = false;
};

// 修改onMounted函数，增加只读模式的自动检测
onMounted(() => {
  try {
    console.warn('TableCollector: 开始挂载，formCreateInject:', !!props.formCreateInject);
    console.warn('TableCollector: 当前只读模式状态:', props.readonly);
    
    // 从FormCreate获取自定义字段名和API值
    let apiValue = null;
    
    if (props.formCreateInject) {
      // 从formCreateInject中获取自定义字段名和其他配置
      if (hasProperty(props.formCreateInject, 'rule') && hasProperty(props.formCreateInject.rule, 'props')) {
        console.warn('TableCollector: 从FormCreate规则中获取配置', 
          JSON.stringify(props.formCreateInject.rule.props));
        
        // 安全地设置自定义字段名
        if (hasProperty(props.formCreateInject.rule.props, 'fieldName')) {
          // 使用临时变量获取值
          const propFieldName = props.formCreateInject.rule.props.fieldName;
          
          // 如果是有效的字符串，则设置
          if (typeof propFieldName === 'string' && propFieldName) {
            // 如果customFieldName是一个ref，则应该使用value属性来更新它
            customFieldName.value = propFieldName;
            console.warn('TableCollector: 设置自定义字段名:', customFieldName.value);
          }
        }
        
        // 检查是否需要自动设置为只读模式
        if (hasProperty(props.formCreateInject.rule.props, 'autoReadonly') && 
            props.formCreateInject.rule.props.autoReadonly === true) {
          console.warn('TableCollector: 检测到autoReadonly配置，尝试自动设置只读模式');
          
          // 检查是否可能处于回显场景
          if (props.formCreateInject.api && 
              typeof props.formCreateInject.api.getOption === 'function') {
            
            const option = props.formCreateInject.api.getOption();
            // 检查表单模式
            if (option && option.global) {
              const formMode = option.global.formMode || option.formMode;
              if (formMode === 'details' || formMode === 'preview' || formMode === 'readonly') {
                console.warn(`TableCollector: 检测到表单处于 ${formMode} 模式，自动启用只读模式`);
                isComponentReadonly.value = true;
              }
            }
          }
        }
        
        // 输出当前列定义，用于调试
        if (Array.isArray(props.columns)) {
          console.warn('TableCollector: 当前列定义:', 
            props.columns.map(col => ({ prop: col.prop, label: col.label, field: col.field })));
        }
      }
      
      // 从API中获取当前值
      if (hasProperty(props.formCreateInject, 'api') && 
          hasProperty(props.formCreateInject.api, 'getValue') && 
          typeof props.formCreateInject.api.getValue === 'function') {
        const field = hasProperty(props.formCreateInject, 'rule') ? props.formCreateInject.rule.field : null;
        if (field) {
          apiValue = props.formCreateInject.api.getValue(field);
          console.warn('TableCollector: 从FormCreate API获取当前值:', apiValue);
          
          // 如果有值但组件属性未明确设置为只读，则可能是回显场景
          if (apiValue && !props.readonly) {
            // 根据表单的模式或者其他线索判断是否为回显
            if (props.formCreateInject.api && 
                typeof props.formCreateInject.api.getOption === 'function') {
              const option = props.formCreateInject.api.getOption();
              if (option && option.global && option.global.formMode === 'details') {
                console.warn('TableCollector: 检测到表单处于详情模式，自动启用只读模式');
                isComponentReadonly.value = true;
              }
            }
          }
        }
      }
    }
    
    // 确定初始化源：优先使用props.value，如果为空则尝试使用apiValue
    const initSource = (!Array.isArray(props.value) || props.value.length === 0) && apiValue ? apiValue : props.value;
    console.warn('TableCollector: 使用的初始化源:', initSource);
    
    // 使用选定的初始化源初始化数据
    initializeData(initSource);
    
    // 在初始化数据后检查列定义
    setTimeout(() => {
      checkAndAdjustColumns();
    }, 0);
    
    // 延迟检测表单模式 - 有些表单框架可能会延迟设置模式
    setTimeout(() => {
      console.warn('TableCollector: 延迟检测表单模式');
      checkReadonlyMode();
    }, 300);
    
    // 再次延迟检测 - 处理异步加载的场景
    setTimeout(() => {
      console.warn('TableCollector: 再次检测表单模式');
      checkReadonlyMode(true); // 强制检查
    }, 1000);
    
    // 在DOM渲染后立即检查一次 - 捕获初始渲染状态
    nextTick(() => {
      console.warn('TableCollector: DOM已渲染，检查只读状态');
      // 如果已经有回显数据，可能是只读场景
      if (tableData.value && tableData.value.length > 0) {
        // 如果数据非空但此时没有编辑按钮，可能是详情场景
        checkReadonlyMode(true);
      }
      
      // 尝试查找页面上的一些特征元素
      try {
        // 如果页面中有特定的按钮或文本，判断是否处于只读模式
        const pageContent = document.body.textContent || '';
        if (pageContent.includes('查看详情') || pageContent.includes('流程详情') || 
            pageContent.includes('View Detail') || pageContent.includes('Process Detail')) {
          console.warn('TableCollector nextTick: 页面内容表明这是详情页');
          isComponentReadonly.value = true;
        }
        
        // 检查是否有直接父级或附近有明确的只读标志
        const el = document.querySelector('.table-collector');
        if (el) {
          // 向上查找5个层级的父元素
          let parent = el.parentElement;
          let level = 0;
          while (parent && level < 5) {
            if (parent.classList.contains('readonly') || 
                parent.classList.contains('view-mode') || 
                parent.classList.contains('detail-mode') || 
                parent.getAttribute('readonly') !== null) {
              console.warn('TableCollector nextTick: 父元素含有只读标志');
              isComponentReadonly.value = true;
              break;
            }
            parent = parent.parentElement;
            level++;
          }
        }
      } catch (e) {
        console.warn('TableCollector nextTick: 检查页面元素出错', e);
      }
    });
  } catch (e) {
    console.error('TableCollector: 挂载时出错', e);
    // 出错时创建默认空数据
    tableData.value = [];
    if (props.minRows && props.minRows > 0) {
      for (let i = 0; i < props.minRows; i++) {
        tableData.value.push(createEmptyRow());
      }
    }
    emitChange();
  }
});

// 暴露公共方法
defineExpose({
  validateAllData,
  addRow,
  removeRow,
  tableData,
  setReadonly: setComponentReadonly,  // 使用另一个名称解决命名冲突
  checkReadonlyMode,  // 暴露检测函数，允许外部触发检测
});
</script>

<style scoped>
.table-collector {
  width: 100%;
}
.add-row-button {
  margin-top: 10px;
  text-align: center;
}
.table-toolbar {
  margin-bottom: 10px;
  text-align: right;
}
</style> 