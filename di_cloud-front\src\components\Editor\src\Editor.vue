<script lang="ts" setup>
import { PropType } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { i18nChangeLanguage, IDomEditor, IEditorConfig } from '@wangeditor/editor'
import { Boot } from '@wangeditor/editor'
import attachmentModule from '@wangeditor/plugin-upload-attachment'
import { propTypes } from '@/utils/propTypes'
import { isNumber } from '@/utils/is'
import { ElMessage } from 'element-plus'
import { useLocaleStore } from '@/store/modules/locale'
import { getAccessToken, getTenantId } from '@/utils/auth'
import { getUploadUrl } from '@/components/UploadFile/src/useUpload'

// 注册附件上传插件
Boot.registerModule(attachmentModule)

defineOptions({ name: 'Editor' })

type InsertFnType = (url: string, alt: string, href: string) => void

const localeStore = useLocaleStore()

const currentLocale = computed(() => localeStore.getCurrentLocale)

i18nChangeLanguage(unref(currentLocale).lang)

const props = defineProps({
  editorId: propTypes.string.def('wangeEditor-1'),
  height: propTypes.oneOfType([Number, String]).def('500px'),
  editorConfig: {
    type: Object as PropType<Partial<IEditorConfig>>,
    default: () => undefined
  },
  readonly: propTypes.bool.def(false),
  modelValue: propTypes.string.def(''),
  enableAttachment: propTypes.bool.def(true)
})

const emit = defineEmits(['change', 'update:modelValue'])

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>()

const valueHtml = ref('')

watch(
  () => props.modelValue,
  (val: string) => {
    if (val === unref(valueHtml)) return
    valueHtml.value = val
  },
  {
    immediate: true
  }
)

// 监听
watch(
  () => valueHtml.value,
  (val: string) => {
    emit('update:modelValue', val)
  }
)

const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor
}

// 编辑器配置
const editorConfig = computed((): IEditorConfig => {
  return Object.assign(
    {
      placeholder: '请输入内容...',
      readOnly: props.readonly,
      customAlert: (s: string, t: string) => {
        switch (t) {
          case 'success':
            ElMessage.success(s)
            break
          case 'info':
            ElMessage.info(s)
            break
          case 'warning':
            ElMessage.warning(s)
            break
          case 'error':
            ElMessage.error(s)
            break
          default:
            ElMessage.info(s)
            break
        }
      },
      autoFocus: false,
      scroll: true,
      // 在编辑器中，点击选中"附件"节点时，要弹出的菜单
      hoverbarKeys: {
        attachment: {
          menuKeys: ['downloadAttachment'], // "下载附件"菜单
        },
      },
      MENU_CONF: {
        ['uploadImage']: {
          server: getUploadUrl(),
          // 单个文件的最大体积限制，默认为 2M
          maxFileSize: 5 * 1024 * 1024,
          // 最多可上传几个文件，默认为 100
          maxNumberOfFiles: 10,
          // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
          allowedFileTypes: ['image/*'],

          // 自定义增加 http  header
          headers: {
            Accept: '*',
            Authorization: 'Bearer ' + getAccessToken(),
            'tenant-id': getTenantId()
          },

          // 超时时间，默认为 10 秒
          timeout: 5 * 1000, // 5 秒

          // form-data fieldName，后端接口参数名称，默认值wangeditor-uploaded-image
          fieldName: 'file',

          // 上传之前触发
          onBeforeUpload(file: File) {
            // console.log(file)
            return file
          },
          // 上传进度的回调函数
          onProgress(progress: number) {
            // progress 是 0-100 的数字
            console.log('progress', progress)
          },
          onSuccess(file: File, res: any) {
            console.log('onSuccess', file, res)
          },
          onFailed(file: File, res: any) {
            alert(res.message)
            console.log('onFailed', file, res)
          },
          onError(file: File, err: any, res: any) {
            alert(err.message)
            console.error('onError', file, err, res)
          },
          // 自定义插入图片
          customInsert(res: any, insertFn: InsertFnType) {
            console.log('CustomInsert received response:', res)
            // Extract the first element which is expected to be an object { name: '...', url: '...' }
            const fileInfo = Array.isArray(res.data) && res.data.length > 0 ? res.data[0] : null
            const url = fileInfo && typeof fileInfo === 'object' && typeof fileInfo.url === 'string' ? fileInfo.url : ''
            const name = fileInfo && typeof fileInfo === 'object' && typeof fileInfo.name === 'string' ? fileInfo.name : ''
            console.log('Extracted URL:', url, '(type:', typeof url, ')', 'Name:', name)

            if (url) {
              // Use the extracted name for alt text, or a default. Keep href as empty string.
              const altText = name || 'image'
              insertFn(url, altText, '')
            } else {
              console.error('Image upload successful, but no valid URL string found in response object:', res)
              ElMessage.error('图片上传成功，但无法获取图片地址')
            }
          }
        },
        ['uploadVideo']: {
          server: getUploadUrl(),
          // 单个文件的最大体积限制，默认为 10M
          maxFileSize: 10 * 1024 * 1024,
          // 最多可上传几个文件，默认为 100
          maxNumberOfFiles: 10,
          // 选择文件时的类型限制，默认为 ['video/*'] 。如不想限制，则设置为 []
          allowedFileTypes: ['video/*'],

          // 自定义增加 http  header
          headers: {
            Accept: '*',
            Authorization: 'Bearer ' + getAccessToken(),
            'tenant-id': getTenantId()
          },

          // 超时时间，默认为 30 秒
          timeout: 15 * 1000, // 15 秒

          // form-data fieldName，后端接口参数名称，默认值wangeditor-uploaded-image
          fieldName: 'file',

          // 上传之前触发
          onBeforeUpload(file: File) {
            // console.log(file)
            return file
          },
          // 上传进度的回调函数
          onProgress(progress: number) {
            // progress 是 0-100 的数字
            console.log('progress', progress)
          },
          onSuccess(file: File, res: any) {
            console.log('onSuccess', file, res)
          },
          onFailed(file: File, res: any) {
            alert(res.message)
            console.log('onFailed', file, res)
          },
          onError(file: File, err: any, res: any) {
            alert(err.message)
            console.error('onError', file, err, res)
          },
          // 自定义插入视频
          customInsert(res: any, insertFn: InsertFnType) {
            console.log('CustomInsert received response (video):', res)
            // Extract the first element which is expected to be an object { name: '...', url: '...' }
            const fileInfo = Array.isArray(res.data) && res.data.length > 0 ? res.data[0] : null
            const url = fileInfo && typeof fileInfo === 'object' && typeof fileInfo.url === 'string' ? fileInfo.url : ''
            const name = fileInfo && typeof fileInfo === 'object' && typeof fileInfo.name === 'string' ? fileInfo.name : ''
            console.log('Extracted URL (video):', url, '(type:', typeof url, ')', 'Name:', name)

            if (url) {
              // Use the extracted name for alt text, or a default. Keep href as empty string.
              const altText = name || 'video'
              // Assuming insertFn for video also takes (url, alt, href)
              insertFn(url, altText, '')
            } else {
              console.error('Video upload successful, but no valid URL string found in response object:', res)
              ElMessage.error('视频上传成功，但无法获取视频地址')
            }
          }
        },
        ['uploadAttachment']: {
          server: getUploadUrl(),
          // 单个文件的最大体积限制，默认为 10M
          maxFileSize: 10 * 1024 * 1024,
          // 选择文件时的类型限制，设置为空数组表示不限制文件类型
          allowedFileTypes: [],

          // 自定义增加 http header
          headers: {
            Accept: '*',
            Authorization: 'Bearer ' + getAccessToken(),
            'tenant-id': getTenantId()
          },

          // 超时时间，默认为 10 秒
          timeout: 10 * 1000,

          // form-data fieldName，后端接口参数名称
          fieldName: 'file',

          // 上传之前触发
          onBeforeUpload(file: File) {
            console.log('attachment onBeforeUpload', file)
            return file
          },
          // 上传进度的回调函数
          onProgress(progress: number) {
            console.log('attachment progress', progress)
          },
          onSuccess(file: File, res: any) {
            console.log('attachment onSuccess', file, res)
          },
          onFailed(file: File, res: any) {
            ElMessage.error(res.message || '附件上传失败')
            console.log('attachment onFailed', file, res)
          },
          onError(file: File, err: any, res: any) {
            ElMessage.error(err.message || '附件上传出错')
            console.error('attachment onError', file, err, res)
          },
          // 自定义插入附件
          customInsert(res: any, file: File, insertFn: Function) {
            console.log('CustomInsert received response (attachment):', res)
            // Extract the first element which is expected to be an object { name: '...', url: '...' }
            const fileInfo = Array.isArray(res.data) && res.data.length > 0 ? res.data[0] : null
            const url = fileInfo && typeof fileInfo === 'object' && typeof fileInfo.url === 'string' ? fileInfo.url : ''
            const name = fileInfo && typeof fileInfo === 'object' && typeof fileInfo.name === 'string' ? fileInfo.name : file.name
            console.log('Extracted URL (attachment):', url, 'Name:', name)

            if (url) {
              // 插入附件到编辑器，使用与图片上传相同的URL处理逻辑
              insertFn(name, url)
            } else {
              console.error('Attachment upload successful, but no valid URL string found in response object:', res)
              ElMessage.error('附件上传成功，但无法获取附件地址')
            }
          }
        }
      },
      uploadImgShowBase64: true
    },
    props.editorConfig || {}
  )
})

// 工具栏配置
const toolbarDefaultConfig = computed(() => {
  const defaultConfig: any = {}
  
  // 如果启用附件上传，添加附件上传菜单到工具栏
  if (props.enableAttachment) {
    defaultConfig.insertKeys = {
      index: 5, // 插入到第5个位置
      keys: ['uploadAttachment'] // 添加"上传附件"菜单
    }
  }
  
  return defaultConfig
})

const editorStyle = computed(() => {
  return {
    height: isNumber(props.height) ? `${props.height}px` : props.height
  }
})

// 回调函数
const handleChange = (editor: IDomEditor) => {
  emit('change', editor)
}

// 组件销毁时，及时销毁编辑器
onBeforeUnmount(() => {
  const editor = unref(editorRef.value)

  // 销毁，并移除 editor
  editor?.destroy()
})

const getEditorRef = async (): Promise<IDomEditor> => {
  await nextTick()
  return unref(editorRef.value) as IDomEditor
}

defineExpose({
  getEditorRef
})
</script>

<template>
  <div class="border-1 border-solid border-[var(--tags-view-border-color)] z-10">
    <!-- 工具栏 -->
    <Toolbar
      :editor="editorRef"
      :editorId="editorId"
      :defaultConfig="toolbarDefaultConfig"
      class="border-0 b-b-1 border-solid border-[var(--tags-view-border-color)]"
    />
    <!-- 编辑器 -->
    <Editor
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :editorId="editorId"
      :style="editorStyle"
      @on-change="handleChange"
      @on-created="handleCreated"
    />
  </div>
</template>

<style src="@wangeditor/editor/dist/css/style.css"></style>
