<template>
  <div class="template-download-container">
    <el-button
      :type="buttonTypeValue"
      :size="buttonSizeValue"
      @click="handleDownload"
    >
      <el-icon v-if="showIcon"><Download /></el-icon>
      <span v-if="buttonText && buttonText.trim().length > 0">{{ buttonText }}</span>
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 定义按钮类型的可选值
type ButtonType = 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
// 定义按钮尺寸的可选值
type ButtonSize = 'default' | 'small' | 'large'

defineOptions({ name: 'TemplateDownload' })

const props = defineProps({
  // 绑定值（表单数据对象的字段名）
  modelValue: {
    type: String,
    default: ''
  },
  // 字段名称
  field: {
    type: String,
    default: ''
  },
  // 文件名称
  fileName: {
    type: String,
    default: '模板文件'
  },
  // 文件路径
  filePath: {
    type: String,
    default: ''
  },
  // 按钮文字
  buttonText: {
    type: String,
    default: ''
  },
  // 按钮类型
  buttonType: {
    type: String as () => ButtonType,
    default: 'primary'
  },
  // 按钮大小
  buttonSize: {
    type: String as () => ButtonSize,
    default: 'default'
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 兼容formCreate的注入属性
  formCreateInject: {
    type: Object,
    default: () => ({})
  }
})

// 支持v-model
const emit = defineEmits(['update:modelValue', 'change'])

// 计算属性：按钮类型
const buttonTypeValue = computed<ButtonType>(() => {
  const validTypes: ButtonType[] = ['primary', 'success', 'warning', 'danger', 'info', 'text']
  return validTypes.includes(props.buttonType as ButtonType) 
    ? (props.buttonType as ButtonType) 
    : 'primary'
})

// 计算属性：按钮尺寸
const buttonSizeValue = computed<ButtonSize>(() => {
  const validSizes: ButtonSize[] = ['default', 'small', 'large']
  return validSizes.includes(props.buttonSize as ButtonSize) 
    ? (props.buttonSize as ButtonSize) 
    : 'default'
})

// 处理下载操作
const handleDownload = async () => {
  try {
    if (!props.filePath) {
      ElMessage.warning('未设置文件路径')
      return
    }

    // 构建完整的URL
    const url = buildFullUrl(props.filePath)
    console.log('下载文件URL:', url)
    
    // 发射事件，告知表单组件已经点击下载按钮
    emit('update:modelValue', props.fileName)
    emit('change', props.fileName)
    
    // 使用fetch API获取文件并下载
    ElMessage.info('正在下载文件...')
    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }
    
    // 获取Content-Type
    const contentType = response.headers.get('Content-Type') || getMimeTypeFromUrl(url)
    console.log('文件Content-Type:', contentType)
    
    // 获取文件Blob
    const blob = await response.blob()
    
    // 创建正确MIME类型的Blob
    const fileBlob = new Blob([blob], { type: contentType })
    
    // 确保文件名有正确的扩展名
    const fileName = getFileNameWithExtension(props.fileName, url, contentType)
    console.log('下载文件名:', fileName)
    
    // 创建Blob URL
    const blobUrl = window.URL.createObjectURL(fileBlob)
    
    // 创建一个隐藏的a标签用于下载
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(blobUrl)
    
    ElMessage.success('下载成功')
  } catch (error: any) {
    console.error('下载模板文件失败:', error)
    ElMessage.error(`下载失败: ${error.message || '未知错误'}`)
  }
}

// 从URL获取MIME类型
const getMimeTypeFromUrl = (url: string): string => {
  const extension = url.split('.').pop()?.toLowerCase() || ''
  const mimeTypes: Record<string, string> = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'txt': 'text/plain',
    'csv': 'text/csv',
    'html': 'text/html',
    'htm': 'text/html',
    'json': 'application/json',
    'xml': 'application/xml',
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed'
  }
  
  return mimeTypes[extension] || 'application/octet-stream'
}

// 确保文件名有正确的扩展名
const getFileNameWithExtension = (fileName: string, url: string, contentType: string): string => {
  // 如果文件名已经有扩展名，则使用它
  if (/\.\w+$/.test(fileName)) {
    return fileName
  }
  
  // 从URL获取扩展名
  const urlExtension = url.split('.').pop()?.toLowerCase()
  if (urlExtension && urlExtension.length <= 5) {
    return `${fileName}.${urlExtension}`
  }
  
  // 从Content-Type获取扩展名
  const extensionFromContentType = getExtensionFromMimeType(contentType)
  if (extensionFromContentType) {
    return `${fileName}.${extensionFromContentType}`
  }
  
  // 默认返回原始文件名
  return fileName
}

// 从MIME类型获取扩展名
const getExtensionFromMimeType = (mimeType: string): string | null => {
  const mimeToExt: Record<string, string> = {
    'application/pdf': 'pdf',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'application/vnd.ms-excel': 'xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'application/vnd.ms-powerpoint': 'ppt',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'text/plain': 'txt',
    'text/csv': 'csv',
    'text/html': 'html',
    'application/json': 'json',
    'application/xml': 'xml',
    'application/zip': 'zip',
    'application/x-rar-compressed': 'rar',
    'application/x-7z-compressed': '7z'
  }
  
  return mimeToExt[mimeType] || null
}

// 构建完整的URL
const buildFullUrl = (path: string) => {
  // 如果路径已经是完整URL，则直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }
  
  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  
  // 获取当前域名
  const origin = window.location.origin
  
  // 拼接完整URL
  return `${origin}${normalizedPath}`
}
</script>

<style scoped>
.template-download-container {
  display: inline-block;
}

/* 当按钮没有文字只有图标时的样式优化 */
.template-download-container :deep(.el-button:has(.el-icon:only-child)) {
  padding-left: 12px;
  padding-right: 12px;
}
</style> 