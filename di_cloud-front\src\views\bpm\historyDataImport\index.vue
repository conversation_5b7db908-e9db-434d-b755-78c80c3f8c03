<template>
  <ContentWrap>
    <div class="history-data-import">
      <!-- 页面标题 -->
      <div class="page-header mb-4">
        <h2>历史数据导入</h2>
        <p class="text-gray-600">请选择案件类型并上传对应的Excel文件</p>
      </div>

      <!-- 案件类型选择 -->
      <el-card class="mb-4">
        <template #header>
          <span>案件类型选择</span>
        </template>
        <el-radio-group v-model="selectedCaseType" size="large">
          <el-radio-button label="信访件">信访件</el-radio-button>
          <el-radio-button label="立案件">立案件</el-radio-button>
          <el-radio-button label="党纪立案件">党纪立案件</el-radio-button>
        </el-radio-group>
      </el-card>

      <!-- 文件上传区域 -->
      <el-card class="mb-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span>文件上传</span>
            <el-button 
              type="primary" 
              size="small" 
              @click="downloadTemplate"
              :disabled="!selectedCaseType"
            >
              <Icon icon="ep:download" class="mr-1" />
              下载模板
            </el-button>
          </div>
        </template>
        
        <el-upload
          ref="uploadRef"
          v-model:file-list="fileList"
          :action="uploadAction"
          :auto-upload="false"
          :multiple="true"
          :accept="'.xlsx,.xls'"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :disabled="!selectedCaseType"
          drag
          class="upload-area"
        >
          <Icon icon="ep:upload-filled" size="50" color="#409EFF" />
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              <p>支持 .xlsx、.xls 格式文件</p>
              <p>可以选择单个文件或批量选择多个文件</p>
              <p>请确保文件格式与所选案件类型匹配</p>
            </div>
          </template>
        </el-upload>

        <!-- 文件列表 -->
        <div v-if="fileList.length > 0" class="file-list mt-4">
          <h4>已选择文件 ({{ fileList.length }}个)</h4>
          <el-table :data="fileList" border>
            <el-table-column prop="name" label="文件名" />
            <el-table-column prop="size" label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getFileStatusType(row.status)">
                  {{ getFileStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ $index }">
                <el-button 
                  type="danger" 
                  size="small" 
                  text
                  @click="removeFile($index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 上传按钮 -->
        <div class="upload-actions mt-4" v-if="fileList.length > 0">
          <el-button 
            type="primary" 
            @click="handleUpload"
            :loading="uploading"
            :disabled="!selectedCaseType"
          >
            <Icon icon="ep:upload" class="mr-1" />
            解析文件 ({{ fileList.length }}个)
          </el-button>
          <el-button @click="clearFiles">清空文件</el-button>
        </div>
      </el-card>

      <!-- 数据预览区域 -->
      <el-card v-if="previewData.length > 0">
        <template #header>
          <div class="flex justify-between items-center">
            <span>数据预览 (共{{ previewData.length }}条记录)</span>
            <div>
              <el-button 
                type="success" 
                @click="exportPreviewData"
              >
                <Icon icon="ep:download" class="mr-1" />
                导出预览数据
              </el-button>
            </div>
          </div>
        </template>

        <!-- 数据统计 -->
        <div class="data-summary mb-4">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总记录数" :value="previewData.length" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="有效记录" :value="validRecords" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="异常记录" :value="invalidRecords" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="成功率" :value="successRate" suffix="%" />
            </el-col>
          </el-row>
        </div>

        <!-- 数据表格 -->
        <el-table 
          :data="paginatedData" 
          border 
          stripe
          max-height="500"
          v-loading="tableLoading"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="caseNumber" label="案件编号" width="150" />
          <el-table-column prop="caseTitle" label="案件标题" min-width="200" show-overflow-tooltip />
          <el-table-column prop="caseType" label="案件类型" width="100" />
          <el-table-column prop="reportDate" label="举报日期" width="120" />
          <el-table-column prop="reporterName" label="举报人" width="100" />
          <el-table-column prop="reporterPhone" label="联系电话" width="120" />
          <el-table-column prop="targetName" label="被举报人" width="100" />
          <el-table-column prop="targetUnit" label="被举报单位" width="150" show-overflow-tooltip />
          <el-table-column prop="caseContent" label="案件内容" min-width="200" show-overflow-tooltip />
          <el-table-column prop="handleStatus" label="处理状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.handleStatus)">
                {{ row.handleStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip />
          <el-table-column label="数据状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.isValid ? 'success' : 'danger'">
                {{ row.isValid ? '正常' : '异常' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container mt-4">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="previewData.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <!-- 确认导入按钮 -->
        <div class="import-actions mt-4 text-center">
          <el-button 
            type="success" 
            size="large"
            @click="confirmImport"
            :loading="importing"
            :disabled="validRecords === 0"
          >
            <Icon icon="ep:check" class="mr-1" />
            确认导入 ({{ validRecords }}条有效记录)
          </el-button>
          <el-button size="large" @click="cancelImport">
            取消导入
          </el-button>
        </div>
      </el-card>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'

// 响应式数据
const selectedCaseType = ref('')
const fileList = ref([])
const uploading = ref(false)
const importing = ref(false)
const tableLoading = ref(false)
const previewData = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const uploadRef = ref()

// 上传配置
const uploadAction = '#' // 占位符，实际不会用到

// 计算属性
const validRecords = computed(() => 
  previewData.value.filter(item => item.isValid).length
)

const invalidRecords = computed(() => 
  previewData.value.filter(item => !item.isValid).length
)

const successRate = computed(() => 
  previewData.value.length > 0 
    ? Math.round((validRecords.value / previewData.value.length) * 100)
    : 0
)

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return previewData.value.slice(start, end)
})

// 文件上传前的检查
const beforeUpload = (file) => {
  const isExcel = /\.(xlsx|xls)$/.test(file.name.toLowerCase())
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(.xlsx, .xls)')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  
  return false // 阻止自动上传
}

// 文件选择变化
const handleFileChange = (file, files) => {
  console.log('文件变化:', file, files)
}

// 移除文件
const handleFileRemove = (file, files) => {
  console.log('移除文件:', file)
}

// 手动移除文件
const removeFile = (index) => {
  fileList.value.splice(index, 1)
}

// 清空文件
const clearFiles = () => {
  fileList.value = []
  previewData.value = []
  uploadRef.value?.clearFiles()
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
}

// 获取文件状态类型
const getFileStatusType = (status) => {
  const statusMap = {
    'ready': '',
    'uploading': 'warning',
    'success': 'success',
    'fail': 'danger'
  }
  return statusMap[status] || ''
}

// 获取文件状态文本
const getFileStatusText = (status) => {
  const statusMap = {
    'ready': '待上传',
    'uploading': '上传中',
    'success': '成功',
    'fail': '失败'
  }
  return statusMap[status] || '未知'
}

// 获取处理状态类型
const getStatusType = (status) => {
  const statusMap = {
    '待处理': 'warning',
    '处理中': 'primary',
    '已完成': 'success',
    '已关闭': 'info'
  }
  return statusMap[status] || ''
}

// 处理文件上传
const handleUpload = async () => {
  if (!selectedCaseType.value) {
    ElMessage.error('请先选择案件类型')
    return
  }
  
  if (fileList.value.length === 0) {
    ElMessage.error('请选择要上传的文件')
    return
  }
  
  uploading.value = true
  tableLoading.value = true
  
  try {
    const allData = []
    
    // 处理每个文件
    for (const file of fileList.value) {
      const data = await parseExcelFile(file.raw)
      allData.push(...data)
    }
    
    // 模拟后端处理和验证
    previewData.value = allData.map((item, index) => ({
      ...item,
      id: index + 1,
      caseType: selectedCaseType.value,
      isValid: validateRecord(item)
    }))
    
    ElMessage.success(`成功解析 ${allData.length} 条记录`)
  } catch (error) {
    console.error('文件解析失败:', error)
    ElMessage.error('文件解析失败，请检查文件格式')
  } finally {
    uploading.value = false
    tableLoading.value = false
  }
}

// 解析Excel文件
const parseExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = e.target.result
        const workbook = XLSX.read(data, { type: 'array' })
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(firstSheet)
        
        // 转换字段名
        const convertedData = jsonData.map(row => ({
          caseNumber: row['案件编号'] || row['编号'] || '',
          caseTitle: row['案件标题'] || row['标题'] || '',
          reportDate: row['举报日期'] || row['日期'] || '',
          reporterName: row['举报人'] || row['举报人姓名'] || '',
          reporterPhone: row['联系电话'] || row['电话'] || '',
          targetName: row['被举报人'] || row['被举报人姓名'] || '',
          targetUnit: row['被举报单位'] || row['单位'] || '',
          caseContent: row['案件内容'] || row['内容'] || '',
          handleStatus: row['处理状态'] || row['状态'] || '待处理',
          remark: row['备注'] || ''
        }))
        
        resolve(convertedData)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })
}

// 验证记录
const validateRecord = (record) => {
  // 基本字段验证
  if (!record.caseNumber || !record.caseTitle) {
    return false
  }
  
  // 电话号码验证
  if (record.reporterPhone && !/^1[3-9]\d{9}$/.test(record.reporterPhone)) {
    return false
  }
  
  return true
}

// 下载模板
const downloadTemplate = () => {
  // 根据案件类型生成不同的模板
  const templateData = [
    {
      '案件编号': 'CASE001',
      '案件标题': '示例案件标题',
      '举报日期': '2024-01-01',
      '举报人': '张三',
      '联系电话': '13800138000',
      '被举报人': '李四',
      '被举报单位': '某某单位',
      '案件内容': '案件详细内容描述',
      '处理状态': '待处理',
      '备注': '备注信息'
    }
  ]
  
  const ws = XLSX.utils.json_to_sheet(templateData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, selectedCaseType.value)
  XLSX.writeFile(wb, `${selectedCaseType.value}_导入模板.xlsx`)
}

// 导出预览数据
const exportPreviewData = () => {
  const exportData = previewData.value.map(item => ({
    '案件编号': item.caseNumber,
    '案件标题': item.caseTitle,
    '案件类型': item.caseType,
    '举报日期': item.reportDate,
    '举报人': item.reporterName,
    '联系电话': item.reporterPhone,
    '被举报人': item.targetName,
    '被举报单位': item.targetUnit,
    '案件内容': item.caseContent,
    '处理状态': item.handleStatus,
    '备注': item.remark,
    '数据状态': item.isValid ? '正常' : '异常'
  }))
  
  const ws = XLSX.utils.json_to_sheet(exportData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '预览数据')
  XLSX.writeFile(wb, `${selectedCaseType.value}_预览数据.xlsx`)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 确认导入
const confirmImport = async () => {
  if (validRecords.value === 0) {
    ElMessage.error('没有有效记录可以导入')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确认导入 ${validRecords.value} 条有效记录吗？`,
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    importing.value = true
    
    // TODO: 调用后端API进行实际导入
    // const validData = previewData.value.filter(item => item.isValid)
    // await importHistoryData(selectedCaseType.value, validData)
    
    // 模拟导入过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('数据导入成功')
    
    // 清空数据
    cancelImport()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导入失败:', error)
      ElMessage.error('导入失败，请重试')
    }
  } finally {
    importing.value = false
  }
}

// 取消导入
const cancelImport = () => {
  previewData.value = []
  fileList.value = []
  currentPage.value = 1
  uploadRef.value?.clearFiles()
}

onMounted(() => {
  console.log('历史数据导入页面已加载')
})
</script>

<style scoped>
.history-data-import {
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
}

.upload-area {
  width: 100%;
}

.file-list {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.data-summary {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
}

.pagination-container {
  display: flex;
  justify-content: center;
}

.import-actions {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.upload-actions {
  text-align: center;
}
</style>
