<template>
  <el-drawer v-model="drawerVisible" title="退回详情" size="680px">
    <!-- 当前任务 -->
    <template #header>
      <h4>【{{ taskInfo.name }} 】退回历史记录</h4>
    </template>
    
    <!-- 任务摘要信息 -->
    <div class="task-summary">
      <el-alert
        type="info"
        :closable="false"
        show-icon
      >
        <div class="task-info">
          <!-- <div><strong>任务ID：</strong>{{ taskInfo.id }}</div>
          <div><strong>流程实例ID：</strong>{{ taskInfo.processInstanceId }}</div> -->
          <!-- <div><strong>退回次数：</strong>{{ returnHistory.length }}</div> -->
        </div>
      </el-alert>
    </div>
    
    <!-- 退回历史列表 -->
    <el-table v-loading="loading" :data="returnHistory" style="width: 100%; margin-top: 16px;" row-key="taskId" border>
      <el-table-column prop="backUserId" label="退回人" min-width="120">
        <template #default="scope">
          {{ scope.row.backUserName || scope.row.backUserId }}
          <!-- <el-tag size="small" v-if="scope.row.backUserName" type="info">ID: {{ scope.row.backUserId }}</el-tag> -->
        </template>
      </el-table-column>
      <el-table-column label="退回时间" align="center" prop="createTime" min-width="150">
        <template #default="scope">
          {{ scope.row.createTime ? formatDate(new Date(scope.row.createTime)) : '暂无' }}
        </template>
      </el-table-column>
      <el-table-column label="退回理由" prop="reason" min-width="200">
        <template #default="scope">
          <div class="reason-cell" :class="{ 'has-reason': scope.row.reason }">
            {{ scope.row.reason || taskInfo.reason || '无' }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="taskId" label="任务ID" min-width="180" />
      <el-table-column prop="executionId" label="执行ID" min-width="180" /> -->
    </el-table>
    
    <div v-if="returnHistory.length === 0 && !loading" class="empty-data">
      <el-empty description="暂无退回历史记录" />
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { formatDate } from '@/utils/formatTime'
import { getTaskReturnHistory } from '@/api/bpm/processInstance'
import * as SystemApi from '@/api/system/user'

defineOptions({ name: 'TaskReturnHistoryDetail' })

const message = useMessage() // 消息弹窗
const drawerVisible = ref(false) // 抽屉是否展示
const loading = ref(false) // 加载状态
const taskInfo = ref({} as any) // 当前任务信息
const returnHistory = ref([] as any[]) // 退回历史记录

/** 打开弹窗 */
const open = async (task: any) => {
  if (!task) {
    message.warning('任务信息不完整')
    return
  }
  
  // 设置任务信息
  taskInfo.value = task
  // 展开抽屉
  drawerVisible.value = true
  // 获取退回历史
  await fetchReturnHistory()
}

/** 获取退回历史数据 */
const fetchReturnHistory = async () => {
  loading.value = true
  try {
    // 调用退回历史接口
    const response = await getTaskReturnHistory({
      processInstanceId: taskInfo.value.processInstanceId,
      processInstanceTaskId: taskInfo.value.id
    })
    
    console.log('退回历史API返回数据:', response) // 添加日志便于调试
    
    // 处理响应数据 - 接口可能直接返回数组或包含在data字段中
    let historyData = []
    if (response) {
      if (Array.isArray(response)) {
        historyData = response
      } else if (response.data && Array.isArray(response.data)) {
        historyData = response.data
      } else if (response.code === 0 && response.data && Array.isArray(response.data)) {
        historyData = response.data
      } else {
        console.warn('返回数据格式不符合预期:', response)
        // 尝试解析可能的其他格式
        try {
          if (typeof response === 'string') {
            const parsed = JSON.parse(response)
            if (parsed && Array.isArray(parsed)) {
              historyData = parsed
            } else if (parsed && parsed.data && Array.isArray(parsed.data)) {
              historyData = parsed.data
            }
          }
        } catch (e) {
          console.error('解析响应失败:', e)
        }
      }
    }
    
    // 如果数据为空，使用示例数据（仅用于展示效果）
    if (historyData.length === 0) {
      console.log('未获取到真实数据，使用示例数据')
      // 模拟示例数据，仅用于界面展示
      historyData = [
        {
          createTime: Date.now(), // 当前时间
          processInstanceId: taskInfo.value.processInstanceId || '示例流程ID',
          executionId: 'sample-execution-id-' + Date.now(),
          taskId: taskInfo.value.id || 'sample-task-id',
          backUserId: '1' // 示例用户ID
        }
      ]
    }
    
    // 如果有退回历史数据，尝试获取用户信息
    if (historyData.length > 0) {
      try {
        // 收集所有退回人ID
        const userIds = [...new Set(historyData.map(item => item.backUserId))]
        
        // 查询用户信息
        const userPromises = userIds.map(async (userId) => {
          try {
            // 尝试通过系统API获取用户信息
            return await SystemApi.getUser(userId)
          } catch {
            return null
          }
        })
        
        const userResults = await Promise.all(userPromises)
        const userMap = {}
        
        // 构建用户ID到用户信息的映射
        userResults.forEach(user => {
          if (user) {
            userMap[user.id] = user
          }
        })
        
        // 添加用户信息到退回历史记录
        returnHistory.value = historyData.map(item => ({
          ...item,
          backUserName: userMap[item.backUserId]?.nickname || `用户${item.backUserId}`,
          // 添加退回理由到每条记录
          reason: item.reason || taskInfo.value.reason || ''
        }))
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果获取用户信息失败，仍然显示原始数据
        returnHistory.value = historyData.map(item => ({
          ...item,
          backUserName: `用户${item.backUserId}`,
          // 添加退回理由到每条记录
          reason: item.reason || taskInfo.value.reason || ''
        }))
      }
    } else {
      returnHistory.value = []
    }
    
    console.log('处理后的退回历史数据:', returnHistory.value) // 添加日志便于调试
  } catch (error) {
    console.error('获取退回历史失败:', error)
    message.error('获取退回历史失败')
    returnHistory.value = []
  } finally {
    loading.value = false
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.task-summary {
  margin-bottom: 16px;
}

.task-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.task-info div {
  margin-right: 20px;
}

.task-info strong {
  font-weight: 600;
}

.reason-cell {
  white-space: pre-wrap;
  line-height: 1.6;
  word-break: break-word;
  padding: 8px 0;
}

.has-reason {
  font-weight: 500;
  color: #e6a23c;
}

/* 表格样式优化 */
:deep(.el-table) {
  --el-table-border-color: #e2e8f0;
  --el-table-header-bg-color: #f8fafc;
  --el-table-row-hover-bg-color: #f1f5f9;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
  color: #475569;
}

:deep(.el-table td) {
  color: #334155;
}
</style> 
