<template>
  <div class="task-list-flat">
    <div v-for="(task, index) in tasks" :key="index">
      <el-card 
        class="task-card"
        shadow="hover"
      >
        <template #header>
          <div class="task-header">
            <!-- 显示层级标签 -->
            <el-tag v-if="level > 0" size="small" type="info" class="level-tag">{{ level }}级子任务</el-tag>
            <!-- <span><strong>{{ task.name }}</strong></span> -->
            <!-- <span><strong>{{ "加签流程" }}</strong></span> -->
            <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="task.status" class="ml-5px" />
          </div>
        </template>
        
        <div class="task-info">
          <!-- 如果有父任务信息且不是顶级任务，显示父任务名称 -->
          <p v-if="level > 0 && task.parentTaskName" class="parent-task-info">
            <span class="task-label">所属任务：</span>
            <span>{{ task.parentTaskName }}</span>
          </p>
          
          <!-- 已分配处理人的情况 -->
          <p v-if="task.assigneeUser">
            <span class="task-label">处理人：</span>
            {{ task.assigneeUser.nickname }}
            <el-tag size="small" type="info">{{ task.assigneeUser.deptName }}</el-tag>
          </p>
          
          <!-- 待审批任务的处理人或候选处理人 -->
          <p v-else-if="[0, 1].includes(task.status)">
            <span class="task-label">处理人：</span>
            <!-- 有指定ownerUser的情况 -->
            <template v-if="task.ownerUser">
              {{ task.ownerUser.nickname }}
              <el-tag size="small" type="info">{{ task.ownerUser.deptName }}</el-tag>
            </template>
            <!-- 有指定assignee的情况 -->
            <template v-else-if="task.assignee">
              {{ task.assignee }}
            </template>
            <!-- 有候选处理人列表的情况 -->
            <template v-else-if="task.candidateUsers && task.candidateUsers.length > 0">
              <el-tag size="small" type="warning">候选处理人</el-tag>
              <span v-for="(user, uIndex) in task.candidateUsers" :key="uIndex" class="ml-5px">
                {{ user.nickname || user.username || user }}
                <span v-if="uIndex < task.candidateUsers.length - 1">,</span>
              </span>
            </template>
            <!-- 候选组的情况 -->
            <template v-else-if="task.candidateGroups && task.candidateGroups.length > 0">
              <el-tag size="small" type="warning">候选组</el-tag>
              <span v-for="(group, gIndex) in task.candidateGroups" :key="gIndex" class="ml-5px">
                {{ group.name || group }}
                <span v-if="gIndex < task.candidateGroups.length - 1">,</span>
              </span>
            </template>
            <template v-else>
              <el-tag size="small" type="info">待分配</el-tag>
            </template>
          </p>
          
          <p>
            <span class="task-label">创建时间：</span>
            <span class="task-value">{{ formatDate(task.createTime) }}</span>
            <span v-if="task.endTime" class="task-label ml-10px">处理时间：</span>
            <span v-if="task.endTime" class="task-value">{{ formatDate(task.endTime) }}</span>
          </p>
          <p v-if="task.reason">
            <span class="task-label">{{ task.status === 5 ? '退回理由：' : '审批意见：' }}</span>
            <span v-if="task.status === 5" style="color: #fa5555;">{{ task.reason }}</span>
            <span v-else>{{ task.reason }}</span>
          </p>
          
          <!-- 显示评论/加签信息 -->
          <div v-if="task.commentList && task.commentList.length > 0" class="comments-container">
            <p class="comment-header">
              <span class="task-label">操作记录：</span>
            </p>
            <div v-for="(comment, cIndex) in task.commentList" :key="cIndex" class="comment-item">
              <p class="comment-message">
                <Icon icon="ep:chat-dot-round" class="comment-icon" />
                {{ comment.message || comment.fullMessage }}
              </p>
              <p class="comment-time">{{ formatDate(comment.time) }}</p>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 平铺展示子任务而不是嵌套 -->
      <RecursiveTaskList 
        v-if="!isEmpty(task.children)"
        :tasks="task.children" 
        :level="level + 1"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'
import { DICT_TYPE } from '@/utils/dict'
import { isEmpty } from '@/utils/is'

defineOptions({ name: 'RecursiveTaskList' })

defineProps({
  // 要显示的任务列表
  tasks: {
    type: Array,
    required: true
  },
  // 当前嵌套层级
  level: {
    type: Number,
    default: 0
  }
})
</script>

<style lang="scss" scoped>
.task-list-flat {
  width: 100%;
}

.task-card {
  margin-bottom: 10px;
  border-left: 3px solid transparent;
  
  // 根据层级设置不同的左边框颜色
  &:has(.level-tag) {
    border-left-color: #909399;
  }
}

.task-header {
  display: flex;
  align-items: center;
}

.level-tag {
  margin-right: 8px;
}

.task-info {
  font-size: 13px;
  
  p {
    margin: 5px 0;
  }
}

.parent-task-info {
  padding: 3px 6px;
  background-color: #f5f7fa;
  border-radius: 3px;
  display: inline-block;
}

.task-label {
  font-weight: 500;
  margin-right: 5px;
}

.task-value {
  color: #8a909c;
}

.comments-container {
  margin-top: 10px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 2px solid #409EFF;
}

.comment-header {
  margin-bottom: 5px;
  font-weight: 500;
}

.comment-item {
  padding: 5px 0;
  border-bottom: 1px dashed #ebeef5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-message {
  margin: 3px 0;
  display: flex;
  align-items: flex-start;
}

.comment-icon {
  margin-right: 5px;
  margin-top: 2px;
  color: #909399;
  font-size: 14px;
}

.comment-time {
  font-size: 12px;
  color: #909399;
  margin: 2px 0 0 20px;
}

.ml-5px {
  margin-left: 5px;
}

.ml-10px {
  margin-left: 10px;
}
</style>
