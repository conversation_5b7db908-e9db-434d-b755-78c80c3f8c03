<template>
  <ContentWrap>
    <!-- 审批信息，当hideApproval为true时隐藏 -->
    <template v-if="!hideApproval">
      <el-card
        v-for="(item, index) in runningTasks"
        :key="index"
        v-loading="processInstanceLoading"
        class="box-card"
      >
        <template #header>
          <span class="el-icon-picture-outline">流程任务【{{ item.isDelayTask ? '申请延长办理时限的审批流程' : item.name }}】</span>
        </template>
        <el-col :offset="6" :span="16">
          <el-form
            :ref="'form' + index"
            :model="auditForms[index]"
            :rules="auditRule"
            label-width="100px"
          >
            <el-form-item v-if="processInstance && processInstance.name" label="流程名">
              {{ processInstance.name }}
            </el-form-item>
            <el-form-item v-if="processInstance && processInstance.startUser" label="流程发起人">
              {{ processInstance?.startUser.nickname }}
              <el-tag size="small" type="info">{{ processInstance?.startUser.deptName }}</el-tag>
            </el-form-item>
            <el-card v-if="runningTasks[index].formId > 0" class="mb-15px !-mt-10px">
              <!-- <template #header>
                <span class="el-icon-picture-outline">
                  填写表单【{{ runningTasks[index]?.formName }}】
                </span>
              </template> -->
              <form-create
                v-model="approveForms[index].value"
                v-model:api="approveFormFApis[index]"
                :option="approveForms[index].option"
                :rule="approveForms[index].rule"
              />

            </el-card>
            <!-- 添加新闻预览按钮，如果当前任务是新闻类型 -->
            <div v-if="isNewsProcess" class="mb-15px text-right">
              <el-button type="primary" @click="previewApproveNews(index)">
                <Icon icon="ep:view" />
                预览
              </el-button>
            </div>
            <!-- <el-card v-else class="mb-15px !-mt-10px">-->

  <!--            <form-create-->
  <!--              v-model="detailForm.value"-->
  <!--              v-model:api="fApi"-->
  <!--              :option="detailForm.option"-->
  <!--              :rule="detailForm.rule"-->
  <!--            />-->
  <!--          </el-card>-->
            <el-form-item v-if="!runningTasks[index].formId" label="审批意见" prop="reason">
              <el-input
                v-model="auditForms[index].reason"
                placeholder="请输入审批意见"
                type="textarea"
              />
            </el-form-item>
            <el-form-item label="抄送人" prop="copyUserIds">
              <el-select v-model="auditForms[index].copyUserIds" multiple placeholder="请选择抄送人">
                <el-option
                  v-for="itemx in userOptions"
                  :key="itemx.id"
                  :label="itemx.nickname"
                  :value="itemx.id"
                />
              </el-select>
            </el-form-item>
            <!-- 延期任务选择截止日期 -->
            <el-form-item v-if="item.isDelayTask" label="当前办理时限" prop="currentDelayDate">
              <el-date-picker
                v-model="auditForms[index].currentDelayDate"
                type="date"
                placeholder="当前办理时限"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                disabled
              />
            </el-form-item>
            <el-form-item v-if="item.isDelayTask" label="申请延期理由" prop="delayReason">
              <el-input
                v-model="auditForms[index].delayReason"
                type="textarea"
                placeholder="申请延期理由"
                disabled
              />
            </el-form-item>
            <el-form-item v-if="item.isDelayTask" label="新的办理时限" prop="delayDate">
              <el-date-picker
                v-model="auditForms[index].delayDate"
                type="date"
                placeholder="选择新的办理时限"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-form>
          <div style="margin-bottom: 20px; margin-left: 10%; font-size: 14px">
            <!-- TODO @jason：建议搞个 if 来判断，替代现有的 !item.buttonsSetting || item.buttonsSetting[OpsButtonType.APPROVE]?.enable -->
            <el-button
              type="success"
              v-if="!item.buttonsSetting || item.buttonsSetting[OperationButtonType.APPROVE]?.enable"
              @click="item.isDelayTask ? handleApproveDelay(item) : handleAudit(item, true)"
            >
              <Icon icon="ep:select" />
              <!-- TODO @jason：这个也是类似哈，搞个方法来生成名字 -->
              {{
                item.buttonsSetting?.[OperationButtonType.APPROVE]?.displayName ||
                OPERATION_BUTTON_NAME.get(OperationButtonType.APPROVE)
              }}
            </el-button>
            <el-button
              v-hasPermi="['bpm:process-instance:reject']"
              v-if="!item.buttonsSetting || item.buttonsSetting[OperationButtonType.REJECT]?.enable"
              type="danger"
              @click="handleAudit(item, false)"
            >
              <Icon icon="ep:close" />
              {{
                item.buttonsSetting?.[OperationButtonType.REJECT].displayName ||
                OPERATION_BUTTON_NAME.get(OperationButtonType.REJECT)
              }}
            </el-button>
            <el-button
              v-hasPermi="['bpm:process-instance:transfer']"
              v-if="(!item.buttonsSetting || item.buttonsSetting[OperationButtonType.TRANSFER]?.enable) && !item.isDelayTask"
              type="primary"
              @click="openTaskUpdateAssigneeForm(item.id)"
            >
              <Icon icon="ep:edit" />
              {{
                item.buttonsSetting?.[OperationButtonType.TRANSFER]?.displayName ||
                OPERATION_BUTTON_NAME.get(OperationButtonType.TRANSFER)
              }}
            </el-button>
            <el-button
              v-hasPermi="['bpm:process-instance:delegate']"
              v-if="(!item.buttonsSetting || item.buttonsSetting[OperationButtonType.DELEGATE]?.enable) && !item.isDelayTask"
              type="primary"
              @click="handleDelegate(item)"
            >
              <Icon icon="ep:position" />
              {{
                item.buttonsSetting?.[OperationButtonType.DELEGATE]?.displayName ||
                OPERATION_BUTTON_NAME.get(OperationButtonType.DELEGATE)
              }}
            </el-button>
            <el-button
              v-if="(!item.buttonsSetting || item.buttonsSetting[OperationButtonType.ADD_SIGN]?.enable) && !item.isDelayTask"
              type="primary"
              @click="handleSign(item)"
            >
              <Icon icon="ep:plus" />
              {{
                item.buttonsSetting?.[OperationButtonType.ADD_SIGN]?.displayName ||
                OPERATION_BUTTON_NAME.get(OperationButtonType.ADD_SIGN)
              }}
            </el-button>
            <el-button
              v-if="!item.buttonsSetting || item.buttonsSetting[OperationButtonType.RETURN]?.enable"
              type="warning"
              @click="handleBack(item)"
            >
              <Icon icon="ep:back" />
              {{
                item.buttonsSetting?.[OperationButtonType.RETURN]?.displayName ||
                OPERATION_BUTTON_NAME.get(OperationButtonType.RETURN)
              }}
            </el-button>
            <!-- 添加申请延期按钮 -->
            <el-button
              v-hasPermi="['bpm:process-instance:apply-delay']"
              v-if="processInstance?.formVariables?.blsx"
              type="info"
              @click="handleDelayApply(item)"
            >
              <Icon icon="ep:timer" />
              申请延期
            </el-button>
            <!-- <el-button
              type="info"
              @click="handleDelayApply(item)"
            >
              <Icon icon="ep:timer" />
              测试
            </el-button> -->
          </div>
        </el-col>
      </el-card>    
    </template>

  <el-tabs
    v-model="activeName"
    type="card"
    class="demo-tabs"
    @tab-click="handleClick"
  >
    <el-tab-pane label="申请信息" name="first">
          <!-- 申请信息 -->
      <el-card v-loading="processInstanceLoading" class="box-card">
      <template #header>
        <div class="flex justify-between items-center">
        <span class="el-icon-document">申请信息【{{ processInstance.name }}】</span>
          <!-- 添加查看上级流程按钮 -->
          <el-button 
            v-if="isDelayClueBpm" 
            type="primary" 
            @click="handleViewParentDetail"
          >
            <Icon icon="ep:back" />
            查看上级流程
          </el-button>
        </div>
      </template>
      <!-- 情况一：流程表单 -->
      <el-col v-if="processInstance?.processDefinition?.formType === 10" :offset="6" :span="16">
        <!-- 添加新闻预览按钮 -->
        <div v-if="isNewsProcess" class="mb-15px text-right">
          <el-button type="primary" @click="previewNews">
            <Icon icon="ep:view" />
            预览
          </el-button>
        </div>
        <form-create
          v-model="detailForm.value"
          v-model:api="fApi"
          :option="detailForm.option"
          :rule="detailForm.rule"
        />
      </el-col>
      <!-- 情况二：业务表单 -->
      <div v-if="processInstance?.processDefinition?.formType === 20">
        <BusinessFormComponent :id="processInstance.businessKey" />
      </div>
      </el-card>  
    </el-tab-pane>
    <el-tab-pane label="流程记录" name="second">
          <!-- 审批记录 -->
        <ProcessInstanceTaskList
          :loading="tasksLoad"
          :process-instance="processInstance"
          :tasks="tasks"
          @refresh="getTaskList"
        />
    </el-tab-pane>
    <el-tab-pane label="流程图" name="third" :lazy="false">
          <!-- 高亮流程图 -->
          <!-- 需要重新加载一次数据才能保证高亮显示 -->
        <ProcessInstanceBpmnViewer
        :id="`${id}`"
        :bpmn-xml="bpmnXml"
        :loading="processInstanceLoading"
        :process-instance="processInstance"
        :tasks="tasks"
      />          
    </el-tab-pane>
  </el-tabs>


    <!-- 弹窗：转派审批人 -->
    <TaskTransferForm ref="taskTransferFormRef" @success="getDetail" />
    <!-- 弹窗：退回节点 -->
    <TaskReturnForm ref="taskReturnFormRef" @success="getDetail" />
    <!-- 弹窗：委派，将任务委派给别人处理，处理完成后，会重新回到原审批人手中-->
    <TaskDelegateForm ref="taskDelegateForm" @success="getDetail" />
    <!-- 弹窗：加签，当前任务审批人为A，向前加签选了一个C，则需要C先审批，然后再是A审批，向后加签B，A审批完，需要B再审批完，才算完成这个任务节点 -->
    <TaskSignCreateForm ref="taskSignCreateFormRef" @success="handleSignSuccess" @validate-main-form="handleValidateMainForm" />
    <!-- 弹窗：申请延期 -->
    <TaskDelayForm ref="taskDelayFormRef" @success="getDetail" />
  </ContentWrap>
</template>
<script lang="ts" setup>
import formCreate from '@form-create/element-ui';
import { useUserStore } from '@/store/modules/user'
import { setConfAndFields2 } from '@/utils/formCreate'
import type { ApiAttrs } from '@form-create/element-ui/types/config'
import * as DefinitionApi from '@/api/bpm/definition'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as TaskApi from '@/api/bpm/task'
import ProcessInstanceBpmnViewer from './ProcessInstanceBpmnViewer.vue'
import ProcessInstanceTaskList from './ProcessInstanceTaskList.vue'
import TaskReturnForm from './dialog/TaskReturnForm.vue'
import TaskDelegateForm from './dialog/TaskDelegateForm.vue'
import TaskTransferForm from './dialog/TaskTransferForm.vue'
import TaskSignCreateForm from './dialog/TaskSignCreateForm.vue'
import TaskDelayForm from './dialog/TaskDelayForm.vue'
import { registerComponent } from '@/utils/routerHelper'
import { isEmpty } from '@/utils/is'
import * as UserApi from '@/api/system/user'
import {
  OperationButtonType,
  OPERATION_BUTTON_NAME
} from '@/components/SimpleProcessDesignerV2/src/consts'
import { ref, computed, watch } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { useDictStore } from '@/store/modules/dict'
import { setConfAndFields, handleFileName, getFullUrl } from '@/utils/formCreate'
import { ElLoading } from 'element-plus'
import { t } from '@wangeditor/editor';
import { getUserProfile } from '@/api/system/user/profile'
import { useRouter } from 'vue-router'

defineOptions({ name: 'BpmProcessInstanceDetail' })

const { query } = useRoute() // 查询参数
const message = useMessage() // 消息弹窗
const { proxy } = getCurrentInstance() as any
const router = useRouter() // 路由

const userId = useUserStore().getUser.id // 当前登录的编号
const id = query.id as unknown as string // 流程实例的编号
const hideApproval = query.hideApproval === 'true' // 是否隐藏审批信息部分
const processInstanceLoading = ref(false) // 流程实例的加载中
const processInstance = ref<any>({}) // 流程实例
const bpmnXml = ref('') // BPMN XML
const tasksLoad = ref(true) // 任务的加载中
const tasks = ref<any[]>([]) // 任务列表
// ========== 审批信息 ==========
const runningTasks = ref<any[]>([]) // 运行中的任务
const auditForms = ref<any[]>([]) // 审批任务的表单
const auditRule = reactive({
  reason: [{ required: true, message: '建议不能为空', trigger: 'blur' }]
})
const approveForms = ref<any[]>([]) // 审批通过时，额外的补充信息
const approveFormFApis = ref<ApiAttrs[]>([]) // approveForms 的 fAPi

const processDiagramFlag = ref(false) //是否第一次加载流程图
// 详情导航栏
const activeName = ref('first')
//切换菜单 切换到高亮流程图时需要重新加载一次才能保持高亮 组件通过监听Loading属性的变化完成更新
const handleClick = async(tab: TabsPaneContext, event: Event) => {
  if(tab.props.name=="third"){
    // 切换到流程图选项卡时，只更新流程图相关信息，不重新加载表单数据
    processInstanceLoading.value = true
    
    try {
      console.log('更新流程图数据...')
      // 强制延时确保后端数据已经更新
      await new Promise(resolve => setTimeout(resolve, 300))
      // 只重新获取任务列表，不重新加载表单数据
      await getTaskListOnly()
      
      // 添加短暂延时确保DOM更新
      await new Promise(resolve => setTimeout(resolve, 200))
    } catch (e) {
      console.error('加载流程图数据失败:', e)
    } finally {
    processInstanceLoading.value = false      
    }
  }
}
// ========== 申请信息 ==========
const fApi = ref<ApiAttrs>() //
const detailForm = ref({
  rule: [],
  option: {},
  value: {}
}) // 流程实例的表单详情

// 添加一个响应式变量来存储是否为二级线索件的值
const isSecondLevelClueValue = ref(false)

// 添加检查当前用户岗位的方法
// 判断当前用户是否具有特定岗位
const hasRequiredPost = ref(false) // 是否有所需岗位的标志

// 获取当前用户的岗位信息
const getCurrentUserPosts = async () => {
  try {
    // 通过查询个人信息获取当前用户岗位
    const userProfile = await getUserProfile()
    if (userProfile && userProfile.posts) {
      // 检查用户是否有"二级集团案管专员"或"项目经理"岗位
      const requiredPosts = ['二级集团案管专员', '项目经理']
      const userPostNames = userProfile.posts.map(post => post.name)
      
      // 检查用户的岗位是否包含任一所需岗位
      const hasPost = requiredPosts.some(postName => userPostNames.includes(postName))
      hasRequiredPost.value = hasPost
      
      console.log('当前用户岗位:', userPostNames, '是否有所需岗位:', hasRequiredPost.value)
    }
  } catch (error) {
    console.error('获取用户岗位信息失败:', error)
    hasRequiredPost.value = false
  }
}

/** 监听processInstance的变化，更新isSecondLevelClueValue */
watch(
  () => processInstance.value?.processDefinition?.key,
  (newKey) => {
    console.log("processInstance.key变化:", newKey)
    if (newKey === 'ejwtxs_bpm' || newKey === 'wtxs_bpm') {
      isSecondLevelClueValue.value = true
    } else {
      isSecondLevelClueValue.value = false
    }
  }
)

/** 监听 approveFormFApis，实现它对应的 form-create 初始化后，隐藏掉对应的表单提交按钮 */
watch(
  () => approveFormFApis.value,
  (value) => {
    value?.forEach((api) => {
      api.btn.show(false)
      api.resetBtn.show(false)
    })
  },
  {
    deep: true
  }
)

/** 监听processInstance的变化，更新已加载任务的延期日期 */
watch(
  () => processInstance.value?.formVariables?.blsx,
  (newBlsx) => {
    if (newBlsx && runningTasks.value.length > 0) {
      console.log("监测到主表单blsx变化，更新默认延期日期:", newBlsx)
      // 更新所有延期任务的默认日期
      runningTasks.value.forEach((task, index) => {
        if (task.isDelayTask && auditForms.value[index]) {
          auditForms.value[index].delayDate = newBlsx
          console.log(`已更新任务[${index}]的延期日期为:`, newBlsx)
        }
      })
    }
  }
)

/** 提交审批 */
const handleAudit = async (task, pass) => {
  try{
    processInstanceLoading.value = true
    // 1.1 获得对应表单
    const index = runningTasks.value.indexOf(task)
    const auditFormRef = proxy.$refs['form' + index][0]
    // 1.2 校验表单
    const elForm = unref(auditFormRef)
    if (!elForm) return
    let valid = await elForm.validate()
    if (!valid) return
    // 校验申请表单（可编辑字段）
    // TODO @jason：之前这里是 if (!fApi.value) return；针对业务表单的情况下，会导致没办法审核，可能要看下。我这里改了点，看看是不是还有别的地方兼容性
    if (fApi.value) {
      valid = await fApi.value.validate()
      if (!valid) return
    }

    // 2.1 提交审批
    const data = {
      id: task.id,
      reason: auditForms.value[index].reason,
      copyUserIds: auditForms.value[index].copyUserIds
    }
    if (pass) {
      // 审批通过，并且有额外的 approveForm 表单，需要校验 + 拼接到 data 表单里提交
      const formCreateApi = approveFormFApis.value[index]
      if (formCreateApi) {
        await formCreateApi.validate()
        data.variables = approveForms.value[index].value
      }
      // 获取表单可编辑字段的值
      // if (fApi.value) {
      //   data.variables = getWritableValueOfForm(task.fieldsPermission)
      // }

      await TaskApi.approveTask(data)
      message.success('通过成功')
    } else {
      // 拒绝审批时，弹窗让用户填写拒绝理由
      const { value: rejectReason } = await ElMessageBox.prompt(
        '请输入拒绝理由',
        '拒绝审批',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: (value) => {
            return value.trim() !== '';
          },
          inputErrorMessage: '拒绝理由不能为空'
        }
      );
      
      // 如果用户取消了弹窗，则不执行拒绝操作
      if (rejectReason === undefined) {
        processInstanceLoading.value = false;
        return;
      }
      
      // 使用弹窗输入的拒绝理由替换表单中的理由
      data.reason = rejectReason;
      
      await TaskApi.rejectTask(data)
      message.success('不通过成功')
    }
    }finally{
      processInstanceLoading.value = false
    }
    // 2.2 加载最新数据
    await getDetail()
    
    // 2.3 如果当前是流程图选项卡，则触发流程图更新
    if (activeName.value === 'third') {
      console.log('当前是流程图选项卡，触发额外刷新...')
      processInstanceLoading.value = true
      try {
        // 强制延时等待后端数据更新
        await new Promise(resolve => setTimeout(resolve, 500))
        // 重新加载任务列表以更新流程图
        await getTaskListOnly()
        // 等待DOM更新
        await nextTick()
        // 再次延时确保流程图组件能够正确渲染
        await new Promise(resolve => setTimeout(resolve, 200))
      } finally {
        processInstanceLoading.value = false
      }
    }
}

/** 处理延期申请的通过操作 */
const handleApproveDelay = async (task) => {
  try {
    processInstanceLoading.value = true
    // 1.1 获得对应表单
    const index = runningTasks.value.indexOf(task)
    const auditFormRef = proxy.$refs['form' + index][0]
    // 1.2 校验表单
    const elForm = unref(auditFormRef)
    if (!elForm) return
    let valid = await elForm.validate()
    if (!valid) return

    // 准备延期任务的请求参数
    const delayData = {
      id: task.id,
      copyUserIds: auditForms.value[index].copyUserIds || [],
      processFormKey: 'blsx', // 固定值
      taskFormValue: auditForms.value[index].delayDate, // 新的办理时限
      // taskFormValue:"2025-05-30",
      reason:"同意申请延期"
    }
    console.log('延期申请参数:', delayData)
    
    // 调用专门的延期审批接口
    await TaskApi.approveApplyDelay(delayData)
    message.success('延期申请审批通过')
  } catch (e) {
    message.error('延期申请审批失败：' + e.message)
  } finally {
    processInstanceLoading.value = false
    // 刷新数据
    await getDetail()
    
    // 如果当前是流程图选项卡，则触发流程图更新
    if (activeName.value === 'third') {
      console.log('延期审批后，当前是流程图选项卡，触发额外刷新...')
      processInstanceLoading.value = true
      try {
        // 重新加载任务列表以更新流程图
        await getTaskListOnly()
        // 等待DOM更新
        await nextTick()
      } finally {
        processInstanceLoading.value = false
      }
    }
  }
}

/** 转派审批人 */
const taskTransferFormRef = ref()
const openTaskUpdateAssigneeForm = (id: string) => {
  taskTransferFormRef.value.open(id)
}

/** 处理审批退回的操作 */
const taskDelegateForm = ref()
const handleDelegate = async (task) => {
  taskDelegateForm.value.open(task.id)
}

/** 处理审批退回的操作 */
const taskReturnFormRef = ref()
const handleBack = (task) => {
  taskReturnFormRef.value.open(task.id, task)
}

/** 处理审批加签的操作 */
const taskSignCreateFormRef = ref()
const handleSign = async (task: any) => {
  taskSignCreateFormRef.value.open(task.id)
}

/** 处理加签成功的回调 */
const handleSignSuccess = async (data = {}) => {
  // 如果需要提交主表单
  if (data && data.submitMainForm) {
    // 获取任务对应的索引
    const taskIndex = runningTasks.value.findIndex(item => item.id === data.id);
    if (taskIndex >= 0) {
      // 构建审批数据
      const approveData = {
        id: runningTasks.value[taskIndex].id,
        reason: auditForms.value[taskIndex].reason || '',
        copyUserIds: auditForms.value[taskIndex].copyUserIds || []
      };

      // 如果有表单数据，添加到变量中
      const formCreateApi = approveFormFApis.value[taskIndex];
      if (formCreateApi) {
        approveData.variables = approveForms.value[taskIndex].value;
      }

      try {
        // 提交审批任务
        await TaskApi.approveTask(approveData);
        message.success('表单提交成功');
      } catch (error) {
        console.error('提交主表单失败:', error);
        message.error('表单提交失败：' + error.message);
      }
    }
  }
  // 刷新数据
  await getDetail();
}

/** 处理主表单校验的请求 */
const handleValidateMainForm = async ({ callback, taskId }) => {
  try {
    // 找到对应任务的索引
    const taskIndex = runningTasks.value.findIndex(item => item.id === taskId);
    if (taskIndex < 0) {
      callback(false);
      return;
    }

    // 校验对应的表单
    const auditFormRef = proxy.$refs['form' + taskIndex][0];
    // 校验表单
    const elForm = unref(auditFormRef);
    if (!elForm) {
      callback(false);
      return;
    }
    
    let valid = await elForm.validate().catch(() => false);
    if (!valid) {
      callback(false);
      return;
    }
    
    // 校验额外表单
    const formCreateApi = approveFormFApis.value[taskIndex];
    if (formCreateApi) {
      try {
        await formCreateApi.validate();
        callback(true, { taskIndex, id: taskId });
      } catch (error) {
        callback(false);
      }
    } else {
      callback(true, { taskIndex, id: taskId });
    }
  } catch (error) {
    console.error('表单校验失败:', error);
    callback(false);
  }
}

/** 获得详情 */
const getDetail = async () => {
  try {
    // 1. 先获得流程实例相关数据
    await getProcessInstance()
    // 2. 再获得流程任务列表（审批记录）
  await getTaskList()
    
    // 3. 如果当前是流程图选项卡，添加短暂延时确保DOM更新完成
    if (activeName.value === 'third') {
      console.log('详情加载完成，当前是流程图选项卡，确保流程图正确更新...')
      // 添加短暂延时确保DOM已完全更新
      await new Promise(resolve => setTimeout(resolve, 100))
      // 触发processInstanceLoading的变化，这样流程图组件会感知到变化并更新
      processInstanceLoading.value = true
      await nextTick()
      processInstanceLoading.value = false
    }
  } catch (error) {
    console.error('获取详情失败:', error)
  }
}

/** 加载流程实例 */
const BusinessFormComponent = ref(null) // 异步组件
const getProcessInstance = async () => {
  try {
    processInstanceLoading.value = true
    const data = await ProcessInstanceApi.getProcessInstance(id)
    if (!data) {
      message.error('查询不到流程信息！')
      return
    }
    processInstance.value = data
    //将实例传给表单
    addGetProcessInstanceApi(data)

    // 设置表单信息
    const processDefinition = data.processDefinition
    if (processDefinition.formType === 10) {
      if (detailForm.value.rule.length > 0) {
        detailForm.value.value = data.formVariables
      } else {
        setConfAndFields2(
          detailForm,
          processDefinition.formConf,
          processDefinition.formFields,
          data.formVariables
        )
      }
      nextTick().then(() => {
        fApi.value?.btn.show(false)
        fApi.value?.resetBtn.show(false)
        fApi.value?.disabled(true)
        // handleTreeDisabled()

        // 注意：runningTasks可能尚未加载，将在getTaskList完成后单独设置权限
      })
    } else {
      // 注意：data.processDefinition.formCustomViewPath 是组件的全路径，例如说：/crm/contract/detail/index.vue
      BusinessFormComponent.value = registerComponent(data.processDefinition.formCustomViewPath)
    }

    // 加载流程图
    bpmnXml.value = (
      await DefinitionApi.getProcessDefinition(processDefinition.id as number)
    )?.bpmnXml
  } finally {
    processInstanceLoading.value = false
  }
}

/**处理树形控件的禁用状态*/
const handleTreeDisabled = () => {
  if (!fApi.value) {
          console.warn('fApi.value 为空，无法获取规则')
          return
        }

        // 直接使用rule属性
        const rules = fApi.value.rule
        
        if (!rules) {
          return
        }

        // 递归处理规则及其子规则
        const processRule = (rule: any, field: string) => {
          if (!rule) return
          
          // 处理当前规则
          if (rule.type === 'tree') {
            // 如果是树形控件，处理props.data中的节点
            if (rule.props && rule.props.data && Array.isArray(rule.props.data)) {
              const setDataDisabled = (data: any[]) => {
                if (!Array.isArray(data)) return
                data.forEach(item => {
                  item.disabled = true
                  if (item.children && item.children.length > 0) {
                    setDataDisabled(item.children)
                  }
                })
              }
              setDataDisabled(rule.props.data)
            }
          }

          // 处理子规则
          if (rule.children && Array.isArray(rule.children)) {
            rule.children.forEach((childRule: any, index: number) => {
              const childField = `${field}.${index}`
              processRule(childRule, childField)
            })
          }
        }

        // 遍历所有字段，处理树形控件
        Object.keys(rules).forEach((field) => {
          processRule(rules[field], field)
        })
}
/** 加载任务列表 */
const getTaskList = async () => {
  runningTasks.value = []
  auditForms.value = []
  approveForms.value = []
  approveFormFApis.value = []
  try {
    // 获得未取消的任务
    tasksLoad.value = true
    const data = await TaskApi.getTaskListByProcessInstanceId(id)
    tasks.value = []
    // 1.1 移除已取消的审批
    data.forEach((task) => {
      if (task.status !== 4) {
        tasks.value.push(task)
      }
    })
    // 1.2 排序，将未完成的排在前面，已完成的排在后面；
    tasks.value.sort((a, b) => {
      // 有已完成的情况，按照完成时间倒序
      if (a.endTime && b.endTime) {
        return b.endTime - a.endTime
      } else if (a.endTime) {
        return 1
      } else if (b.endTime) {
        return -1
        // 都是未完成，按照创建时间倒序
      } else {
        return b.createTime - a.createTime
      }
    })

    // 获得需要自己审批的任务
    loadRunningTask(tasks.value)
    
    // 设置表单权限 - 在任务加载完成后设置
    nextTick(() => {
      if (runningTasks.value.length > 0 && fApi.value) {
        const task = runningTasks.value[0]
        if (task.fieldsPermission) {
          console.log('设置表单权限:', task.fieldsPermission)
          Object.keys(task.fieldsPermission).forEach((item) => {
            setFieldPermission(item, task.fieldsPermission[item])
          })
        }
      }
    })
  } finally {
    tasksLoad.value = false
  }
}

/**
 * 加载运行中的任务，提供给 User 审批
 */
const loadRunningTask = async (dataSource: any[]) => {
  dataSource.forEach(async (task) => {
    if (!isEmpty(task.children)) {
      loadRunningTask(task.children)
    }
    // 2.1 只有待处理才需要
    if (task.status !== 1 && task.status !== 6) {
      return
    }
    // 2.2 自己不是处理人
    if (!task.assigneeUser || task.assigneeUser.id !== userId) {
      return
    }

    // 检查是否是申请延期任务
    // 暂时不使用此功能
    try {
      // const data  = await TaskApi.checkTaskApplyDelay(task.id)
      // console.log('检查申请延期任务结果:', data)
      // task.isDelayTask = data
      const data = false
      if (data) {
        // 初始化延期日期 - 优先使用主表单的blsx值
        const mainFormBlsx = processInstance.value?.formVariables?.blsx 
        const defaultDelayDate = mainFormBlsx || task.formVariables?.blsx
  
        // 获取申请延期理由 - 从任务列表中获取第一个任务的reason
        let delayReason = ''
        try {
          // 获取任务列表数据
          const taskListData = await TaskApi.getTaskListByProcessInstanceId(id)
          if (taskListData && taskListData.length > 0) {
            // 找到状态为"处理中"的延期任务，通常是最新的延期申请
            const delayTask = taskListData.find(t => t.reason)
            if (delayTask) {
              delayReason = delayTask.reason
            } else {
              console.log("未找到包含理由的延期任务")
              delayReason = ''
            }
          }
        } catch (e) {
          console.error("获取延期理由失败:", e)
        }
        
        if (!auditForms.value[runningTasks.value.length]) {
          auditForms.value.push({
            reason: '',
            copyUserIds: [],
            currentDelayDate: defaultDelayDate,
            delayDate: defaultDelayDate,
            delayReason: delayReason
          })
        } else {
          auditForms.value[runningTasks.value.length].currentDelayDate = defaultDelayDate
          auditForms.value[runningTasks.value.length].delayDate = defaultDelayDate
          auditForms.value[runningTasks.value.length].delayReason = delayReason
        }
      }
    } catch (error) {
      console.error('检查申请延期任务失败:', error)
      task.isDelayTask = false
    }

    // 2.3 添加到处理任务
    runningTasks.value.push({ ...task })
    
    // 如果上面没有创建表单（非延期任务），这里创建
    if (!auditForms.value[runningTasks.value.length - 1]) {
    auditForms.value.push({
      reason: '',
      copyUserIds: []
    })
    }

    // 2.4 处理 approve 表单
    if (task.formId && task.formConf) {
      const approveForm = {}
      setConfAndFields2(approveForm, task.formConf, task.formFields, task.formVariables)
      approveForms.value.push(approveForm)
    } else {
      approveForms.value.push({}) // 占位，避免为空
    }
  })
}

/**
 * 设置表单权限
 */
const setFieldPermission = (field: string, permission: string) => {
  if (permission === '1') {
    fApi.value?.disabled(true, field)
  }
  if (permission === '2') {
    fApi.value?.disabled(false, field)
    // handleTreeDisabled()
  }
  if (permission === '3') {
    fApi.value?.hidden(true, field)
  }
}
/**
 * 获取可以编辑字段的值
 */
const getWritableValueOfForm = (fieldsPermission: Object) => {
  const fieldsValue = {}
  if (fieldsPermission && fApi.value) {
    Object.keys(fieldsPermission).forEach((item) => {
      if (fieldsPermission[item] === '2') {
        fieldsValue[item] = fApi.value.getValue(item)
      }
    })
  }
  return fieldsValue
}

/** 初始化 */
const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
onMounted(async () => {
  await getDetail()
  // 获得用户列表
  userOptions.value = await UserApi.getSimpleUserList()
  // 获取用户岗位信息
 // await getCurrentUserPosts()
})

// 修改判断逻辑和处理函数
/** 判断是否为二级线索件且用户有所需岗位 */
const isSecondLevelClue = () => {
  console.log("【判断二级线索件】");
  // 使用响应式变量和实时检测结合的方式
  const key = processInstance.value?.processDefinition?.key;
  const isClue = (key === 'ejwtxs_bpm' || key === 'wtxs_bpm' || isSecondLevelClueValue.value) && hasRequiredPost.value;
  console.log("【判断二级线索件】processKey:", key, "响应式值:", isSecondLevelClueValue.value, "用户岗位:", hasRequiredPost.value, "最终结果:", isClue);
  
  // 返回结合了响应式变量的结果和用户岗位判断
  return isClue;
}

/** 判断是否应该显示申请延期按钮 */
const shouldShowDelayButton = (task: any, index: number) => {
  // 1. 检查用户岗位，如果是项目经理或其他指定岗位，直接返回true
  const userInfo = useUserStore().getUser;
  if (userInfo && userInfo.posts) {
    const userPostNames = userInfo.posts.map(post => post.name);
    const requiredPosts = ['二级集团案管专员', '项目经理'];
    if (requiredPosts.some(postName => userPostNames.includes(postName))) {
      console.log(`用户拥有所需岗位，允许显示申请延期按钮`);
      return true;
    }
  }
  
  // 2. 检查任务表单配置中是否定义了delay字段
  if (task.formFields) {
    // 修改判断逻辑，寻找任何包含"delay"的字段配置
    const hasDelayField = task.formFields.some(field => {
      // 1. 检查field属性是否为delay
      if (field.field === 'delay' || field.vModel === 'delay') {
        return true;
      }
      
      // 2. 将对象转为字符串，检查是否包含delay字符串
      try {
        const fieldStr = JSON.stringify(field);
        return fieldStr.toLowerCase().includes('delay');
      } catch (e) {
        console.error('字段序列化出错:', e);
        return false;
      }
    });
    
    if (hasDelayField) {
      console.log(`任务[${index}]的formFields中找到delay字段定义`);
      return true;
    }
  }
  return false;
}

/** 处理申请延期操作 */
const taskDelayFormRef = ref() // 申请延期表单的 Ref
const handleDelayApply = async (task: any) => {
  try {
    // 获取流程实例ID
    const processInstanceId = processInstance.value?.id
    // 获取办理时限值
    const deadline = processInstance.value?.formVariables?.blsx || ''
    
    // 打开申请延期表单，传递任务ID、流程实例ID和办理时限
    taskDelayFormRef.value?.open(task.id, processInstanceId, deadline)
    console.log('打开申请延期表单, 任务ID:', task.id, '流程实例ID:', processInstanceId, '办理时限:', deadline)
  } catch (e) {
    message.error('申请延期失败：' + e.message)
  }
}
const addGetProcessInstanceApi = (data) => {
  formCreate.extendApi((api) => {
    api.getProcessInstance = function() {
      // 执行自定义操作
      console.log('这是一个自定义 API 方法',data);
      return data
    }
  })
}

// 新增方法：只加载任务列表，不影响表单数据
const getTaskListOnly = async () => {
  try {
    // 获得未取消的任务
    tasksLoad.value = true
    const data = await TaskApi.getTaskListByProcessInstanceId(id)
    tasks.value = []
    // 1.1 移除已取消的审批
    data.forEach((task) => {
      if (task.status !== 4) {
        tasks.value.push(task)
      }
    })
    // 1.2 排序，将未完成的排在前面，已完成的排在后面；
    tasks.value.sort((a, b) => {
      // 有已完成的情况，按照完成时间倒序
      if (a.endTime && b.endTime) {
        return b.endTime - a.endTime
      } else if (a.endTime) {
        return 1
      } else if (b.endTime) {
        return -1
        // 都是未完成，按照创建时间倒序
      } else {
        return b.createTime - a.createTime
      }
    })
    
    // 只在初始化时更新运行中的任务列表，避免影响已填写的表单
    if (runningTasks.value.length === 0) {
      loadRunningTask(tasks.value)
    }
  } finally {
    tasksLoad.value = false
  }
}

// 新增方法：根据任务ID查找索引
const findIndexByTaskId = (taskId) => {
  return runningTasks.value.findIndex(task => task.id === taskId)
}

// 是否为新闻流程的计算属性
const isNewsProcess = computed(() => {
  const key = processInstance.value?.processDefinition?.key || '';
  // 判断是否是新闻流程，根据key名称判断
  return key.includes('news') || processInstance.value?.category === 'oa_news';
})

/** 预览新闻 */
const previewNews = () => {
  if (!detailForm.value.value) {
    message.error('表单数据不存在');
    return;
  }
  
  try {
    // 存储到localStorage
    localStorage.setItem('newsPreviewData', JSON.stringify(detailForm.value.value));
    
    // 使用router.push导航到新闻详情页（在新窗口中打开）
    const routeData = router.resolve({
      name: 'newsPage',
      query: { preview: 'true' }
    });
    window.open(routeData.href, '_blank');
  } catch (error) {
    console.error('预览失败:', error);
    message.error('预览失败，请检查表单数据');
  }
}

/** 预览审批表单中的新闻 */
const previewApproveNews = (index) => {
  // 使用当前审批任务的表单数据
  const formData = approveForms[index]?.value;
  if (!formData) {
    // 如果审批表单没有数据，则使用主表单数据
    previewNews();
    return;
  }
  
  try {
    // 存储到localStorage
    localStorage.setItem('newsPreviewData', JSON.stringify(formData));
    
    // 使用router.push导航到新闻详情页（在新窗口中打开）
    const routeData = router.resolve({
      name: 'newsPage',
      query: { preview: 'true' }
    });
    window.open(routeData.href, '_blank');
  } catch (error) {
    console.error('预览失败:', error);
    message.error('预览失败，请检查表单数据');
  }
}

/** 查看上级流程详情操作 */
const handleViewParentDetail = async () => {
  try {
    if (!processInstance.value || !processInstance.value.id) {
      message.warning('无法获取流程信息')
      return
    }
    
    // 先调用API获取详细数据
    console.log("查看上级流程详情，获取实例数据中...", processInstance.value.id)
    const processDetail = await ProcessInstanceApi.getProcessInstance(processInstance.value.id)
    
    // 从返回的数据中提取lcid字段
    const parentProcessId = processDetail.formVariables?.lcid
    if (!parentProcessId) {
      message.warning('无法获取上级流程信息')
      return
    }
    
    console.log("获取到上级流程ID:", parentProcessId)
    
    // 跳转到流程实例详情页面
    router.push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: parentProcessId,
        hideApproval: 'true'
      }
    })
  } catch (error) {
    console.error("获取流程详情失败:", error)
    message.error('获取流程详情失败')
  }
}

// 检查是否为问题线索申请延期流程
const isDelayClueBpm = computed(() => {
  const processDefinitionId = processInstance.value?.processDefinition?.key
  return processDefinitionId === 'wtxssqyq_bpm'
})

</script>
