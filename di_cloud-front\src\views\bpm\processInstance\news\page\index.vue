<template>
  <!-- 头部行，包含logo和发布时间 -->
  <el-row class="mt-8px" :gutter="8" justify="space-between" style="border-bottom: 1px solid #0073c2;">
    <el-col :xl="16" :lg="16" :md="24" :sm="24" :xs="24" class="mb-8px">
      <!-- 网站logo -->
      <img src="@/assets/imgs/adminlogo.png" />
    </el-col>
    <el-col :xl="2" :lg="2" :md="24" :sm="24" :xs="24" class="mb-8px">
      <!-- 格式化的发布时间 -->
      <span>{{ formatTime(news_publish_date, 'yyyy-MM-dd') }}</span>
    </el-col>
  </el-row>

  <!-- 新闻标题 -->
  <el-row justify="center" style="margin-top: 30px;">
    <h1 style="font-size: 20px;">{{ news_title }}</h1>
  </el-row>

  <!-- 发布者信息 -->
  <el-row justify="center" style="margin-top: 10px; margin-bottom: 20px;">
    <span style="font-size: 16px; color: grey;">{{ news_publisher }}</span>
  </el-row>

  <!-- 新闻内容区域 -->
  <el-row style="border-top: 3px solid #d5002e; padding-top: 60px;">
    <div class="news-content-wrapper">
      <div class="news-content" v-html="news_content"></div>
    </div>
  </el-row>
  <!-- <el-row>
    <form-create
    v-model="formData"
    v-model:api="fapi"
    :rule="rule"
    :option="option"
  />
  </el-row> -->
</template>

<script lang="ts" setup>
import { formatTime } from '@/utils';  // 引入格式化时间的工具函数
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';  // 字典类型和字典选项的工具函数
import * as ProcessInstanceApi from '@/api/bpm/processInstance';  // 引入API接口
import { get } from 'http';  // 引入http模块
import { ref, reactive, onMounted } from 'vue';  // 引入Vue中的响应式API
import { useRoute } from 'vue-router'; // 引入vue-router的useRoute钩子

// 定义接口类型
interface NewsData {
  content: string;
  title: string;
  news_publish_date?: string | Date;
  publisher?: string;
  publishDate?: string | Date;  // 添加publishDate字段
}

interface ProcessInstanceData {
  formVariables: {
    news_content: string;
    news_title: string;
    news_publish_date?: string | Date;
    news_publisher?: string;
  };
}

interface QueryParams {
  pageNo: number;
  pageSize: number;
  name: string;
  processDefinitionKey?: string | undefined;
  category: string;
  status: number;
  createTime: Array<string | Date>;
}

// 获取路由查询参数
const route = useRoute();
const id = route.query.id as string;  // 从路由查询参数中提取ID并断言为string类型
const preview = route.query.preview === 'true'; // 判断是否是预览模式
const category = ref<string>('oa_news');  // 新闻类别
const news_content = ref<string>('');  // 新闻内容
const news_title = ref<string>('');  // 新闻标题
const news_publisher = ref<string>('');  // 新闻发布者
const news_publish_date = ref<Date>(new Date());  // 新闻发布日期

// const option = ref(formCreate.parseJson('{"language":{"zh-cn":{"Az87OmQS":"商品名称","BAVvUidu":"商品价格","CkD1fG2H":"商品描述","DgH2iJ3K":"库存数量","EhI3jK4L":"发货方式","FiJ4kL5M":"配送时间","GjK5lM6N":"用户评价","HkL6mN7O":"添加到购物车","IkM7nO8P":"立即购买","JlN8oP9Q":"优惠活动","KmO9pQ0R":"搜索商品","LnP0qR1S":"分类","MoQ1rS2T":"品牌","NpR2sT3U":"付款方式","OqS3tU4V":"订单确认","PrT4uV5W":"用户注册","QsU5vW6X":"用户登录","RtV6wX7Y":"联系客服","SuW7xY8Z":"退出登录","TvX8yZ9A":"个人信息","UwY9zA0B":"购物车","VxZ0aB1C":"结算","WyA1bC2D":"运费","XzB2cD3E":"订单状态","YaC3dE4F":"支付成功","ZbD4eF5G":"支付失败"},"en":{"Az87OmQS":"Goods name","BAVvUidu":"Goods price","CkD1fG2H":"Product description","DgH2iJ3K":"Stock quantity","EhI3jK4L":"Shipping method","FiJ4kL5M":"Delivery time","GjK5lM6N":"User reviews","HkL6mN7O":"Add to cart","IkM7nO8P":"Buy now","JlN8oP9Q":"Promotions","KmO9pQ0R":"Search products","LnP0qR1S":"Category","MoQ1rS2T":"Brand","NpR2sT3U":"Payment method","OqS3tU4V":"Order confirmation","PrT4uV5W":"User registration","QsU5vW6X":"User login","RtV6wX7Y":"Contact customer service","SuW7xY8Z":"Logout","TvX8yZ9A":"Personal information","UwY9zA0B":"Shopping cart","VxZ0aB1C":"Checkout","WyA1bC2D":"Shipping fee","XzB2cD3E":"Order status","YaC3dE4F":"Payment successful","ZbD4eF5G":"Payment failed"}},"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"125px"},"globalEvent":{"event_Feq4lui56zxbabc":{"label":"自定义","deletable":false,"handle":"[[FORM-CREATE-PREFIX-function handle($inject){aaa;}-FORM-CREATE-SUFFIX]]"},"event_Feq4lui56zxbab2c":{"label":"自定义2","handle":"[[FORM-CREATE-PREFIX-function(e){console.log(e)}-FORM-CREATE-SUFFIX]]"}},"globalData":{"data_Fk6dlui4k0xuabc":{"label":"自定义数据","type":"static","data":[1,2,3,4]},"data_Fs1elui4kttlacc":{"action":"http://***********:8081/","deletable":false,"method":"GET","headers":{},"data":{},"parse":"[[FORM-CREATE-PREFIX-function parse(res){return res.data;}-FORM-CREATE-SUFFIX]]","onError":"[[FORM-CREATE-PREFIX-function onError(e){}-FORM-CREATE-SUFFIX]]","label":"自定义接口数据","type":"fetch"}},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"},"globalVariable":{"var_Fppdlz6gytmzb1c":{"label":"token","deletable":false,"handle":"[[FORM-CREATE-PREFIX-function(e,n){return e(\\"$cookie.token\\")||\\"default Token\\"}-FORM-CREATE-SUFFIX]]"}},"globalClass":{"cls_Fzmulzw3u0oib0c":{"label":"fff","deletable":false,"style":{"color":"red"}}}}'));
// const rule = ref(formCreate.parseJson('[{"type":"fcEditor","title":"","field":"richText","_fc_id":"id_Fgtwm7tsdenaacc","name":"ref_Ftchm7tsdenaadc","display":true,"hidden":false,"_fc_drag_tag":"fcEditor","props":{"readonly":true}}]'));
// const rule = ref(formCreate.parseJson('[{"type":"fcEditor","field":"F3aqm7tvdlznakc","title":"","info":"","$required":false,"props":{"disabled":true},"_fc_id":"id_F7o9m7tvdlznalc","name":"ref_Fheym7tvdlznamc","display":true,"hidden":false,"_fc_drag_tag":"fcEditor"}]'))
// const option = ref(formCreate.parseJson('{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"125px"},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":true,"innerText":"提交"}}'))
    
// const fapi = ref(null);
// const formData = ref({});

// 设置查询参数的响应式对象
const queryParams = reactive<QueryParams>({
  pageNo: 1,  // 当前页码
  pageSize: 10,  // 每页显示的数据量
  name: '',  // 搜索名称
  processDefinitionKey: undefined,  // 进程定义的键值
  category: '',  // 新闻类别
  status: 0,  // 状态
  createTime: [],  // 创建时间范围
});

// 获取数据的方法
const getData = async (): Promise<void> => {
  // 调用API接口获取新闻详情数据
  const data: ProcessInstanceData = await ProcessInstanceApi.getProcessInstance(id);
  // console.log("获取的data",data);
  
  // 将获取到的新闻内容、标题、发布时间和发布者赋值给响应式数据
  news_content.value = data.formVariables.news_content;
  news_title.value = data.formVariables.news_title;
  news_publish_date.value = new Date(data.formVariables.news_publish_date || new Date()); 
  news_publisher.value = data.formVariables.news_publisher || '';
    
  // rule.value[0].value = news_content.value
  // rule.value[0].props.readonly=true
  // console.log("rule",rule.value)
  // option.value.preview = true
};

// 获取数据的方法
const getNewsData = async (): Promise<void> => {
  // 调用API接口获取新闻详情数据
  const data: NewsData = await ProcessInstanceApi.getNewsById({ id });
  // console.log("获取的data",data);
  
  // 将获取到的新闻内容、标题、发布时间和发布者赋值给响应式数据
  news_content.value = data.content;
  news_title.value = data.title;
  // 优先使用publishDate字段，如果不存在则回退到news_publish_date
  news_publish_date.value = new Date(data.publishDate || data.news_publish_date || new Date());
  news_publisher.value = data.publisher || '';
};

// 从本地存储获取预览数据
const getPreviewData = (): void => {
  try {
    const previewData = JSON.parse(localStorage.getItem('newsPreviewData') || '{}');
    news_content.value = previewData.news_content || previewData.news_content_secondunit ||'';
    news_title.value = previewData.news_title||previewData.news_title_secondunit || '';
    news_publish_date.value = previewData.news_publish_date||previewData.news_publish_date_secondunit ? new Date(previewData.news_publish_date||previewData.news_publish_date_secondunit) : new Date();
    news_publisher.value = previewData.news_publisher ||previewData.news_publisher_secondunit || '';
    
    // 预览后清除本地存储的数据，避免影响下次查看
    localStorage.removeItem('newsPreviewData');
  } catch (error) {
    console.error('解析预览数据失败:', error);
  }
};

// 决定采用哪种方式获取数据
onMounted(() => {
  if (preview) {
    // 如果是预览模式，直接使用本地存储的表单数据
    getPreviewData();
  } else if (id) {
    // 如果提供了ID，则从服务器获取数据
    getNewsData();
  }
});

// rule.value[0].value = news_content.value

</script>

<style>
/* 新闻内容容器样式 */
.news-content-wrapper {
  padding: 0 250px 0 210px;
  width: 100%;
}

/* 为富文本内容添加样式，使其与编辑器中显示效果一致 */
.news-content {
  line-height: 1.5;
  font-size: 16px;
  color: #333;
}

/* wangEditor 常用标签样式重置，使其与编辑器中显示一致 */
.news-content h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 20px 0;
  color: #1e1e1e;
}

.news-content h2 {
  font-size: 24px;
  font-weight: bold;
  margin: 18px 0;
  color: #1e1e1e;
}

.news-content h3 {
  font-size: 20px;
  font-weight: bold;
  margin: 16px 0;
  color: #1e1e1e;
}

.news-content h4 {
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0;
  color: #1e1e1e;
}

.news-content h5 {
  font-size: 16px;
  font-weight: bold;
  margin: 12px 0;
  color: #1e1e1e;
}

.news-content p {
  margin: 16px 0;
  line-height: 1.7;
}

.news-content ul, .news-content ol {
  margin: 16px 0;
  padding-left: 25px;
}

.news-content li {
  margin: 8px 0;
}

.news-content blockquote {
  border-left: 4px solid #d0d0d0;
  padding: 8px 15px;
  margin: 16px 0;
  background-color: #f5f5f5;
  color: #666;
}

.news-content a {
  color: #1890ff;
  text-decoration: none;
}

.news-content a:hover {
  text-decoration: underline;
}

.news-content img {
  max-width: 100%;
  margin: 12px 0;
}

.news-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
  border: 1px solid #ddd;
}

.news-content th, .news-content td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.news-content th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.news-content pre {
  background-color: #f6f8fa;
  padding: 16px;
  border-radius: 4px;
  overflow: auto;
  margin: 16px 0;
}

.news-content code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 14px;
}

/* 响应式适配 */
@media screen and (max-width: 1200px) {
  .news-content-wrapper {
    padding: 0 120px;
  }
}

@media screen and (max-width: 768px) {
  .news-content-wrapper {
    padding: 0 20px;
  }
}
</style>
