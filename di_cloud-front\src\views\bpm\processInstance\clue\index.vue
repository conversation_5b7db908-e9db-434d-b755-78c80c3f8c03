<template>
  <div class="clue-monitor">
    <!-- 查询区域 -->
    <div class="search-area" >
      <el-card shadow="never" class="search-card">
        <div class="filter-container">
          <div class="filter-header" @click="toggleFilter">
            <!-- <span>搜索条件</span> -->
            <el-icon class="filter-icon" :class="{ 'is-active': showFilter }">
              <ArrowDown />
            </el-icon>
          </div>
          <el-collapse-transition>
            <div v-show="showFilter" class="filter-content">
              <el-form ref="filterFormRef" :model="queryParams" inline class="filter-form">
                <div class="form-items">
                  <el-form-item v-if="hasUnitRangePermission" label="单位范围">
                    <el-select
                      v-model="queryParams.organizingDeptId"
                      placeholder="请选择单位范围"
                      clearable
                      style="width: 240px"
                    >
                      <el-option
                        v-for="item in unitOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id || ''"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="统计时间范围">
                    <el-date-picker
                      v-model="queryParams.dateRange"
                      type="daterange"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="YYYY-MM-DD"
                      style="width: 360px"
                      clearable
                    />
                  </el-form-item>
                </div>
                <div class="form-buttons">
                  <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
                  <el-button :icon="Refresh" @click="handleReset">重置</el-button>
                </div>
              </el-form>
            </div>
          </el-collapse-transition>
        </div>
      </el-card>
    </div>

    <!-- 主要KPI看板区域 -->
    <div class="statistics-area" v-if="false">
      <el-row :gutter="20">
        <!-- 线索总数 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="4">
          <el-card shadow="hover" class="kpi-card">
            <template #header>
              <div class="kpi-header">
                <span>{{ displayText.totalLabel }}</span>
                <el-tooltip :content="'统计范围内的' + displayText.clueLabel + '总计数'" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="kpi-content">
              <div class="kpi-value">{{ statisticsData?.total?.totalCount ?? '-' }}</div>
              <div class="kpi-trend" v-if="staticData.kpiData.main.trends.totalClues">
                <!-- <span :class="{'trend-up': staticData.kpiData.main.trends.totalClues.includes('+'), 'trend-down': staticData.kpiData.main.trends.totalClues.includes('-')}">
                  <el-icon><component :is="staticData.kpiData.main.trends.totalClues.includes('+') ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ staticData.kpiData.main.trends.totalClues }}
                </span> -->
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 在办线索数 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="4">
          <el-card shadow="hover" class="kpi-card">
            <template #header>
              <div class="kpi-header">
                <span>在办{{ displayText.clueLabel }}数</span>
                <el-tooltip :content="'当前正在处理中的' + displayText.clueLabel + '数量'" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="kpi-content">
              <div class="kpi-value">{{ statisticsData?.total?.runningCount ?? '-' }}</div>
              <div class="kpi-trend" v-if="staticData.kpiData.main.trends.inProgress">
                <!-- <span :class="{'trend-up': staticData.kpiData.main.trends.inProgress.includes('+'), 'trend-down': staticData.kpiData.main.trends.inProgress.includes('-')}">
                  <el-icon><component :is="staticData.kpiData.main.trends.inProgress.includes('+') ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ staticData.kpiData.main.trends.inProgress }}
                </span> -->
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 按期办结率 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="4">
          <el-card shadow="hover" class="kpi-card">
            <template #header>
              <div class="kpi-header">
                <span>按期办结率</span>
                <el-tooltip content="按期完成的线索占比" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="kpi-content">
              <div class="kpi-value">{{ (statisticsData?.total?.approvedRate ? Number(statisticsData.total.approvedRate) * 100 : 0).toFixed(2) }}%</div>
              <div class="kpi-trend" v-if="staticData.kpiData.main.trends.onTimeCompletionRate">
                <!-- <span :class="{'trend-up': staticData.kpiData.main.trends.onTimeCompletionRate.includes('+'), 'trend-down': staticData.kpiData.main.trends.onTimeCompletionRate.includes('-')}">
                  <el-icon><component :is="staticData.kpiData.main.trends.onTimeCompletionRate.includes('+') ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ staticData.kpiData.main.trends.onTimeCompletionRate }}
                </span> -->
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 即将到期 -->
        <!-- <el-col :xs="24" :sm="12" :md="8" :lg="4">
          <el-card shadow="hover" class="kpi-card warning">
            <template #header>
              <div class="kpi-header">
                <span>即将到期</span>
                <el-tooltip content="距离到期不足30天的线索数量" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="kpi-content">
              <div class="kpi-value">{{ staticData.kpiData.main.warningCount }}</div>
              <div class="kpi-trend" v-if="staticData.kpiData.main.trends.warningCount">
                <span :class="{'trend-up': staticData.kpiData.main.trends.warningCount.includes('+'), 'trend-down': staticData.kpiData.main.trends.warningCount.includes('-')}">
                  <el-icon><component :is="staticData.kpiData.main.trends.warningCount.includes('+') ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ staticData.kpiData.main.trends.warningCount }}
                </span>
              </div>
            </div>
          </el-card>
        </el-col> -->

        <!-- 已超时 -->
        <!-- <el-col :xs="24" :sm="12" :md="8" :lg="4">
          <el-card shadow="hover" class="kpi-card danger">
            <template #header>
              <div class="kpi-header">
                <span>已超时</span>
                <el-tooltip content="已超过办理时限的线索数量" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="kpi-content">
              <div class="kpi-value">{{ staticData.kpiData.main.overdueCount }}</div>
              <div class="kpi-trend" v-if="staticData.kpiData.main.trends.overdueCount">
                <span :class="{'trend-up': staticData.kpiData.main.trends.overdueCount.includes('+'), 'trend-down': staticData.kpiData.main.trends.overdueCount.includes('-')}">
                  <el-icon><component :is="staticData.kpiData.main.trends.overdueCount.includes('+') ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ staticData.kpiData.main.trends.overdueCount }}
                </span>
              </div>
            </div>
          </el-card>
        </el-col> -->

        <!-- 本期新增 -->
        <!-- <el-col :xs="24" :sm="12" :md="8" :lg="4">
          <el-card shadow="hover" class="kpi-card">
            <template #header>
              <div class="kpi-header">
                <span>本期新增</span>
                <el-tooltip content="统计周期内新增的线索数量" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="kpi-content">
              <div class="kpi-value">{{ staticData.kpiData.main.newInPeriod }}</div>
              <div class="kpi-trend" v-if="staticData.kpiData.main.trends.newInPeriod">
                <span :class="{'trend-up': staticData.kpiData.main.trends.newInPeriod.includes('+'), 'trend-down': staticData.kpiData.main.trends.newInPeriod.includes('-')}">
                  <el-icon><component :is="staticData.kpiData.main.trends.newInPeriod.includes('+') ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                  {{ staticData.kpiData.main.trends.newInPeriod }}
                </span>
              </div>
            </div>
          </el-card>
        </el-col> -->
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-area">
      
      <!-- 信访举报方式图表风格选择器 -->
      <el-card shadow="hover" style="margin-top: 20px;">
        <!-- <template #header>
          <div class="chart-header">
            <span>多维图表</span>
          </div>
        </template> -->
        
        <!-- 图表展示区域 -->
        <report-methods-tech-chart 
          ref="reportChartRef"
          :dept-id="queryParams.organizingDeptId"
          :start-date="queryParams.dateRange ? queryParams.dateRange[0] : ''"
          :end-date="queryParams.dateRange ? queryParams.dateRange[1] : ''"
          :category="category"
          @update:category="(val) => console.log('组件收到category更新:', val)"
        />
        {{ console.log('传递给ReportMethodsTechChart的category:', category) }}
      </el-card>
    </div>

    <!-- 次要指标区域 -->
    <div class="secondary-kpi-area" v-if="false">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-statistic :value="statisticsData?.total?.totalCount || 0" :title="displayText.totalLabel">
            <template #suffix>
              <el-icon><Tickets /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-statistic :value="statisticsData?.total?.runningCount || 0" title="办理中">
            <template #suffix>
              <el-icon><Clock /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-statistic :value="statisticsData?.total?.approvedCount || 0" title="办结数">
            <template #suffix>
              <el-icon><Select /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <!-- <el-col :xs="24" :sm="12" :md="6">
          <el-statistic :value="statisticsData?.total?.rejectedCount || 0" title="审批不通过数">
            <template #suffix>
              <el-icon><CircleClose /></el-icon>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 15px;">
        <el-col :xs="24" :sm="12" :md="6">
          <el-statistic :value="statisticsData?.total?.cancelCount || 0" title="已取消数">
            <template #suffix>
              <el-icon><Delete /></el-icon>
            </template>
          </el-statistic>
        </el-col> -->
        <el-col :xs="24" :sm="12" :md="6">
          <el-statistic 
            :value="(statisticsData?.total?.approvedRate ? Number(statisticsData.total.approvedRate) * 100 : 0).toFixed(2)" 
            :precision="2"
            title="办结率"
            suffix="%"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 列表区域 -->
    <div class="list-area" v-if="false">
      <el-card shadow="never">
        <el-table :data="tableData" stripe border style="width: 100%">
          <el-table-column :label="displayText.numberLabel" width="100" fixed="left">
            <template #default="{ row }">
              <el-link type="primary" @click="handleDetailClick(row)">{{ row.caseNumber || '-' }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="processInstanceTitle" :label="displayText.clueLabel + '名称'" min-width="200" show-overflow-tooltip />
          <el-table-column prop="deptName" label="流程发起单位" width="140" show-overflow-tooltip />
          <el-table-column label="流程状态" width="90">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="row.processInstanceStatus" />
            </template>
          </el-table-column>
          <!-- <el-table-column prop="status" :label="displayText.statusLabel" width="120" /> -->
          <el-table-column v-if="category === 'oa_xf'" prop="processXfCategory" label="信访类别" width="120">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.BPM_XF_CATEGORY" :value="row.processXfCategory" />
            </template>
          </el-table-column>
          <el-table-column v-if="category === 'oa_xf'" prop="processXfDeptName" label="承办单位" width="140" show-overflow-tooltip />
          <el-table-column v-if="category === 'oa_xf'" prop="processXfRegistrationDate" label="登记日期" width="150" sortable />
          <el-table-column v-if="category === 'oa_xs'" prop="organizeUserDeptName" label="承办单位" width="150" show-overflow-tooltip />
          <el-table-column v-if="category === 'oa_xs'" label="承办人员" width="90">
            <template #default="{ row }">
              {{ row.organizeUserName || '' }}
            </template>
          </el-table-column>
          <el-table-column v-if="category === 'oa_xs'" label="审核人员" width="90">
            <template #default="{ row }">
              {{ getUserNameById(row.reviewerUserId) }}
            </template>
          </el-table-column>
          <el-table-column v-if="category === 'oa_xs'" prop="processInstanceAcceptanceDate" label="受理时间" width="110" sortable />
          <el-table-column v-if="category === 'oa_xs'" label="剩余时限" width="120" sortable prop="remainingDays">
            <template #default="{ row }">
              <template v-if="row.processInstanceStatus !== 2 && row.processInstanceLimitDate">
                <el-tooltip
                  :content="`时限日期: ${row.processInstanceLimitDate}`"
                  placement="top"
                >
                  <el-tag :type="getTimeLimitTagType(row)" size="small">
                    {{ formatTimeLimitText(row) }}
                  </el-tag>
                </el-tooltip>
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link size="small" @click="handleDetailClick(row)">详情</el-button>
              <el-button
                v-if="row.processInstanceStatus !== 2 && !['已完结(办结)', '已完结(立案)', '已完结(终止)'].includes(row.status)"
                type="primary"
                link
                size="small"
                @click="handleUrgeClick(row)"
              >催办</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          :current-page="currentPage" 
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalItems"
          layout="total, sizes, prev, pager, next, jumper"
          background
          style="margin-top: 20px; justify-content: flex-end"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @update:current-page="val => currentPage = val"
          @update:page-size="val => pageSize = val"
        />
      </el-card>
    </div>
  <!-- API调试抽屉 -->
  <el-drawer
    v-model="debugDrawer"
    title="API调试面板"
    direction="rtl"
    size="50%"
  >
    <div class="debug-panel">
      <el-form :model="debugForm" label-width="120px">
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="debugForm.startTime"
            type="date"
            placeholder="选择开始日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            v-model="debugForm.endTime"
            type="date"
            placeholder="选择结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="承办单位ID">
          <el-input v-model="debugForm.organizingDeptId" />
        </el-form-item>
        <el-form-item label="线索编号">
          <el-input v-model="debugForm.clueId" />
        </el-form-item>
        <el-form-item label="线索状态">
          <el-select v-model="debugForm.status" multiple placeholder="请选择线索状态">
            <el-option
              v-for="item in clueStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="承办人员">
          <el-input v-model="debugForm.handler" />
        </el-form-item>
        <el-form-item label="时限状态">
          <el-select v-model="debugForm.timeLimitStatus" placeholder="请选择时限状态" clearable>
            <el-option
              v-for="item in timeLimitStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="页码">
          <el-input-number v-model="debugForm.pageNo" :min="1" />
        </el-form-item>
        <el-form-item label="每页条数">
          <el-input-number v-model="debugForm.pageSize" :min="1" />
        </el-form-item>
      </el-form>

      <div class="debug-actions">
        <el-button type="primary" @click="handleDebugSend">发送请求</el-button>
        <el-button @click="handleDebugReset">重置参数</el-button>
      </div>

      <div class="debug-result">
        <el-alert
          v-if="debugResult.error"
          :title="debugResult.error"
          type="error"
          show-icon
        />
        <div v-else>
          <el-descriptions title="请求结果" :column="1" border>
            <el-descriptions-item label="状态">
              {{ debugResult.success ? '成功' : '失败' }}
            </el-descriptions-item>
            <el-descriptions-item label="响应时间">
              {{ debugResult.time }}ms
            </el-descriptions-item>
          </el-descriptions>
          
          <el-collapse v-model="activeDebugPanel" class="debug-response">
            <!-- 总体统计数据 -->
            <el-collapse-item title="总体统计数据" name="total">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="线索总数">
                  {{ debugResult.data?.total?.totalCount ?? '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="在办线索数">
                  {{ debugResult.data?.total?.runningCount ?? '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="已审核通过数">
                  {{ debugResult.data?.total?.approvedCount ?? '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="已驳回数">
                  {{ debugResult.data?.total?.rejectedCount ?? '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="已取消数">
                  {{ debugResult.data?.total?.cancelCount ?? '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="按期办结率">
                  {{ debugResult.data?.total?.approvedRate !== undefined ? `${(Number(debugResult.data.total.approvedRate) * 100).toFixed(2)}%` : '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>

            <!-- 分页数据概览 -->
            <el-collapse-item title="分页数据概览" name="page">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="当前页数据条数">
                  {{ debugResult.data?.page?.list?.length ?? 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="数据示例">
                  <el-descriptions :column="1" border size="small">
                    <el-descriptions-item label="线索编号">
                      {{ debugResult.data?.page?.list?.[0]?.caseNumber ?? '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="线索标题">
                      {{ debugResult.data?.page?.list?.[0]?.processInstanceTitle ?? '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="承办单位">
                      {{ debugResult.data?.page?.list?.[0]?.deptName ?? '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="承办人">
                      {{ debugResult.data?.page?.list?.[0]?.organizeUserName || '' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="审核人员">
                      {{ getUserNameById(debugResult.data?.page?.list?.[0]?.reviewerUserId) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="受理日期">
                      {{ debugResult.data?.page?.list?.[0]?.processInstanceAcceptanceDate ?? '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="category === 'oa_xf'" label="信访类别">
                      <dict-tag
                        :type="DICT_TYPE.BPM_XF_CATEGORY"
                        :value="debugResult.data?.page?.list?.[0]?.processXfCategory"
                      />
                    </el-descriptions-item>
                  </el-descriptions>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>

            <!-- 原始响应数据 -->
            <el-collapse-item title="原始响应数据" name="response">
              <pre>{{ JSON.stringify(debugResult.data, null, 2) }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </el-drawer>    
  </div>


</template>

<script setup lang="ts">
import {
  Search, Refresh, QuestionFilled, ArrowDown, ArrowUp,
  Finished, Delete, Select, CircleClose, Tickets, Clock, Warning, Monitor, PieChart
} from '@element-plus/icons-vue'
import { ref, reactive, onMounted, computed, nextTick, onUnmounted, watch, watchEffect } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import * as echarts from 'echarts'
import { getOrganizingDeptList } from '@/api/system/dept'
import { getStatistics } from '@/api/bpm/clue'
import { checkPermi } from '@/utils/permission'
import { categoryRoute } from '@/api/bpm/category'
import { DICT_TYPE, getDictLabel, getIntDictOptions } from '@/utils/dict'
import { DictTag } from '@/components/DictTag'
import { getSimpleUserList } from '@/api/system/user'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import ReportMethodsTechChart from './components/ReportMethodsTechChart.vue'

const router = useRouter()
const route = useRoute()

// 当前分类（线索件/信访件）
const category = ref(route.query.category?.toString() || '')

// 监听路由参数变化
watch(
  () => route.query.category,
  (newCategory) => {
    console.log('路由category参数变化:', newCategory)
    category.value = newCategory?.toString() || ''
  }
)

// 用户列表缓存
const userList = ref([])

// 加载用户列表
const loadUserList = async () => {
  try {
    const res = await getSimpleUserList()
    userList.value = res
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 根据用户ID获取用户名称
const getUserNameById = (userId) => {
  if (!userId) return '-'
  const user = userList.value.find(item => item.id === userId)
  return user ? user.nickname : String(userId)
}

// 根据分类获取显示文本
const displayText = computed(() => ({
  title: category.value === 'oa_xs' ? '线索件监控' : '信访件监控',
  clueLabel: category.value === 'oa_xs' ? '线索' : '信访',
  numberLabel: category.value === 'oa_xs' ? '线索编号' : '信访编号',
  statusLabel: category.value === 'oa_xs' ? '线索状态' : '信访状态',
  totalLabel: category.value === 'oa_xs' ? '线索总数' : '信访总数'
}))

// 统计数据
const statisticsData = ref()

// 单位选项
const unitOptions = ref([])

// 获取单位列表
const getUnitOptions = async () => {
  try {
    const res = await getOrganizingDeptList()
    unitOptions.value = res
  } catch (error) {
    console.error('获取单位列表失败:', error)
    ElMessage.error('获取单位列表失败')
  }
}

// 查询参数
const queryParams = reactive({
  dateRange: undefined,
  organizingDeptId: '',
  clueId: '',
  status: [],
  handler: '',
  acceptanceDateRange: undefined,
  timeLimitStatus: ''
})

// 设置默认日期范围：当年1月1日到当天
const setDefaultDateRange = () => {
  if (!queryParams.dateRange) {
    const currentDate = new Date();
    const defaultStartDate = `${currentDate.getFullYear()}-01-01`;
    
    // 创建第二天的日期对象
    const nextDate = new Date(currentDate);
    nextDate.setDate(currentDate.getDate() + 1);
    const defaultEndDate = nextDate.toISOString().split('T')[0]; // 格式：YYYY-MM-DD
    
    queryParams.dateRange = [defaultStartDate, defaultEndDate];
  }
}

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
const tableData = ref([])

// 删除折叠面板状态
// const activeNames = ref([])
const filterFormRef = ref()

// 控制筛选条件的显示/隐藏
const showFilter = ref(true)
const toggleFilter = () => {
  showFilter.value = !showFilter.value
}

// 线索状态选项
const clueStatusOptions = computed(() => {
  if (category.value === 'oa_xs') {
    return [
      { label: '线索登记', value: '登记' },
      { label: '集体研判', value: '研判' },
      { label: '集团审核', value: '审核' },
      { label: '初步核实', value: '初核' },
      { label: '谈话', value: '谈话' },
      { label: '函询', value: '函询' },
      { label: '暂存待查', value: '暂存' },
      { label: '已完结(办结)', value: '办结' },
      { label: '已完结(立案)', value: '立案' },
      { label: '已完结(终止)', value: '终止' }
    ]
  } else {
    return [
      { label: '信访登记', value: '登记' },
      { label: '信访审核', value: '审核' },
      { label: '信访处理', value: '处理' },
      { label: '信访办结', value: '办结' },
      { label: '信访终止', value: '终止' }
    ]
  }
})

// 时限状态选项
const timeLimitStatusOptions = [
  { label: '绿灯 (>30天)', value: 'green' },
  { label: '黄灯 (1-30天)', value: 'yellow' },
  { label: '红灯 (<=0天)', value: 'red' }
]

// 检查是否有单位范围权限
const hasUnitRangePermission = computed(() => checkPermi(['bpm:clue:query']))

// 获取数据
const fetchData = async () => {
  // try {
  //   const [startTime, endTime] = queryParams.dateRange || []
  //   const res = await getStatistics({
  //     category: category.value,
  //     startTime: startTime || undefined,
  //     endTime: endTime || undefined,
  //     organizingDeptId: queryParams.organizingDeptId,
  //     clueId: queryParams.clueId,
  //     status: queryParams.status,
  //     handler: queryParams.handler,
  //     acceptanceDateRange: queryParams.acceptanceDateRange,
  //     timeLimitStatus: queryParams.timeLimitStatus,
  //     pageNo: currentPage.value,
  //     pageSize: pageSize.value
  //   })
    
  //   if (res) {
  //     statisticsData.value = res
  //     tableData.value = res.page?.list || []
  //     totalItems.value = res.page?.total || 0
  //   } else {
  //     throw new Error('API返回数据异常')
  //   }
  // } catch (error) {
  //   console.error('获取数据失败，详细错误:', error)
  //   ElMessage.error('获取数据失败')
  // }
}

// 查询按钮点击
const handleSearch = () => {
  currentPage.value = 1
  // fetchData()
  
  // 调用子组件的refreshData方法刷新图表数据
  console.log('点击搜索按钮，准备刷新图表数据')
  
  // 确保子组件的引用存在
  if (reportChartRef.value) {
    console.log('找到子组件引用，调用refreshData方法')
    const dateRange = queryParams.dateRange || []
    const params = {
      deptId: queryParams.organizingDeptId || '',
      startDate: dateRange[0] || '',
      endDate: dateRange[1] || ''
    }
    console.log('刷新图表使用的参数：', params)
    
    // 调用子组件的refreshData方法
    reportChartRef.value.refreshData()
  } else {
    console.warn('未找到ReportMethodsTechChart组件引用')
  }
}

// 重置按钮点击
const handleReset = () => {
  queryParams.dateRange = undefined
  queryParams.organizingDeptId = ''
  queryParams.clueId = ''
  queryParams.status = []
  queryParams.handler = ''
  queryParams.acceptanceDateRange = undefined
  queryParams.timeLimitStatus = ''
  filterFormRef.value?.resetFields()
  
  // 设置默认日期范围
  setDefaultDateRange()
  
  // 调用搜索按钮点击处理函数
  handleSearch()
}

// 分页事件
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 图表引用
const handlingUnitChartRef = ref()
const statusDistributionChartRef = ref()
const completionRateTrendChartRef = ref()
const reportChartRef = ref()

// 静态数据
const staticData = reactive({
  kpiData: {
    main: {
      totalClues: 152,
      inProgress: 78,
      onTimeCompletionRate: 85.5,
      warningCount: 15,
      overdueCount: 8,
      newInPeriod: 25,
      trends: {
        totalClues: '+10%',
        inProgress: '-5%',
        onTimeCompletionRate: '****%',
        warningCount: '+2',
        overdueCount: '-1',
        newInPeriod: '+3'
      }
    },
    secondary: {
      completedCount: 60,
      cancelledCount: 5,
      groupApprovedCount: 45,
      groupRejectedCount: 3
    }
  },
  chartData: {
    handlingUnit: [
      { name: '第一纪检监察室', value: 18 },
      { name: '第二纪检监察室', value: 25 },
      { name: '第三纪检监察室', value: 12 },
      { name: '第四纪检监察室', value: 8 },
      { name: 'XX公司纪委', value: 15 },
      { name: 'YY公司纪委', value: 10 },
      { name: 'ZZ公司纪委', value: 7 },
      { name: '总部机关纪委', value: 5 },
      { name: '其他单位A', value: 4 },
      { name: '其他单位B', value: 3 }
    ],
    statusDistribution: [
      { name: '初步核实', value: 30 },
      { name: '谈话函询', value: 20 },
      { name: '集体研判', value: 10 },
      { name: '集团审核', value: 8 },
      { name: '已完结(办结)', value: 55 },
      { name: '已完结(立案)', value: 5 },
      { name: '暂存待查', value: 12 },
      { name: '其他在办', value: 12 }
    ],
    completionRateTrend: [
      { month: '2024-10', rate: 88.0 },
      { month: '2024-11', rate: 85.5 },
      { month: '2024-12', rate: 86.0 },
      { month: '2025-01', rate: 87.5 },
      { month: '2025-02', rate: 85.0 },
      { month: '2025-03', rate: 84.0 }
    ]
  }
})

// 初始化图表
const initCharts = () => {
  // 办理主体分布图
  if (handlingUnitChartRef.value) {
    const chart = echarts.init(handlingUnitChartRef.value)
    chart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: staticData.chartData.handlingUnit.map(item => item.name)
      },
      series: [
        {
          name: '在办数量',
          type: 'bar',
          data: staticData.chartData.handlingUnit.map(item => item.value)
        }
      ]
    })
  }

  // 状态分布图
  if (statusDistributionChartRef.value) {
    const chart = echarts.init(statusDistributionChartRef.value)
    chart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: displayText.value.statusLabel,
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '12',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: staticData.chartData.statusDistribution
        }
      ]
    })
  }

  // 按期办结率趋势图
  if (completionRateTrendChartRef.value) {
    const chart = echarts.init(completionRateTrendChartRef.value)
    chart.setOption({
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: staticData.chartData.completionRateTrend.map(item => item.month),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '按期办结率',
          type: 'line',
          data: staticData.chartData.completionRateTrend.map(item => item.rate),
          smooth: true
        }
      ]
    })
  }
}

// 获取时限标签类型
const getTimeLimitTagType = (row) => {
  const remainingDays = calculateRemainingDays(row)
  if (remainingDays === null) return 'info'
  if (remainingDays > 30) return 'success'
  if (remainingDays > 0) return 'warning'
  return 'danger'
}

// 格式化时限文本
const formatTimeLimitText = (row) => {
  const remainingDays = calculateRemainingDays(row)
  if (remainingDays === null) return ''
  if (remainingDays > 30) return `剩余 ${remainingDays} 天`
  if (remainingDays > 0) return `剩余 ${remainingDays} 天`
  return `超时 ${Math.abs(remainingDays)} 天`
}

// 计算剩余天数
const calculateRemainingDays = (row) => {
  if (!row.processInstanceLimitDate) return null
  
  const limitDate = new Date(row.processInstanceLimitDate)
  const today = new Date()
  
  // 重置时间部分以获得准确的天数差异
  limitDate.setHours(0, 0, 0, 0)
  today.setHours(0, 0, 0, 0)
  
  const diffTime = limitDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return diffDays
}

// 详情按钮点击
const handleDetailClick = (row) => {
  router.push({
    path: '/bpm/process-instance/detail',
    query: {
      id: row.processInstanceId
    }
  })
}

// 催办按钮点击
const handleUrgeClick = (row) => {
  ElMessageBox.confirm(
    `确定要对${displayText.value.clueLabel} "${row.processInstanceTitle}" 发送催办通知吗？`,
    '催办确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        // 调用催办API
        await ProcessInstanceApi.urgeProcessInstances({
          processInstanceIds: [row.processInstanceId]
        })
        ElMessage.success(`已成功发送催办通知`)
      } catch (error) {
        console.error('催办失败:', error)
        ElMessage.error('催办失败，请稍后重试')
      }
    })
    .catch(() => {
      ElMessage.info('已取消催办')
    })
}

// 监听窗口大小变化，重绘图表
const handleResize = () => {
  if (handlingUnitChartRef.value) {
    echarts.getInstanceByDom(handlingUnitChartRef.value)?.resize()
  }
  if (statusDistributionChartRef.value) {
    echarts.getInstanceByDom(statusDistributionChartRef.value)?.resize()
  }
  if (completionRateTrendChartRef.value) {
    echarts.getInstanceByDom(completionRateTrendChartRef.value)?.resize()
  }
}

// 获取分类参数
const getCategory = async () => {
  try {
    const routeCategory = await categoryRoute.getCategoryByRouteParam(route)
    console.log('获取到的路由category:', routeCategory)
    if (routeCategory && ['oa_xs', 'oa_xf', 'oa_register', 'total'].includes(routeCategory as string)) {
      category.value = routeCategory as string
      console.log('设置category为:', category.value)
    } else {
      // 如果没有有效的category，设置为默认值
      category.value = 'total'
      console.log('没有有效的category，设置为默认值:', category.value)
    }
  } catch (error) {
    console.error('获取category失败:', error)
    category.value = 'total' // 出错时设置默认值
  }
}

// 初始化
onMounted(async () => {
  await getCategory()
  await getUnitOptions()
  await loadUserList()
  
  // 设置默认日期范围
  setDefaultDateRange()
  
  // 初始加载数据
  // fetchData()
  
  // 页面首次加载时，等待图表组件初始化完成后调用刷新数据
  nextTick(() => {
    // 点击搜索按钮获取数据，而不是自动获取
    handleSearch()
    initCharts()
    window.addEventListener('resize', handleResize)
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// API调试相关
const debugDrawer = ref(false)
const activeDebugPanel = ref(['total', 'page', 'response'])
const debugForm = reactive({
  category: computed(() => category.value),
  startTime: '',
  endTime: '',
  organizingDeptId: '',
  clueId: '',
  status: [],
  handler: '',
  timeLimitStatus: '',
  pageNo: 1,
  pageSize: 10
})

const debugResult = reactive({
  success: false,
  error: '',
  time: 0,
  data: null
})

const handleDebugSend = async () => {
  debugResult.error = ''
  debugResult.data = null
  
  const startTime = new Date().getTime()
  try {
    const res = await getStatistics({
      ...debugForm
    })
    debugResult.success = true
    debugResult.data = res
  } catch (error: any) {
    debugResult.success = false
    debugResult.error = error.message || '请求失败'
  } finally {
    debugResult.time = new Date().getTime() - startTime
  }
}

const handleDebugReset = () => {
  debugForm.startTime = ''
  debugForm.endTime = ''
  debugForm.organizingDeptId = ''
  debugForm.clueId = ''
  debugForm.status = []
  debugForm.handler = ''
  debugForm.timeLimitStatus = ''
  debugForm.pageNo = 1
  debugForm.pageSize = 10
  
  debugResult.success = false
  debugResult.error = ''
  debugResult.time = 0
  debugResult.data = null
}

// 监听路由变化
watch(() => route.query.category, async (newCategory) => {
  if (newCategory && ['oa_xs', 'oa_xf', 'oa_register', 'total'].includes(newCategory as string)) {
    category.value = newCategory as string
    await fetchData()
  }
})

// 修改页面标题
watchEffect(() => {
  document.title = displayText.value.title
})

// 跳转到二级看板
const viewSecondaryDashboard = () => {
  router.push({
    name: 'BpmSecondaryDashboard',
    query: {
      category: category.value,
      startTime: queryParams.dateRange ? queryParams.dateRange[0] : '',
      endTime: queryParams.dateRange ? queryParams.dateRange[1] : '',
      organizingDeptId: queryParams.organizingDeptId
    }
  })
}
</script>

<style scoped>
/* 添加自定义样式 */
.dashboard-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
  padding: 0 10px;
}

.tech-button {
  position: relative;
  overflow: hidden;
}

.tech-button::before {
  content: '';
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    top: -100%;
  }
  20% {
    top: 100%;
  }
  100% {
    top: 100%;
  }
}
</style>
