<template>
  <div class="category-page scroll-target" ref="categoryPageRef">
    <div class="page-content">
      <div class="category-header">
        <div class="title-container">
          <div class="title-decoration"></div>
          <h1 class="category-title">{{ categoryTitle }}</h1>
        </div>
      </div>

      <div v-if="newsList.length > 0" class="news-container">
        <el-row :gutter="24" class="news-list">
          <el-col v-for="news in newsList" :key="news.news_id" :span="24" class="news-item">
            <el-card shadow="hover" class="news-card" @click="viewDetails(news.news_id)">
              <div class="news-content">
                <div class="news-main">
                  <h3 class="news-title">
                    <span v-if="news.icon" class="hot-tag">热门</span>
                    {{ news.name }}
                  </h3>
                </div>
                <div class="news-info">
                  <div class="news-meta">
                    <el-icon><User /></el-icon>
                    <span class="meta-text">{{ news.personal }}</span>
                    <el-icon class="meta-icon"><Calendar /></el-icon>
                    <span class="meta-text">{{ formatTime(news.time, 'yyyy-MM-dd') }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <div class="pagination-container">
          <el-pagination
            background
            layout="total, prev, pager, next, jumper"
            :total="totalNews"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
          />
        </div>
      </div>

      <el-empty 
        v-else 
        description="暂无新闻" 
        :image-size="200"
        class="empty-container"
      >
        <el-button type="primary" @click="goHome">返回首页</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeMount, onActivated } from 'vue';
import { formatTime } from '@/utils';
import { useRoute, useRouter } from 'vue-router';
import { getNewsHomeData } from '@/api/bpm/processInstance';
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
import { Calendar, User } from '@element-plus/icons-vue';

const categoryTitle = ref('');
const allNewsList = ref([]);
const newsList = ref([]);
const totalNews = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);

const route = useRoute();
const router = useRouter();
const newsCategory = route.query.category;

const categoryPageRef = ref(null);

// 强制滚动到顶部的函数，使用多种方法尝试
const forceScrollToTop = () => {
  // 直接使用组件的ref控制滚动
  if (categoryPageRef.value) {
    categoryPageRef.value.scrollTop = 0;
  }
  
  // 方法1：使用window.scrollTo
  window.scrollTo(0, 0);
  
  // 方法2：直接设置scrollTop
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0; // 兼容Safari
  
  // 方法3：寻找可能的滚动容器并应用滚动
  const scrollContainers = [
    document.documentElement,
    document.body,
    document.querySelector('.category-page'),
    document.querySelector('.page-content'),
    document.querySelector('main'),
    document.querySelector('.app-main'),
    document.querySelector('#app'),
    ...Array.from(document.querySelectorAll('.el-scrollbar__wrap')),
    ...Array.from(document.querySelectorAll('.scrollbar')),
    ...Array.from(document.querySelectorAll('[class*="scroll"]')),
    ...Array.from(document.querySelectorAll('*')).filter(el => {
      const style = window.getComputedStyle(el);
      return style.overflow === 'auto' || style.overflow === 'scroll' || 
             style.overflowY === 'auto' || style.overflowY === 'scroll';
    })
  ];
  
  // 尝试对所有可能的滚动容器进行滚动操作
  scrollContainers.forEach(container => {
    if (container) {
      container.scrollTop = 0;
    }
  });
  
  // 方法4：使用setTimeout在下一个宏任务中尝试滚动
  setTimeout(() => {
    window.scrollTo(0, 0);
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
    
    // 再次尝试对所有可能的滚动容器进行滚动操作
    scrollContainers.forEach(container => {
      if (container) {
        container.scrollTop = 0;
      }
    });
  }, 0);
  
  // 方法5：延迟300ms后再次尝试
  setTimeout(() => {
    window.scrollTo(0, 0);
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
    
    // 再次尝试对所有可能的滚动容器进行滚动操作
    scrollContainers.forEach(container => {
      if (container) {
        container.scrollTop = 0;
      }
    });
  }, 300);
  
  // 额外确认: 如果我们有组件的ref，确保它滚动到顶部
  setTimeout(() => {
    if (categoryPageRef.value) {
      categoryPageRef.value.scrollTop = 0;
    }
  }, 600);
};

// 获取首页新闻数据
const fetchNewsData = async () => {
  try {
    // 先尝试滚动到顶部
    forceScrollToTop();
    
    const response = await getNewsHomeData({});
    const responseData = response?.data || response || {};
    
    // 处理新闻类别和列表
    const categoryData = responseData.newsCategory || [];
    if (Array.isArray(categoryData) && categoryData.length > 0) {
      let currentCategory = categoryData.find(category => category.category === newsCategory);
      
      // 如果没找到，尝试将newsCategory转为数字再匹配
      if (!currentCategory && !isNaN(Number(newsCategory))) {
        const numCategory = Number(newsCategory);
        currentCategory = categoryData.find(category => 
          Number(category.category) === numCategory || category.category === numCategory
        );
      }
      
      // 如果仍然没找到，尝试将newsCategory转为字符串再匹配
      if (!currentCategory) {
        const strCategory = String(newsCategory);
        currentCategory = categoryData.find(category => 
          String(category.category) === strCategory || category.category === strCategory
        );
      }
      
      // 更新类别标题
      if (currentCategory) {
        categoryTitle.value = currentCategory.name || '新闻列表';
      } else {
        categoryTitle.value = '新闻列表';
      }
      
      // 更新新闻列表
      let filteredNews = [];
      categoryData.forEach(category => {
        if (category.category === newsCategory && category.list && Array.isArray(category.list)) {
          filteredNews = category.list.map(item => ({
            name: item.title,
            icon: item.hot,
            message: item.summary || '点击查看详情...',
            personal: item.publisher,
            time: new Date(item.publishDate || item.createTime || new Date()),
            news_id: String(item.id),
            news_category: category.category
          }));
        }
      });
      
      allNewsList.value = filteredNews;
      updatePageData();
      
      // 数据加载完成后再次尝试滚动到顶部
      nextTick(() => {
        forceScrollToTop();
      });
    }
  } catch (error) {
    console.error('获取新闻分类数据失败:', error);
    categoryTitle.value = '新闻列表';
    allNewsList.value = [];
    newsList.value = [];
    totalNews.value = 0;
  }
};

// 更新分页数据
const updatePageData = () => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  newsList.value = allNewsList.value.slice(startIndex, endIndex);
  totalNews.value = allNewsList.value.length;
};

// 查看新闻详情
const viewDetails = (id) => {
  router.push({
    name: 'newsPage',
    query: {
      id: id
    }
  });
};

// 返回首页
const goHome = () => {
  router.push('/');
};

// 页码变化处理
const handlePageChange = (page) => {
  currentPage.value = page;
  updatePageData();
  // 滚动到页面顶部
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

// 在组件挂载前检查是否是从"更多"按钮跳转过来的
onBeforeMount(() => {
  forceScrollToTop();
  const fromMoreButton = localStorage.getItem('fromMoreButton');
  if (fromMoreButton === 'true') {
    // 清除标记
    localStorage.removeItem('fromMoreButton');
    // 强制滚动到顶部
    forceScrollToTop();
  }
});

onMounted(() => {
  fetchNewsData();
  // 确保页面挂载时滚动到顶部
  forceScrollToTop();
  
  // 再次延迟尝试滚动到顶部，确保在页面完全加载后执行
  setTimeout(() => {
    forceScrollToTop();
  }, 500);
});

// 在组件激活时也滚动到顶部
onActivated(() => {
  forceScrollToTop();
});
</script>

<style scoped>
.category-page {
  width: 100%;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
  display: flex;
  justify-content: center;
  padding: 32px 16px;
}

.page-content {
  width: 100%;
  max-width: 900px;
}

.category-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  text-align: center;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-decoration {
  width: 6px;
  height: 28px;
  background-color: #409EFF;
  border-radius: 3px;
  margin-right: 16px;
}

.category-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  letter-spacing: 1px;
}

.news-container {
  background-color: #fff;
  border-radius: 12px;
  padding: 28px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.05);
}

.news-list {
  margin-top: 20px;
}

.news-item {
  margin-bottom: 24px;
  transition: all 0.3s;
}

.news-card {
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 8px;
}

.news-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: #409EFF;
}

.news-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.news-main {
  flex: 1;
}

.news-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-top: 0;
  margin-bottom: 16px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  letter-spacing: 0.5px;
}

.hot-tag {
  display: inline-block;
  background-color: #f56c6c;
  color: white;
  font-size: 13px;
  padding: 3px 8px;
  border-radius: 4px;
  margin-right: 12px;
  font-weight: normal;
}

.news-info {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 12px;
  padding-top: 16px;
  border-top: 1px dashed #ebeef5;
}

.news-meta {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 14px;
}

.meta-text {
  margin: 0 18px 0 6px;
}

.meta-icon {
  margin-left: 6px;
}

.pagination-container {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  padding-bottom: 16px;
}

.empty-container {
  margin: 80px 0;
  background-color: #fff;
  border-radius: 12px;
  padding: 60px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.05);
  text-align: center;
}

@media (max-width: 768px) {
  .category-page {
    padding: 20px 12px;
  }
  
  .category-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
  
  .title-decoration {
    height: 24px;
    margin-right: 12px;
  }
  
  .category-title {
    font-size: 22px;
  }
  
  .news-container {
    padding: 20px 16px;
    border-radius: 8px;
  }
  
  .news-title {
    font-size: 18px;
    margin-bottom: 12px;
  }
  
  .news-card {
    padding: 4px;
  }
  
  .news-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .news-meta {
    width: 100%;
    flex-wrap: wrap;
  }
  
  .empty-container {
    padding: 40px 20px;
    margin: 40px 0;
  }
}
</style>
