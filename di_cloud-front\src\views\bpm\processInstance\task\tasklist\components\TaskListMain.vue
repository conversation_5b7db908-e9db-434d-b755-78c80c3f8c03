<template>
  <div>
    <!-- 标题区域 -->
    <div class="mb-4">
      <div class="flex justify-between items-center">
        <h2 class="text-center text-xl font-bold">
          工作任务清单（集团纪委）
        </h2>
        <div class="flex gap-2">
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:refresh" class="mr-1" /> 刷新列表
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 任务列表 -->
    <div class="mb-4">
      <el-table v-loading="loading" :data="list">
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column label="工作任务" align="center" prop="taskName" min-width="180px" />
        <el-table-column label="工作项目" align="center" prop="taskType" width="120">
          <template #default="scope">
            <el-tag 
              :type="getTaskProjectType(scope.row.taskType)"
              :effect="scope.row.taskType === '6' ? 'plain' : 'dark'"
            >
              {{ scope.row.taskTypeLabel || '其他事项' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="下发日期"
          align="center"
          prop="createTime"
          width="180"
          :formatter="dateFormatter2"
        />
        <el-table-column
          label="办理时限"
          align="center"
          prop="dueTime"
          width="180"
          :formatter="dateFormatter"
        />
        <el-table-column label="完结率" align="center" prop="completionRate" width="120">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.completionRate" 
              :color="getProgressColor(scope.row.completionRate)"
            />
          </template>
        </el-table-column>
        <el-table-column label="反馈情况" align="center" width="180">
          <template #default="scope">
            <el-button link type="primary" @click="showFeedbackDetails(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="mt-3 flex justify-center">
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onActivated } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { dateFormatter } from '@/utils/formatTime'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { getTaskEngineProcessInstancePage } from '@/api/bpm/processInstance'

defineOptions({ name: 'TaskListMain' })

const emit = defineEmits(['show-feedback-details'])

// 消息工具
const message = useMessage()

// 加载和分页状态
const loading = ref(false)
const total = ref(0)
const list = ref<Array<{
  id: string;
  taskName: string;
  taskType: string;
  taskTypeLabel?: string;
  createTime: string;
  dueTime: string;
  completionRate: number;
  feedbackCount: number;
  totalCount: number;
}>>([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 30) {
    return '#f56c6c' // 红色
  } else if (percentage < 70) {
    return '#e6a23c' // 黄色
  } else {
    return '#67c23a' // 绿色
  }
}

// 获取工作项目的标签类型
const getTaskProjectType = (taskType: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  switch (taskType) {
    case '1': return 'warning'  // 会议通知
    case '2': return 'info'     // 工作提示
    case '3': return 'primary'  // 专项工作
    case '4': return 'success'  // 队伍建设
    case '5': return 'info'     // 日常报表
    default: return 'info'      // 其他事项
  }
}

/** 获取任务列表 */
const getList = async () => {
  loading.value = true
  try {
    // 构建API所需的查询参数
    const apiParams = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize
    }
    
    // 调用任务引擎接口并获取数据
    try {
      const response = await getTaskEngineProcessInstancePage(apiParams)
      
      // 使用API返回的数据
      if (response && response.list && response.total) {
        // 将API返回的数据映射到当前使用的字段
        list.value = response.list.map(item => ({
          id: item.id,
          taskName: item.name,
          taskType: item.taskEngineProject,
          taskTypeLabel: getDictLabel('system_task_project', item.taskEngineProject),
          createTime: item.taskEngineReleaseDate || null,  // 下发时间
          dueTime: item.taskEngineTimeLimit,          // 办理时限
          completionRate: Math.round((item.completedCount / item.processInstanceNum) * 100),
          feedbackCount: item.completedCount,
          totalCount: item.processInstanceNum
        }))
        // 按id降序排序（从大到小，即倒序）
        list.value.sort((a, b) => Number(b.id) - Number(a.id))
        total.value = response.total
      } else {
        // 如果API没有返回预期的数据结构，使用空数组
        list.value = []
        total.value = 0
      }
    } catch (error) {
      console.error('调用任务引擎接口出错:', error)
      message.error('获取任务引擎数据失败')
      list.value = []
      total.value = 0
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 查看反馈详情 */
const showFeedbackDetails = (row) => {
  emit('show-feedback-details', row)
}

onMounted(() => {
  getList()
})

onActivated(() => {
  getList()
})

// 暴露方法给父组件
defineExpose({
  getList
})
</script>

<style scoped>
.el-progress {
  margin-right: 0;
  margin-bottom: 5px;
}
</style> 
