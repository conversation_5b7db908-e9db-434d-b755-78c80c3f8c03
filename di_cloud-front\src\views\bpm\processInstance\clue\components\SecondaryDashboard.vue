<template>
  <div class="secondary-dashboard">
    <!-- 优化查询区域 -->
    <el-card shadow="never" class="search-card" style="margin-bottom: 20px;">
      <div class="filter-container">
        <el-form ref="filterFormRef" :model="queryParams" inline class="filter-form">
          <!-- 第一行：所有筛选条件 -->
          <div class="filter-row">
            <el-form-item v-if="hasUnitRangePermission" label="单位范围">
              <el-select
                v-model="queryParams.organizingDeptId"
                placeholder="请选择单位范围"
                clearable
                class="filter-select"
                :loading="unitOptionsLoading"
              >
                <el-option
                  v-for="item in unitOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id ? item.id.toString() : ''"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="统计时间范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                class="date-range-picker"
                clearable
              />
            </el-form-item>
            
            <el-form-item label="流程状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择流程状态"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_xs'" label="线索处置方式">
              <el-select
                v-model="queryParams.xsczfs"
                placeholder="请选择线索处置方式"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_XS_XSCZFS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_xs'" label="处置结果">
              <el-select
                v-model="queryParams.czjgtj"
                placeholder="请选择处置结果"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_XS_CZJGTJ)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_xs'" label="是否超时">
              <el-select
                v-model="queryParams.sfcs"
                placeholder="请选择是否超时"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_XS_SFCS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_xf'" label="信访举报方式">
              <el-select
                v-model="queryParams.xfjbfs"
                placeholder="请选择信访举报方式"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_XF_XFJBFS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_xf'" label="信访举报类别">
              <el-select
                v-model="queryParams.xflb"
                placeholder="请选择信访举报类别"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_XF_XFLB)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_xf'" label="信访结果分布">
              <el-select
                v-model="queryParams.xfjgfb"
                placeholder="请选择信访结果分布"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_XF_XFJGFB)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_xf'" label="是否重复件">
              <el-select
                v-model="queryParams.sfcfj"
                placeholder="请选择是否重复件"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_XF_SFCFJ)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_register'" label="政务处分结果">
              <el-select
                v-model="queryParams.zwcfjg"
                placeholder="请选择政务处分结果"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_LAJ_ZWCFJG)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item v-if="category === 'oa_register'" label="党纪处分结果">
              <el-select
                v-model="queryParams.djcfjg"
                placeholder="请选择党纪处分结果"
                clearable
                class="filter-select"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SJKB_LAJ_DJCFJG)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item class="button-item">
              <el-button type="primary" @click="handleSearch">
                <Icon icon="ep:search" class="mr-5px" /> 搜索
              </el-button>
              <el-button @click="handleReset">
                <Icon icon="ep:refresh" class="mr-5px" /> 重置
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-card>

    <el-card class="dashboard-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>{{ title }}</span>
          <el-button plain @click="goBack">
            <Icon icon="ep:back" class="mr-5px" /> 返回主看板
          </el-button>
        </div>
      </template>

      <!-- 数据表格区域 -->
      <el-table :data="tableData" stripe border style="width: 100%" v-loading="loading">
        <!-- 信访件表格列 -->
        <template v-if="category === 'oa_xf'">
          <el-table-column label="序号" width="60" type="index" fixed="left" />
          <el-table-column label="信访件名称" prop="processInstanceTitle" min-width="200" show-overflow-tooltip fixed="left" />
          <el-table-column label="信访编号" width="120" fixed="left">
            <template #default="{ row }">
              <el-link type="primary" @click="handleDetailClick(row)">{{ row.caseNumber || '-' }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="信访件状态" width="120">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                :effect="getStatusTagEffect(row.status)"
              >
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="承办单位" min-width="150" show-overflow-tooltip />
          <el-table-column prop="processXfRegistrationDate" label="受理时间" width="180" sortable>
            <template #default="{ row }">
              {{ formatAcceptanceDate(row.processXfRegistrationDate) }}
            </template>
          </el-table-column>
        </template>

        <!-- 线索件表格列 -->
        <template v-if="category === 'oa_xs'">
          <el-table-column label="序号" width="60" type="index" fixed="left" />
          <el-table-column label="线索件名称" prop="processInstanceTitle" min-width="200" show-overflow-tooltip fixed="left" />
          <el-table-column label="线索编号" width="120" fixed="left">
            <template #default="{ row }">
              <el-link type="primary" @click="handleDetailClick(row)">{{ row.caseNumber || '-' }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="线索件状态" width="120">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                :effect="getStatusTagEffect(row.status)"
              >
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="承办单位" min-width="150" show-overflow-tooltip />
          <el-table-column prop="processInstanceAcceptanceDate" label="受理时间" width="180" sortable>
            <template #default="{ row }">
              {{ formatAcceptanceDate(row.processInstanceAcceptanceDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="processInstanceLimitDate" label="办理时限" width="180" sortable>
            <template #default="{ row }">
              {{ formatAcceptanceDate(row.processInstanceLimitDate) }}
            </template>
          </el-table-column>
        </template>

        <!-- 立案件表格列 -->
        <template v-if="category === 'oa_register'">
          <el-table-column label="序号" width="60" type="index" fixed="left" />
          <el-table-column label="案件名称" prop="processInstanceTitle" min-width="200" show-overflow-tooltip fixed="left" />
          <el-table-column label="案件编号" width="120" fixed="left">
            <template #default="{ row }">
              <el-link type="primary" @click="handleDetailClick(row)">{{ row.caseNumber || '-' }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="案件状态" width="120">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                :effect="getStatusTagEffect(row.status)"
              >
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="承办单位" min-width="150" show-overflow-tooltip />
          <el-table-column prop="processRegisterDate" label="立案时间" width="180" sortable>
            <template #default="{ row }">
              {{ formatAcceptanceDate(row.processRegisterDate) }}
            </template>
          </el-table-column>
        </template>

        <!-- 总体视图表格列（包含所有类型） -->
        <template v-if="category === 'total'">
          <el-table-column label="序号" width="60" type="index" fixed="left" />
          <el-table-column label="类型" width="100" fixed="left">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.category)">
                {{ getTypeLabel(row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="名称" prop="processInstanceTitle" min-width="200" show-overflow-tooltip fixed="left" />
          <el-table-column label="编号" width="120">
            <template #default="{ row }">
              <el-link type="primary" @click="handleDetailClick(row)">{{ row.caseNumber || '-' }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="120">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                :effect="getStatusTagEffect(row.status)"
              >
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="承办单位" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.deptName || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="受理时间" width="180" sortable>
            <template #default="{ row }">
              {{ formatAcceptanceDate(row.processInstanceAcceptanceDate || row.acceptanceDate || row.processRegisterDate || row.processXfRegistrationDate || '-') }}
            </template>
          </el-table-column>
        </template>

        <!-- 通用操作列 -->
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleDetailClick(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <el-pagination
        :current-page="queryParams.pageNo"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        background
        style="margin-top: 20px; justify-content: flex-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, defineComponent, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Icon } from '@/components/Icon'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { DictTag } from '@/components/DictTag'
import { getStatistics } from '@/api/bpm/clue'
import { getBpmLettersStatisticsPage, getBpmClueStatisticsPage, getBpmFilingStatisticsPage } from '@/api/bpm/processInstance'
import { getOrganizingDeptList } from '@/api/system/dept'
import { formatDate } from '@/utils/formatTime'
import { checkPermi } from '@/utils/permission'
import { useDictStoreWithOut } from '@/store/modules/dict'

// 定义组件名称
defineOptions({
  name: 'SecondaryDashboard'
})

const router = useRouter()
const route = useRoute()

// 获取字典Store
const dictStore = useDictStoreWithOut()

// 格式化受理时间
const formatAcceptanceDate = (dateTime) => {
  if (!dateTime) return '-'
  
  // 判断是否为数字（毫秒数）
  if (!isNaN(Number(dateTime)) && String(dateTime).length > 10) {
    // 将毫秒数转换为日期对象
    const date = new Date(Number(dateTime))
    return formatDate(date, 'YYYY-MM-DD')
  }
  
  // 如果已经是格式化的日期字符串，则直接返回
  return dateTime
}

// 单位选项
const unitOptions = ref([])
const unitOptionsLoading = ref(false)

// 获取单位列表
const getUnitOptions = async () => {
  try {
    unitOptionsLoading.value = true
    const res = await getOrganizingDeptList()
    unitOptions.value = res
  } catch (error) {
    console.error('获取单位列表失败:', error)
    ElMessage.error('获取单位列表失败')
  } finally {
    unitOptionsLoading.value = false
  }
}

// 当前分类（线索件/信访件/立案件/总体）
const category = ref(route.query.category?.toString() || 'total')

// 日期范围
const dateRange = ref(undefined)

// 监听日期范围变化，同步到查询参数
watch(dateRange, (val) => {
  if (val) {
    queryParams.startTime = val[0]
    queryParams.endTime = val[1]
  } else {
    queryParams.startTime = ''
    queryParams.endTime = ''
  }
})

// 表单引用
const filterFormRef = ref()

// 查询参数
const queryParams = reactive({
  category: computed(() => category.value),
  startTime: route.query.startTime?.toString() || '',
  endTime: route.query.endTime?.toString() || '',
  organizingDeptId: route.query.organizingDeptId?.toString() || '',
  xfjbfs: '',
  xflb: '',
  xfjgfb: '',
  sfcfj: '',
  xsczfs: '',
  czjgtj: '',
  sfcs: '',
  zwcfjg: '',
  djcfjg: '',
  status: '',
  pageNo: 1,
  pageSize: 10
})

// 表格数据
const tableData = ref([])
const total = ref(0)
const loading = ref(false)

// 表格标题
const title = computed(() => {
  switch (category.value) {
    case 'oa_xf':
      return '信访件列表'
    case 'oa_xs':
      return '线索件列表'
    case 'oa_register':
      return '立案件列表'
    case 'total':
      return '综合业务列表'
    default:
      return '综合业务列表'
  }
})

// 获取类型标签类型
const getTypeTagType = (type) => {
  switch (type) {
    case 'oa_xf':
      return 'success'
    case 'oa_xs':
      return 'warning'
    case 'oa_register':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取类型标签文本
const getTypeLabel = (type) => {
  // 暂时全部显示为线索件
  return '线索件'
}

// 初始化日期范围
const initDateRange = () => {
  if (queryParams.startTime && queryParams.endTime) {
    dateRange.value = [queryParams.startTime, queryParams.endTime]
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 创建params对象并定义其类型，包括可选的category属性
    const params: {
      startTime?: string;
      endTime?: string;
      organizingDeptId?: string;
      pageNo: number;
      pageSize: number;
      category?: string;
      xfjbfs?: string;
      xflb?: string;
      xfjgfb?: string;
      sfcfj?: string;
      xsczfs?: string;
      czjgtj?: string;
      sfcs?: string;
      zwcfjg?: string;
      djcfjg?: string;
      status?: string;
    } = {
      startTime: queryParams.startTime || undefined,
      endTime: queryParams.endTime || undefined,
      organizingDeptId: queryParams.organizingDeptId || undefined,
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize
    }

    // 是否为信访件类别
    const isXfCategory = queryParams.category === 'oa_xf'
    // 是否为线索件类别
    const isXsCategory = queryParams.category === 'oa_xs'
    // 是否为立案件类别
    const isRegisterCategory = queryParams.category === 'oa_register'

    if (isXfCategory) {
      // 使用新接口处理信访件的情况
      const xfParams = {
        startDate: queryParams.startTime || undefined,
        endDate: queryParams.endTime || undefined,
        deptId: queryParams.organizingDeptId || undefined,
        pageNo: queryParams.pageNo.toString(),
        pageSize: queryParams.pageSize.toString(),
        sysXfjbfs: queryParams.xfjbfs || undefined,
        sysXflb: queryParams.xflb || undefined,
        sysXfjgfb: queryParams.xfjgfb || undefined,
        sysSfcfj: queryParams.sfcfj || undefined,
        finishStatus: queryParams.status || undefined
      }

      try {
        const res = await getBpmLettersStatisticsPage(xfParams)
        if (res) {
          tableData.value = res.list || []
          total.value = res.total || 0
        } else {
          tableData.value = []
          total.value = 0
        }
      } catch (error) {
        console.error('获取信访件统计数据失败:', error)
        ElMessage.error('获取信访件统计数据失败')
        tableData.value = []
        total.value = 0
      }
    } else if (isXsCategory) {
      // 使用新接口处理线索件的情况
      const xsParams = {
        startDate: queryParams.startTime || undefined,
        endDate: queryParams.endTime || undefined,
        deptId: queryParams.organizingDeptId || undefined,
        pageNo: queryParams.pageNo.toString(),
        pageSize: queryParams.pageSize.toString(),
        sysXsczfs: queryParams.xsczfs || undefined,
        sysCzjgtj: queryParams.czjgtj || undefined,
        sysSfcs: queryParams.sfcs || undefined,
        finishStatus: queryParams.status || undefined
      }

      try {
        const res = await getBpmClueStatisticsPage(xsParams)
        if (res) {
          tableData.value = res.list || []
          total.value = res.total || 0
        } else {
          tableData.value = []
          total.value = 0
        }
      } catch (error) {
        console.error('获取线索件统计数据失败:', error)
        ElMessage.error('获取线索件统计数据失败')
        tableData.value = []
        total.value = 0
      }
    } else if (isRegisterCategory) {
      // 使用新接口处理立案件的情况
      const registerParams = {
        startDate: queryParams.startTime || undefined,
        endDate: queryParams.endTime || undefined,
        deptId: queryParams.organizingDeptId || undefined,
        pageNo: queryParams.pageNo.toString(),
        pageSize: queryParams.pageSize.toString(),
        sysZwcfjg: queryParams.zwcfjg || undefined,
        sysDjcfjg: queryParams.djcfjg || undefined,
        finishStatus: queryParams.status || undefined
      }

      try {
        const res = await getBpmFilingStatisticsPage(registerParams)
        if (res) {
          tableData.value = res.list || []
          total.value = res.total || 0
        } else {
          tableData.value = []
          total.value = 0
        }
      } catch (error) {
        console.error('获取立案件统计数据失败:', error)
        ElMessage.error('获取立案件统计数据失败')
        tableData.value = []
        total.value = 0
      }
    } else {
      // 仅当category不为total时才添加category参数
      if (queryParams.category !== 'total') {
        params.category = queryParams.category
      }

      // 如果选择了流程状态，添加到参数中
      if (queryParams.status) {
        params.status = queryParams.status
      }

      // 仅当选择了信访方式且当前类别为信访件时添加该参数
      if (queryParams.xfjbfs && queryParams.category === 'oa_xf') {
        params.xfjbfs = queryParams.xfjbfs
      }

      // 仅当选择了信访类别且当前类别为信访件时添加该参数
      if (queryParams.xflb && queryParams.category === 'oa_xf') {
        params.xflb = queryParams.xflb
      }

      // 仅当选择了信访结果分布且当前类别为信访件时添加该参数
      if (queryParams.xfjgfb && queryParams.category === 'oa_xf') {
        params.xfjgfb = queryParams.xfjgfb
      }

      // 仅当选择了是否重复件且当前类别为信访件时添加该参数
      if (queryParams.sfcfj && queryParams.category === 'oa_xf') {
        params.sfcfj = queryParams.sfcfj
      }

      // 仅当选择了线索处置方式且当前类别为线索件时添加该参数
      if (queryParams.xsczfs && queryParams.category === 'oa_xs') {
        params.xsczfs = queryParams.xsczfs
      }

      // 仅当选择了处置结果且当前类别为线索件时添加该参数
      if (queryParams.czjgtj && queryParams.category === 'oa_xs') {
        params.czjgtj = queryParams.czjgtj
      }

      // 仅当选择了是否超时且当前类别为线索件时添加该参数
      if (queryParams.sfcs && queryParams.category === 'oa_xs') {
        params.sfcs = queryParams.sfcs
      }

      // 仅当选择了政务处分结果且当前类别为立案件时添加该参数
      if (queryParams.zwcfjg && queryParams.category === 'oa_register') {
        params.zwcfjg = queryParams.zwcfjg
      }

      // 仅当选择了党纪处分结果且当前类别为立案件时添加该参数
      if (queryParams.djcfjg && queryParams.category === 'oa_register') {
        params.djcfjg = queryParams.djcfjg
      }

      try {
        const res = await getStatistics(params)
    
        if (res) {
          tableData.value = res.list || []
          total.value = res.total || 0
        } else {
          tableData.value = []
          total.value = 0
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        ElMessage.error('获取统计数据失败')
        tableData.value = []
        total.value = 0
      }
    }
  } finally {
    loading.value = false
  }
}

// 搜索按钮点击
const handleSearch = () => {
  queryParams.pageNo = 1
  fetchData()
}

// 重置按钮点击
const handleReset = () => {
  filterFormRef.value?.resetFields()
  dateRange.value = undefined
  queryParams.startTime = ''
  queryParams.endTime = ''
  queryParams.organizingDeptId = ''
  queryParams.xfjbfs = ''
  queryParams.xflb = ''
  queryParams.xfjgfb = ''
  queryParams.sfcfj = ''
  queryParams.xsczfs = ''
  queryParams.czjgtj = ''
  queryParams.sfcs = ''
  queryParams.zwcfjg = ''
  queryParams.djcfjg = ''
  queryParams.status = ''
  queryParams.pageNo = 1
  fetchData()
}

// 详情按钮点击
const handleDetailClick = (row) => {
  router.push({
    path: '/bpm/process-instance/detail',
    query: {
      id: row.processInstanceId
    }
  })
}

// 返回主看板
const goBack = () => {
  router.back()
}

// 分页事件
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  queryParams.pageNo = 1
  fetchData()
}

const handleCurrentChange = (val) => {
  queryParams.pageNo = val
  fetchData()
}

// 监听路由参数变化
watch(
  () => [
    route.query.category,
    route.query.startTime,
    route.query.endTime,
    route.query.organizingDeptId
  ],
  ([newCategory, newStartTime, newEndTime, newOrganizingDeptId]) => {
    if (newCategory) {
      category.value = newCategory.toString()
    }
    if (newStartTime !== undefined) {
      queryParams.startTime = newStartTime?.toString() || ''
    }
    if (newEndTime !== undefined) {
      queryParams.endTime = newEndTime?.toString() || ''
    }
    if (newOrganizingDeptId !== undefined) {
      queryParams.organizingDeptId = newOrganizingDeptId?.toString() || ''
    }
    initDateRange()
    queryParams.pageNo = 1
    fetchData()
  }
)

// 检查是否有单位范围权限
const hasUnitRangePermission = computed(() => checkPermi(['bpm:clue:query']))

// 状态标签类型
const getStatusTagType = (status) => {
  // 假设 1 代表办理中，2 代表已办结，请根据实际情况调整
  if (status === '1' || status === 1) {
    return 'warning' // 办理中用黄色警告标签
  } else if (status === '2' || status === 2) {
    return 'success' // 已办结用绿色成功标签
  } else {
    return 'info' // 其他状态用灰色信息标签
  }
}

// 状态标签效果（dark、plain、light）
const getStatusTagEffect = (status) => {
  // 假设 1 代表办理中，2 代表已办结，请根据实际情况调整
  if (status === '1' || status === 1) {
    return 'dark' // 办理中用深色背景
  } else if (status === '2' || status === 2) {
    return 'light' // 已办结用浅色背景
  } else {
    return 'plain' // 其他状态用朴素效果
  }
}

// 获取状态标签文本
const getStatusLabel = (status) => {
  // 从字典获取状态文本
  const statusDict = dictStore.getDictByType(DICT_TYPE.SJKB_STATUS)
  if (statusDict && statusDict.length > 0) {
    const found = statusDict.find(item => item.value === status.toString())
    if (found) {
      return found.label
    }
  }
  
  // 如果找不到，则返回默认值
  return status === '1' || status === 1 ? '办理中' : 
         status === '2' || status === 2 ? '已办结' : '未知状态'
}

onMounted(async () => {
  try {
    // 先加载字典数据，确保下拉框能够正常显示
    await dictStore.setDictMap()
    console.log('字典数据加载完成，包含信访方式数据:', dictStore.getDictByType(DICT_TYPE.SJKB_XF_XFJBFS))
    
    // 再加载其他数据
    await getUnitOptions()
    
    // 在单位选项加载完成后，确保单位范围下拉框显示正确的值
    // 如果路由参数中有organizingDeptId，需要确保下拉框能正确显示对应的单位名称
    if (queryParams.organizingDeptId && unitOptions.value.length > 0) {
      // 验证传入的organizingDeptId是否在单位选项中存在
      const foundUnit = unitOptions.value.find((unit: any) => unit.id?.toString() === queryParams.organizingDeptId)
      if (!foundUnit) {
        console.warn('传入的单位ID在单位选项中不存在:', queryParams.organizingDeptId)
        // 如果找不到对应的单位，清空organizingDeptId
        queryParams.organizingDeptId = ''
      } else {
        console.log('找到对应的单位:', foundUnit.name)
      }
    }
    
    initDateRange()
    
    // 等待下一个DOM更新周期再进行数据获取
    await nextTick()
    fetchData()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败，请刷新页面重试')
  }
})
</script>

<style scoped>
.secondary-dashboard {
  margin-top: 20px;
}

.dashboard-card {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e6e6e6;
  background-color: #f5f7fa;
}

.card-header span {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

/* 查询区域样式 */
.search-card {
  margin-bottom: 20px;
}

.filter-container {
  position: relative;
}

.filter-form {
  width: 100%;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  width: 100%;
  gap: 10px;
}

.filter-select {
  width: 240px;
}

.date-range-picker {
  width: 340px;
}

.button-item {
  margin-left: 10px !important;
}

.button-item :deep(.el-form-item__content) {
  display: flex;
  gap: 10px;
}

/* 设置移动端响应式布局 */
@media screen and (max-width: 1200px) {
  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .button-item {
    margin-left: 0 !important;
    align-self: flex-start;
  }
  
  .filter-select, 
  .date-range-picker {
    width: 100%;
  }
}

/* 表格样式 */
:deep(.el-table) {
  border: none !important;
}

:deep(.el-table th) {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-weight: bold;
  border-bottom: 1px solid #e6e6e6 !important;
}

:deep(.el-table td) {
  border-bottom: 1px solid #e6e6e6 !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: #f5f7fa !important;
}

:deep(.el-pagination) {
  margin-top: 15px;
  justify-content: flex-end;
  padding: 15px;
}

/* 标签样式 */
:deep(.el-tag--success) {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

:deep(.el-tag--warning) {
  background-color: #fdf6ec;
  border-color: #faecd8;
  color: #e6a23c;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  border-color: #fde2e2;
  color: #f56c6c;
}

:deep(.el-tag--info) {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399;
}

:deep(.el-form-item) {
  margin-right: 0;
  margin-bottom: 15px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
}
</style> 
