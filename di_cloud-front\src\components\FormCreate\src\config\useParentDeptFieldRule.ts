import { generateUUID } from '@/utils'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'

export const useParentDeptFieldRule = () => {
  const label = '上级部门字段'
  const name = 'ParentDeptField'
  return {
    icon: 'icon-apartment',
    label,
    name,
    rule() {
      const field = generateUUID()
      
      return {
        type: name,
        field: field,
        title: label,
        info: '自动获取当前用户的上级部门信息',
        $required: false,
        props: {
          field: field,
          // 显示类型：text-文本显示，hidden-隐藏字段，select-下拉选择（允许用户更改）
          displayType: 'text',
          fieldLabel: '上级部门',
          // 字段值类型：id-仅部门ID，name-仅部门名称，both-ID和名称（JSON格式）
          valueType: 'id',
          // 是否禁用（用于select类型）
          disabled: false,
          // 无上级部门时的默认值
          defaultValue: '',
          // 无上级部门时的默认文本
          defaultText: '无上级部门',
          // 无上级部门时是否使用当前部门ID
          useCurrentDeptWhenNoParent: true
        }
      }
    },
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        {
          type: 'input',
          field: 'field',
          title: '字段名称',
          info: '表单提交时的字段名',
          props: {
            placeholder: '请输入字段名称'
          }
        },
        {
          type: 'select',
          field: 'displayType',
          title: '显示类型',
          value: 'text',
          options: [
            { label: '文本显示', value: 'text' },
            { label: '隐藏字段', value: 'hidden' },
            { label: '下拉选择', value: 'select' }
          ],
          info: '选择字段的显示方式'
        },
        {
          type: 'input',
          field: 'fieldLabel',
          title: '字段标签',
          value: '上级部门',
          props: {
            placeholder: '显示的字段标签名称'
          }
        },
        {
          type: 'select',
          field: 'valueType',
          title: '值类型',
          value: 'id',
          options: [
            { label: '仅部门ID', value: 'id' },
            { label: '仅部门名称', value: 'name' },
            { label: 'ID和名称', value: 'both' }
          ],
          info: '选择字段保存的值类型'
        },
        {
          type: 'switch',
          field: 'disabled',
          title: '禁用选择',
          value: false,
          info: '仅在显示类型为"下拉选择"时有效'
        },
        {
          type: 'input',
          field: 'defaultValue',
          title: '默认值',
          value: '',
          info: '当无上级部门时使用的默认值'
        },
        {
          type: 'input',
          field: 'defaultText',
          title: '默认文本',
          value: '无上级部门',
          info: '当无上级部门时显示的文本'
        },
        {
          type: 'switch',
          field: 'useCurrentDeptWhenNoParent',
          title: '无上级时使用当前部门',
          value: true,
          info: '当无上级部门时，使用当前用户的部门ID作为值'
        }
      ])
    }
  }
} 