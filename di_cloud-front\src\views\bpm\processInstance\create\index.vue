<template>

  <!-- 第一步，通过流程定义的列表，选择对应的流程 -->
  <ContentWrap v-if="!selectProcessDefinition" v-loading="loading">
    <!--<el-tabs tab-position="left" v-model="categoryActive">
      <el-tab-pane
        :label="category.name"
        :name="category.code"
        :key="category.code"
        v-for="category in categoryList"
      >
        <el-row :gutter="20">
          <el-col
            :lg="6"
            :sm="12"
            :xs="24"
            v-for="definition in categoryProcessDefinitionList"
            :key="definition.id"
          >
            <el-card
              shadow="hover"
              class="mb-20px cursor-pointer"
              @click="handleSelect(definition)"
            >
              <template #default>
                <div class="flex">
                  <el-image :src="definition.icon" class="w-32px h-32px" />
                  <el-text class="!ml-10px" size="large">{{ definition.name }}</el-text>
                </div>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>-->
    <el-tabs
v-model="categoryActive" type="border-card" @tab-click="handleClick"
             tab-position="top">
      <el-tab-pane
:label="category.name" :name="category.code" :key="category.code"
                   v-for="category in categoryList">


        <el-row :gutter="20">
          <el-col
            :lg="6"
            :sm="12"
            :xs="24"
            v-for="definition in processDefinitionList"
            :key="definition.id"
          >
            <el-card
              shadow="hover"
              class="mb-20px cursor-pointer"
              @click="handleSelect(definition)"
            >
              <template #default>
                <div class="flex">
                  <el-image :src="definition.icon" class="w-32px h-32px"/>
                  <el-text class="!ml-10px" size="large">{{ definition.name }}</el-text>
                </div>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      <!--<el-tab-pane label="配置管理" name="second">配置管理</el-tab-pane>
      <el-tab-pane label="角色管理" name="third">角色管理</el-tab-pane>
      <el-tab-pane label="定时任务补偿" name="fourth">定时任务补偿</el-tab-pane>-->
    </el-tabs>
    <!-- <el-row :gutter="20">
       <el-col
         :lg="6"
         :sm="12"
         :xs="24"
         v-for="definition in processDefinitionList"
         :key="definition.id"
       >
         <el-card
           shadow="hover"
           class="mb-20px cursor-pointer"
           @click="handleSelect(definition)"
         >
           <template #default>
             <div class="flex">
               <el-image :src="definition.icon" class="w-32px h-32px" />
               <el-text class="!ml-10px" size="large">{{ definition.name }}</el-text>
             </div>
           </template>
         </el-card>
       </el-col>
     </el-row>-->
  </ContentWrap>

  <!-- 第二步，填写表单，进行流程的提交 -->
  <ContentWrap v-else>
    <div class="clearfix" style="margin-bottom: 20px">
      <span
class="el-icon-document"
            style="font-size: larger;font-weight: bold">{{ selectProcessDefinition.name }}</span>

    </div>
    <el-card class="box-card">

      <el-col :span="16" :offset="4" style="margin-top: 20px">
        <form-create
          :rule="detailForm.rule"
          v-model:api="fApi"
          v-model="detailForm.value"
          :option="detailForm.option"
          @submit="submitForm"
        >
          <template #type-startUserSelect>
            <el-col :span="24">
              <el-card class="mb-10px">
                <template #header>指定审批人</template>
                <el-form
                  :model="startUserSelectAssignees"
                  :rules="startUserSelectAssigneesFormRules"
                  ref="startUserSelectAssigneesFormRef"
                >
                  <el-form-item
                    v-for="userTask in startUserSelectTasks"
                    :key="userTask.id"
                    :label="`任务【${userTask.name}】`"
                    :prop="userTask.id"
                  >
                    <UserSelect
                      v-model="startUserSelectAssignees[userTask.id]"
                      :ref="el => userSelectRefs[userTask.id] = el"
                    />
                  </el-form-item>
                </el-form>
              </el-card>
            </el-col>

          </template>
        </form-create>
      </el-col>

      <el-col
:span="16" :offset="4" style="display: flex;

  align-items: center;
  justify-content: center;
  text-align: center;">
        <el-button
style="padding-left: 10px" type="primary"
                   @click="selectProcessDefinition = undefined" size="large"
                   v-if="processDefinitionList.length > 1">
          <Icon icon="ep:d-arrow-left"/>
          返回
        </el-button>

        <el-button style="padding-left: 10px" type="primary" size="large" @click="saveCurForm()">
          <Icon icon="ep:document-add"/>
          保存
        </el-button>

        <!-- 新闻预览按钮，仅在新闻类别时显示 -->
        <el-button 
          style="padding-left: 10px" 
          type="primary" 
          size="large" 
          @click="previewNews()"
          v-if="category === 'oa_news' && fApi">
          <Icon icon="ep:view"/>
          预览
        </el-button>

        <el-button style="padding-left: 10px" type="primary" size="large" @click="submitFormNew()">
          <Icon icon="ep:check"/>
          提交
        </el-button>

        <el-button style="padding-left: 10px" type="primary" size="large" @click="resetFormNew()">
          <Icon icon="ep:refresh"/>
          重置
        </el-button>
      </el-col>


    </el-card>
    <!-- 流程图预览 -->
    <!-- <ProcessInstanceBpmnViewer :bpmn-xml="bpmnXML as any" />-->
  </ContentWrap>
</template>
<script lang="ts" setup>
import * as DefinitionApi from '@/api/bpm/definition'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { setConfAndFields2 } from '@/utils/formCreate'
import type { ApiAttrs } from '@form-create/element-ui/types/config'
import ProcessInstanceBpmnViewer from '../detail/ProcessInstanceBpmnViewer.vue'
import { CategoryApi, categoryRoute } from '@/api/bpm/category'
import { useTagsViewStore } from '@/store/modules/tagsView'
import * as UserApi from '@/api/system/user'
import { ElMessageBox } from 'element-plus'
import {formTempApi} from '@/api/bpm/form-temp'
import UserSelect from '@/components/UserSelect'

defineOptions({name: 'BpmProcessInstanceCreate'})

const route = useRoute() // 路由
//console.info("route",JSON.stringify(route))
const {push, currentRoute} = useRouter() // 路由
const router = useRouter()
//console.info("currentRoute",JSON.stringify(currentRoute))
const message = useMessage() // 消息
const {delView} = useTagsViewStore() // 视图操作
//console.info('route', route);
const processInstanceId = ref(null);
//console.info('processInstanceId', processInstanceId);
//const code = route.query.code;
//console.info('code', code);
const loading = ref(true) // 加载中
const categoryList = ref([]) // 分类的列表
const categoryActive = ref('') // 选中的分类
const processDefinitionList = ref([]) // 流程定义的列表
const formTempId = ref(null)
const formTemp = ref([{
  "id": "string",
  "processDefinitionId": "1024",
  "processCategoryCode": "1024",
  "formName": "string",
  "formId": "string",
  "formTitle": "string",
  "createTime": "2025-02-13T08:54:02.393Z",
  "updateTime": "2025-02-13T08:54:02.393Z",
  "formVariables": {
    "additionalProp1": {},
    "additionalProp2": {},
    "additionalProp3": {}
  },
  "formConf": "string",
  "formFields": [
    "string"
  ],
  "status": 0
}])
/** 查询列表 */
const getList = async (code) => {
  loading.value = true
  try {
    // 流程分类
    categoryList.value = await CategoryApi.getCategorySimpleListByCode(code) //20250208 lijunhua 根据CODE类型进行查询流程分类
    //console.info('categoryList', categoryList.value)
    if (categoryList.value.length > 0) {
      categoryActive.value = categoryList.value[0].code;
    }
    // 流程定义
    processDefinitionList.value = await DefinitionApi.getProcessDefinitionList({
      suspensionState: 1
    })
    // console.info('processDefinitionList', JSON.stringify(processDefinitionList.value));
    if (!route.query.processDefinitionId){
      processDefinitionList.value = processDefinitionList.value.filter((item) => item.category == code);
      
      // 在oa_xs类别中，过滤掉wtxssqyq_bpm流程
      if (code === 'oa_xs') {
        processDefinitionList.value = processDefinitionList.value.filter((item) => item.key !== 'wtxssqyq_bpm');
      }
    }else {
      processDefinitionList.value = processDefinitionList.value.filter((item) => item.id == route.query.processDefinitionId);
    }
   // console.info('processDefinitionListLength', processDefinitionList.value.length)
    if (processDefinitionList.value.length == 1) {
      processInstanceId.value = processDefinitionList.value[0].id;
      //console.info("processInstanceId", processInstanceId);
      //categoryActive.value = processDefinitionList.value[0].category;
    }
    //console.info('processDefinitionList2', processDefinitionList.value);

    //console.info('processDefinitionList', processDefinitionList)

    // 如果 processInstanceId 非空，说明是重新发起
    //console.info("processInstanceId", processInstanceId.value)
    if (processInstanceId.value) {
     // console.info("流程1")
      /** const processInstance = await ProcessInstanceApi.getProcessInstance(processInstanceId)
       console.info('processInstance', processInstance)
       if (!processInstance) {
       message.error('重新发起流程失败，原因：流程实例不存在1')
       return
       }
       const processDefinition = processDefinitionList.value.find(
       (item) => item.key == processInstance.processDefinition?.key
       )
       if (!processDefinition) {
       message.error('重新发起流程失败，原因：流程定义不存在2')
       return
       }**/
      if (!route.query.formTempId){
        //  console.info("流程2")
          await handleSelect(processDefinitionList.value[0])
      }else{
       // console.info("流程4")
        formTemp.value = await formTempApi.getFormTem(route.query.formTempId);
       //console.info("formTemp.value",JSON.stringify(formTemp.value.formVariables))
       // console.info("processDefinitionList.value[0]",JSON.stringify(processDefinitionList.value[0]))
        await handleSelect(processDefinitionList.value[0],formTemp.value.formVariables)
      }
    }
  } finally {
    loading.value = false
  }
};

/** 选中分类对应的流程定义列表 */
const categoryProcessDefinitionList = computed(() => {
  return processDefinitionList.value.filter((item) => item.category == categoryActive.value)
})

// ========== 表单相关 ==========
const fApi = ref<ApiAttrs>()
const detailForm = ref({
  rule: [],
  option: {},
  value: {}
}) // 流程表单详情
const saveFormTempObjectReq = ref({
  "formTempId":"",
  "processDefinitionId": "",
  "formTitle": "",
  "variables": {}

})
const saveFormTempObjectResp = ref({
  "code": 0,
  "data": {},
  "msg": ""
})
const selectProcessDefinition = ref() // 选择的流程定义

// 指定审批人
const bpmnXML = ref(null) // BPMN 数据
const startUserSelectTasks = ref([]) // 发起人需要选择审批人的用户任务列表
const startUserSelectAssignees = ref({}) // 发起人选择审批人的数据
const startUserSelectAssigneesFormRef = ref() // 发起人选择审批人的表单 Ref
const startUserSelectAssigneesFormRules = ref({}) // 发起人选择审批人的表单 Rules
const userList = ref<any[]>([]) // 用户列表
const category = ref("")

// 添加在现有变量定义之后
const userSelectRefs = ref({}) // 用户选择组件引用

/** 处理选择流程的按钮操作 **/
const handleSelect = async (row, formVariables) => {
  // 设置选择的流程
  selectProcessDefinition.value = row

  // 重置指定审批人
  startUserSelectTasks.value = []
  startUserSelectAssignees.value = {}
  startUserSelectAssigneesFormRules.value = {}
 // console.info('row', JSON.stringify(row))
  // 情况一：流程表单
  if (row.formType == 10) {
    // 设置表单
    setConfAndFields2(detailForm, row.formConf, row.formFields, formVariables)
    // 加载流程图
    const processDefinitionDetail = await DefinitionApi.getProcessDefinition(row.id)
    if (processDefinitionDetail) {
      bpmnXML.value = processDefinitionDetail.bpmnXml
      startUserSelectTasks.value = processDefinitionDetail.startUserSelectTasks

      // 设置指定审批人
      if (startUserSelectTasks.value?.length > 0) {
        detailForm.value.rule.push({
          type: 'startUserSelect',
          props: {
            title: '指定审批人'
          }
        })
        // 设置校验规则
        for (const userTask of startUserSelectTasks.value) {
          startUserSelectAssignees.value[userTask.id] = []
          startUserSelectAssigneesFormRules.value[userTask.id] = [
            {required: true, message: '请选择审批人', trigger: 'blur'}
          ]
        }
        // 加载用户列表
        userList.value = await UserApi.getSimpleUserList()
      }
    }
    // 情况二：业务表单
  } else if (row.formCustomCreatePath) {
    await push({
      path: row.formCustomCreatePath
    })
    // 这里暂时无需加载流程图，因为跳出到另外个 Tab；
  }

  //手动关闭提交和重置按钮
  detailForm.value.option.submitBtn = false;
  detailForm.value.option.resetBtn = false;


}
/** 提交按钮 */
const submitForm = async (formData) => {
  if (!fApi.value || !selectProcessDefinition.value) {
    return
  }
  // 如果有指定审批人，需要校验
  if (startUserSelectTasks.value?.length > 0) {
    await startUserSelectAssigneesFormRef.value.validate()
  }

  // 准备审批方式数据
  const taskApproveMethodMap = {}
  if (startUserSelectTasks.value?.length > 0) {
    for (const userTask of startUserSelectTasks.value) {
      const userSelectRef = userSelectRefs.value[userTask.id]
      if (userSelectRef) {
        const selection = userSelectRef.getCurrentSelection()
        if (selection && selection.approveMethod) {
          taskApproveMethodMap[userTask.id] = selection.approveMethod
        }
      }
    }
  }

  // 提交请求
  fApi.value.btn.loading(true)
  try {
    // if (category.value ==="oa_task"){
    //   await ProcessInstanceApi.createTaskEngineProcessInstance({
    //     processDefinitionId: selectProcessDefinition.value.id,
    //     variables: formData,
    //     startUserSelectAssignees: startUserSelectAssignees.value,
    //     taskApproveMethodMap: taskApproveMethodMap // 添加审批方式参数
    //   })   
    // }else{
      await ProcessInstanceApi.createProcessInstance({
        processDefinitionId: selectProcessDefinition.value.id,
        variables: formData,
        startUserSelectAssignees: startUserSelectAssignees.value,
        taskApproveMethodMap: taskApproveMethodMap // 添加审批方式参数
      })      
    // }

    // 提示
    message.success('发起流程成功')
    // 跳转回去
    delView(unref(currentRoute))
    await push({
      name: 'BpmProcessInstanceMy'
    })
  } finally {
    fApi.value.btn.loading(false)
  }
}

/**
 * 保存当前表单
 */
const saveCurForm = async () => {
  /**let detailForm = ref({
   processDefinitionId: "",
   variables: {},
   startUserSelectAssignees: {}
   });**/
  if (!fApi.value || !selectProcessDefinition.value) {
    return
  }
  //console.info('saveCurForm', selectProcessDefinition.value);
  //console.info("fApi", fApi.value.formData());
  //console.info("detailForm.value",JSON.stringify(detailForm.value.value))
  fApi.value.btn.loading(true)
  if (!detailForm.value.value.form_title) {
    message.error('请填写流程标题')
    return
  }
  //拼接请求参数
  saveFormTempObjectReq.value.formTitle = detailForm.value.value.form_title;
  saveFormTempObjectReq.value.processDefinitionId = selectProcessDefinition.value.id;
  saveFormTempObjectReq.value.variables = detailForm.value.value;
  //saveFormTempObjectReq.value.id = detailForm.value.value.id
  fApi.value.btn.loading(true)
  try {
    if (!formTempId.value) {
      saveFormTempObjectResp.value = await formTempApi.saveProcessInstanceForm(saveFormTempObjectReq.value);
      ///console.error(JSON.stringify(saveFormTempObjectResp.value))

        // 提示
        message.success('暂存流程成功')
        saveFormTempObjectReq.value.id = saveFormTempObjectResp.value.id;
        formTempId.value = saveFormTempObjectReq.value.id
    }else{
      saveFormTempObjectReq.value.id = formTempId.value;
      saveFormTempObjectResp.value = await formTempApi.updateFormTem(saveFormTempObjectReq.value);

        // 提示
        message.success('更新暂存流程成功')


    }


  } finally {
    fApi.value.btn.loading(false)
  }
}

const submitFormNew = async () => {
  if (!fApi.value || !selectProcessDefinition.value) {
    return
  }
  if (startUserSelectTasks.value?.length > 0) {
    await startUserSelectAssigneesFormRef.value.validate()
  }

  // 准备审批方式数据
  const taskApproveMethodMap = {}
  if (startUserSelectTasks.value?.length > 0) {
    for (const userTask of startUserSelectTasks.value) {
      const userSelectRef = userSelectRefs.value[userTask.id]
      if (userSelectRef) {
        const selection = userSelectRef.getCurrentSelection()
        if (selection && selection.approveMethod) {
          taskApproveMethodMap[userTask.id] = selection.approveMethod
        }
      }
    }
  }

  // 提交请求
  fApi.value.btn.loading(true)
  try {
    fApi.value.validate().then(() => {

      if (category.value ==="oa_news"){
      // 检查新闻内容中是否包含超链接
      let content = fApi.value.formData().news_content || ''
      // 先移除所有 img 标签，避免错误匹配 src 中的 URL
      content = content.replace(/<img[^>]*>/gi, ''); 
      // 检测a标签和常见URL格式
      const linkRegex = /<a[^>]*>.*?<\/a>|https?:\/\/\S+|www\.\S+/i
      if (linkRegex.test(content)) {
        message.error('新闻内容中不能包含超链接')
        return
      }
      }
      if (selectProcessDefinition.value.key==="task_task_bpm"){
        const depts = JSON.parse(JSON.stringify(detailForm.value.value.jsrwzzs))
        delete  detailForm.value.value.jsrwzzs
        // detailForm.value.value.jsrwzzs = null
        ProcessInstanceApi.createTaskEngineProcessInstance({
          processDefinitionId: selectProcessDefinition.value.id,
          variables: detailForm.value.value,
          startUserSelectAssignees: startUserSelectAssignees.value,
          processInstanceTitle: detailForm.value.value.form_title,
          taskApproveMethodMap: taskApproveMethodMap, // 添加审批方式参数
          depts:depts,
          processDefinitionKey:selectProcessDefinition.value.key,
          taskEngineName: detailForm.value.value.task_title,
          deptFormKey: "jsrwzzs",
          taskEngineProject: detailForm.value.value.project.toString(),
          taskEngineReleaseDate: detailForm.value.value.task_releasedate,
          taskEngineTimeLimit: detailForm.value.value.task_timelimit,
          taskEngineReleaseTime: detailForm.value.value.task_releasetime
        })    
      }else if(selectProcessDefinition.value.key==="wtxs_bpm"){ 
        ProcessInstanceApi.createProcessInstance({
          processDefinitionId: selectProcessDefinition.value.id,
          variables: detailForm.value.value,
          startUserSelectAssignees: startUserSelectAssignees.value,
          processInstanceTitle: detailForm.value.value.form_title,
          taskApproveMethodMap: taskApproveMethodMap, // 添加审批方式参数
          caseNumber:detailForm.value.value.xsbm, //添加线索编码
          processInstanceAcceptanceDate:detailForm.value.value.xsslrq //添加受理日期
        })   
      }else if(selectProcessDefinition.value.key==="ejwtxs_bpm"){ 
        ProcessInstanceApi.createProcessInstance({
          processDefinitionId: selectProcessDefinition.value.id,
          variables: detailForm.value.value,
          startUserSelectAssignees: startUserSelectAssignees.value,
          processInstanceTitle: detailForm.value.value.form_title,
          taskApproveMethodMap: taskApproveMethodMap, // 添加审批方式参数
          processInstanceAcceptanceDate:detailForm.value.value.slrq //添加受理日期
        })         
      }else if(selectProcessDefinition.value.key==="xinfang_bpm"||selectProcessDefinition.value.key==="sec_xinfang_BPM"){ 
        ProcessInstanceApi.createProcessInstance({
          processDefinitionId: selectProcessDefinition.value.id,
          variables: detailForm.value.value,
          startUserSelectAssignees: startUserSelectAssignees.value,
          processInstanceTitle: detailForm.value.value.form_title,
          taskApproveMethodMap: taskApproveMethodMap, // 添加审批方式参数
          caseNumber: detailForm.value.value.xfbh ? detailForm.value.value.xfbh : (detailForm.value.value.ejxfbh ? detailForm.value.value.ejxfbh : ""), //添加信访编号
          xfCategory: detailForm.value.value.xflb, //添加信访类别
          processXfRegistrationDate:detailForm.value.value.djrq, //添加登记日期
          processXfDeptId:detailForm.value.value.ejcbdw, //添加二级承办单位
        })   
      }else if(selectProcessDefinition.value.key==="register_bpm"){
        ProcessInstanceApi.createProcessInstance({
          processDefinitionId: selectProcessDefinition.value.id,
          variables: detailForm.value.value,
          startUserSelectAssignees: startUserSelectAssignees.value,
          processInstanceTitle: detailForm.value.value.form_title,
          taskApproveMethodMap: taskApproveMethodMap, // 添加审批方式参数
          caseNumber: detailForm.value.value.ajbh? detailForm.value.value.ajbh:"",//添加案件编号
          processRegisterDate: detailForm.value.value.lasj//添加立案时间
        })        
      }else if(selectProcessDefinition.value.key==="register_sec_bpm"){
        ProcessInstanceApi.createProcessInstance({
          processDefinitionId: selectProcessDefinition.value.id,
          variables: detailForm.value.value,
          startUserSelectAssignees: startUserSelectAssignees.value,
          processInstanceTitle: detailForm.value.value.form_title,
          taskApproveMethodMap: taskApproveMethodMap, // 添加审批方式参数
          processRegisterDate: detailForm.value.value.lasj//添加立案时间
        })  
        }else{
        ProcessInstanceApi.createProcessInstance({
          processDefinitionId: selectProcessDefinition.value.id,
          variables: detailForm.value.value,
          startUserSelectAssignees: startUserSelectAssignees.value,
          processInstanceTitle: detailForm.value.value.form_title,
          taskApproveMethodMap: taskApproveMethodMap // 添加审批方式参数
        })        
      }

      

      console.log("detailForm.value.value",detailForm.value.value)
      //如果是暂存数据，提交变更状态请求。
      if(formTempId.value){
        formTempApi.updateStatusFormByTemp({
          id: formTempId.value,
          processInstanceId : selectProcessDefinition.value.id
        })
      }
      // 提示
      message.success('发起流程成功')
      // 跳转回去
     // delView(unref(currentRoute))
      let routeName = "";
      if (category.value ==="oa_xf"){
        routeName = "BpmProcessInstanceMyByXF";
      } else if (category.value ==="oa_xs"){
        routeName = "BpmProcessInstanceMyByXS";
      } else if (category.value ==="oa_news"){
        routeName = "BpmProcessInstanceMyByNews";
        console.log('新闻流程提交成功，准备跳转到:', routeName)
      } else if (category.value ==="oa_task"){
        routeName = "TaskList";
      } else if (category.value ==="oa_tr"){
        routeName = "BpmProcessInstanceMyByTr";
      } else if(category.value ==="oa_leave"){
        routeName = "BpmProcessInstanceMyByLeave"
      }else if(category.value ==="oa_register"){
        routeName = "BpmProcessInstanceMyByRegister"
      }else {
        // 对于其他类型，使用通用路由
        routeName = "BpmProcessInstanceMy";
      }

      // 确保路由名称有效
      if (!routeName) {
        console.error('未找到匹配的路由名称，使用默认路由')
        routeName = "BpmProcessInstanceMy"
      }

      console.log('跳转到路由:', routeName, '参数:', { refresh: 'true', category: category.value })
      push({
        name: routeName,
        query: {
          refresh: 'true',
          category: category.value // 显式传递 category 参数
        }
      }).then(() => {
        console.log('路由跳转成功')
      }).catch(err => {
        console.error('路由跳转失败:', err)
        // 尝试使用window.location作为备选方案
        try {
          const url = router.resolve({
            name: routeName,
            query: { 
              refresh: 'true',
              category: category.value // 显式传递 category 参数
            }
          }).href
          console.log('尝试使用window.location跳转到:', url)
          window.location.href = url
        } catch (e) {
          console.error('备选跳转方案也失败:', e)
          message.error('页面跳转失败，请手动返回列表页')
        }
      })
    }).catch((e) => {
      console.error("表单数据检验未成功", e)
      message.error("表单数据检验未成功")
    });


  } finally {
    fApi.value.btn.loading(false)
  }
}

const resetFormNew = async () => {
  if (!fApi.value || !selectProcessDefinition.value) {
    return
  }
  ElMessageBox.confirm(
    '是否重置表单？',
    '表单重置确认框',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      fApi.value.resetFields();
    })
    .catch(() => {

    })

}

const getCategory= async () => {
    category.value = await  categoryRoute.getCategoryByRouteParam(route)
   // console.info("category",category.value)
}

const getFormTempId = async () => {
  if (route.query.formTempId) {
    formTempId.value = route.query.formTempId
    //getFormTemp(route.query.id)
    console.log("formTempId", formTempId.value)
  }
}

/** 新闻预览 */
const previewNews = () => {
  if (!fApi.value) {
    message.error('表单不存在');
    return;
  }
  
  try {
    // 获取当前表单数据
    const formData = fApi.value.formData();
    console.log("预览表单数据:", formData);
    
    // 存储到localStorage
    localStorage.setItem('newsPreviewData', JSON.stringify(formData));
    
    // 使用router.push导航到新闻详情页（在新窗口中打开）
    const routeData = router.resolve({
      name: 'newsPage',
      query: { preview: 'true' }
    });
    window.open(routeData.href, '_blank');
  } catch (error) {
    console.error('预览失败:', error);
    message.error('预览失败，请检查表单数据');
  }
}

/** 初始化 */
onMounted(() => {
  /*
* 获取路由上的参数
* **/
  //.info('this.route', JSON.stringify(route));

  getCategory().then(() => {
    if(!category.value){
      message.error("缺少访问参数！")
      return
    }
    console.log('流程分类:', category.value); // 打印当前分类
    console.log('路由参数:', route.query); // 打印路由参数
    getFormTempId()
    getList(category.value)
  });
  //console.info("this.category",category.value)


});

</script>
