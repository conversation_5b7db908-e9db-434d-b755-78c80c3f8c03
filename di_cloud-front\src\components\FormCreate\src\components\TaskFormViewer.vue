<template>
  <div class="task-form-viewer">
    <el-button 
      :type="buttonType" 
      :size="buttonSize" 
      @click="showDialog"
    >
      <el-icon v-if="showIcon"><Document /></el-icon>
      {{ buttonText }}
    </el-button>

    <!-- 弹窗：表单 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="60%" :destroy-on-close="true">
      <div class="task-form-container" v-loading="loading">
        <el-empty v-if="!formConfig" description="暂无表单数据" />
        <form-create 
          v-else
          v-model="formConfig.value"
          :rule="formConfig.rule" 
          :option="formConfig.option" 
          ref="formRef"
        />
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, inject } from 'vue'
import { Document } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { Ref } from 'vue'
import { getFormByProcessInstanceIdAndTaskKey } from '@/api/bpm/processInstance'
import { useRoute } from 'vue-router'

// 定义组件名称
defineOptions({ name: 'TaskFormViewer' })

// 定义按钮类型和尺寸的类型
type ButtonType = 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default' | 'text'
type ButtonSize = 'small' | 'default' | 'large'

// 定义表单配置类型
interface FormConfig {
  rule: any[];
  option: any;
  value: Record<string, any>;
}

// 定义组件的属性
const props = defineProps({
  // 流程实例ID
  processInstanceId: {
    type: String,
    default: ''
  },
  // 是否自动获取流程实例ID
  autoGetProcessInstanceId: {
    type: Boolean,
    default: false
  },
  // 任务定义key
  taskDefinitionKey: {
    type: String,
    default: ''
  },
  // 按钮文字
  buttonText: {
    type: String,
    default: '查看表单'
  },
  // 对话框标题
  dialogTitle: {
    type: String,
    default: '任务表单详情'
  },
  // 按钮类型
  buttonType: {
    type: String as () => ButtonType,
    default: 'primary'
  },
  // 按钮大小
  buttonSize: {
    type: String as () => ButtonSize,
    default: 'small'
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  }
})

// 定义组件的事件
const emit = defineEmits(['update:modelValue', 'change'])

// 获取当前路由
const route = useRoute()

// 从provide中获取可能存在的流程实例上下文
const taskContextProcessId = inject<Ref<string>>('taskContextProcessId', ref(''))
const globalProcessInstanceId = inject<Ref<string>>('globalProcessInstanceId', ref(''))

// 计算实际使用的流程实例ID
const actualProcessInstanceId = computed(() => {
  if (props.autoGetProcessInstanceId) {
    // 优先使用注入的上下文流程实例ID
    if (taskContextProcessId.value) {
      return taskContextProcessId.value
    }
    // 其次使用全局流程实例ID
    if (globalProcessInstanceId.value) {
      return globalProcessInstanceId.value
    }
    // 再次尝试从路由参数获取
    if (route.params.processInstanceId) {
      return route.params.processInstanceId as string
    }
    // 尝试从路由查询参数processInstanceId获取
    if (route.query.processInstanceId) {
      return route.query.processInstanceId as string
    }
    // 尝试从路由查询参数id获取 (适用于/bpm/process-instance/detail?id=xxx格式)
    if (route.query.id) {
      return route.query.id as string
    }
    // 如果在表格行中，尝试获取当前行数据
    if (window.currentRowProcessInstanceId) {
      return window.currentRowProcessInstanceId
    }
  }
  // 退回到手动配置的流程实例ID
  return props.processInstanceId
})

// 对话框可见性
const dialogVisible = ref(false)
// 加载状态
const loading = ref(false)
// 表单配置 - 初始化为null但使用FormConfig类型
const formConfig = ref<FormConfig | null>(null)
// 表单实例
const formRef = ref(null)

// 打印当前获取到的流程实例ID，用于调试
const showDialog = async () => {
  dialogVisible.value = true
  
  // 打印当前获取到的流程实例ID，用于调试
  console.log('流程实例ID获取途径:', {
    fromProps: props.processInstanceId,
    fromContext: taskContextProcessId.value,
    fromGlobal: globalProcessInstanceId.value,
    fromRouteParams: route.params.processInstanceId,
    fromRouteQueryProcessInstanceId: route.query.processInstanceId,
    fromRouteQueryId: route.query.id,
    fromWindowGlobal: window.currentRowProcessInstanceId,
    actualUsed: actualProcessInstanceId.value
  })
  
  await loadFormData()
}

// 加载表单数据
async function loadFormData() {
  // 判断必要参数是否存在
  const processInstanceId = actualProcessInstanceId.value
  if (!processInstanceId) {
    ElMessage.error('未指定流程实例ID，无法获取表单数据')
    return
  }
  if (!props.taskDefinitionKey) {
    ElMessage.error('未指定任务定义Key，无法获取表单数据')
    return
  }

  // 开始加载
  loading.value = true
  formConfig.value = null

  try {
    // 获取表单数据
    const res = await getFormByProcessInstanceIdAndTaskKey(processInstanceId, props.taskDefinitionKey)
    console.log('获取表单数据结果:', res)
    if (res) {
      // 解析表单配置和字段
      const formData = res
      console.log('获取到表单数据:', formData)
      
      try {
        // 解析表单配置
        const formConf = formData.formConf ? JSON.parse(formData.formConf) : {}
        // 解析表单字段
        const formFields = formData.formFields?.map(field => {
          try {
            return JSON.parse(field)
          } catch (e) {
            console.error('解析表单字段失败:', e)
            return null
          }
        }).filter(Boolean) || []
        
        // 创建FormCreate规则
        const rule = formFields
        
        // 设置表单选项（禁用按钮并设置只读）
        const option = {
          form: formConf.form || {},
          submitBtn: false,
          resetBtn: false,
          mounted: (api) => {
            // 禁用所有字段，使表单为只读
            api.disabled(true)
          }
        }
        
        // 构建表单配置
        formConfig.value = {
          rule,
          option,
          value: formData.formVariables || {}
        }
        
        console.log('表单配置已生成:', formConfig.value)
      } catch (e) {
        console.error('解析表单数据失败:', e)
        // 不显示错误消息，只在控制台打印
        formConfig.value = null
      }
    } else {
      // 不显示警告，保持静默
      formConfig.value = null
    }
  } catch (error) {
    console.error('获取表单数据失败', error)
    // 不显示错误消息，只在控制台打印
    formConfig.value = null
  } finally {
    loading.value = false
  }
}

// 声明window上可能存在的全局属性
declare global {
  interface Window {
    currentRowProcessInstanceId?: string
  }
}
</script>

<style scoped>
.task-form-container {
  min-height: 300px;
  margin-bottom: 20px;
  max-width: 750px;
  margin-left: auto;
  margin-right: auto;
}

.task-form-viewer {
  display: inline-block;
}
</style> 
