<template>
  <el-card class="mb-8px" shadow="never">
    <template #header>
      <div class="category-header">
        <span class="category-title">待办任务</span>
        <div>
          <!-- <el-button text @click="showSettings = !showSettings" size="small">
            <el-icon><Setting /></el-icon>
          </el-button> -->
          <el-button text @click="getTodoList" :loading="isLoading" size="small">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
      <el-divider class="category-divider" />
    </template>
    <div v-loading="isLoading">
      <!-- 设置面板 -->
      <el-collapse-transition>
        <div v-if="showSettings" class="settings-panel mb-15px">
          <div class="settings-header">
            <h3>分类设置</h3>
            <el-button text type="primary" @click="saveSettings">保存设置</el-button>
          </div>
          <div v-for="(type, index) in editableTaskTypes" :key="index" class="type-setting">
            <div class="type-title">
              <span>{{ type.label }}关键词:</span>
            </div>
            <el-tag
              v-for="(keyword, kidx) in type.keywords"
              :key="kidx"
              closable
              @close="removeKeyword(index, kidx)"
              class="mr-5px mb-5px"
            >
              {{ keyword }}
            </el-tag>
            <div class="add-keyword">
              <el-input
                v-model="newKeywords[index]"
                placeholder="添加关键词"
                size="small"
                @keyup.enter="addKeyword(index)"
              >
                <template #append>
                  <el-button @click="addKeyword(index)">添加</el-button>
                </template>
              </el-input>
            </div>
          </div>
        </div>
      </el-collapse-transition>

      <!-- 任务类型统计 -->
      <div class="task-stats" v-if="todoList.length">
        <div class="task-stat-item" 
          v-for="(stat, index) in taskStats" 
          :key="index"
          :class="{ active: activeType === stat.type }"
          @click="setActiveType(stat.type)">
          <div class="stat-count">{{ stat.count }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
        <div class="task-stat-item" 
          :class="{ active: activeType === '' }"
          @click="setActiveType('')">
          <div class="stat-count">{{ totalTasks }}</div>
          <div class="stat-label">全部</div>
        </div>
      </div>

      <el-empty v-if="!filteredTodoList.length" description="暂无待办任务" />
      <div v-else class="todo-list-container">
        <el-scrollbar height="390px" class="todo-list">
          <div v-for="item in filteredTodoList" :key="item.id" class="todo-item" @click="handleTodoClick(item)">
            <div class="todo-content">
              <div class="todo-title">{{ item.name }}</div>
              <div class="todo-info">
                <span>{{ item.processInstance?.name }}</span>
                <span class="todo-time">{{ formatTime(item.createTime, 'yyyy-MM-dd') }}</span>
              </div>
            </div>
            <div class="task-type-tag" :class="getTagClass(item)">
              {{ getTaskTypeName(getTaskCategory(item)) }}
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { formatTime } from '@/utils'
import * as TaskApi from '@/api/bpm/task'
import { useRouter } from 'vue-router'
import { Refresh, Setting } from '@element-plus/icons-vue'
import { ref, computed, onMounted, nextTick } from 'vue'

// 防抖函数
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number) {
  let timer: number | null = null
  return function(this: any, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay) as unknown as number
  }
}

interface TodoItem {
  id: string
  name: string
  createTime: string
  processInstance?: {
    id: string
    name: string
    category?: string
  }
  category?: string
  processCategory?: string
  _cachedCategory?: string // 缓存分类结果
}

// 任务类型定义
const DEFAULT_TASK_TYPES = [
  { 
    type: 'oa_xf', 
    label: '信访件', 
    color: 'tag-danger',
    keywords: ['信访']
  },
  { 
    type: 'oa_xs', 
    label: '线索件', 
    color: 'tag-warning',
    keywords: ['线索']
  },
  { 
    type: 'oa_la', 
    label: '立案件', 
    color: 'tag-success',
    keywords: ['立案']
  },
  { 
    type: 'oa_news', 
    label: '新闻类', 
    color: 'tag-success',
    keywords: ['新闻']
  },
  { 
    type: 'oa_task', 
    label: '任务类', 
    color: 'tag-info',
    keywords: ['任务']
  },
  { 
    type: 'other', 
    label: '其他', 
    color: 'tag-primary',
    keywords: []
  }
]

// 配置版本标识，用于版本更新时强制更新配置
const CONFIG_VERSION = '1.1';

defineOptions({ name: 'TodoCard' })

const router = useRouter()
const isLoading = ref(false)
const todoList = ref<TodoItem[]>([])
const activeType = ref('') // 当前选中的任务类型，空字符串表示全部
const showSettings = ref(false) // 是否显示设置面板
const editableTaskTypes = ref(JSON.parse(JSON.stringify(DEFAULT_TASK_TYPES))) // 可编辑的任务类型配置
const newKeywords = ref(['', '', '', '', '', '']) // 新关键词输入

// 尝试从localStorage加载保存的配置
const loadSavedConfig = () => {
  try {
    const savedConfig = localStorage.getItem('todoTaskTypeConfig');
    const savedVersion = localStorage.getItem('todoTaskTypeConfigVersion');
    
    // 只有当配置存在且版本匹配时才加载
    if (savedConfig && savedVersion === CONFIG_VERSION) {
      editableTaskTypes.value = JSON.parse(savedConfig);
    } else if (savedConfig && savedVersion !== CONFIG_VERSION) {
      // 版本不匹配，使用新配置并保存
      localStorage.setItem('todoTaskTypeConfig', JSON.stringify(DEFAULT_TASK_TYPES));
      localStorage.setItem('todoTaskTypeConfigVersion', CONFIG_VERSION);
    }
  } catch (error) {
    console.error("加载保存的配置失败:", error);
  }
}

// 保存配置到localStorage
const saveSettings = () => {
  try {
    // 检查是否与默认配置相同，如果相同则不保存
    const configStr = JSON.stringify(editableTaskTypes.value);
    const defaultConfigStr = JSON.stringify(DEFAULT_TASK_TYPES);
    
    localStorage.setItem('todoTaskTypeConfig', configStr);
    localStorage.setItem('todoTaskTypeConfigVersion', CONFIG_VERSION);
    
    // 重新加载任务列表，使用新配置进行分类
    getTodoList();
    // 隐藏设置面板
    showSettings.value = false;
  } catch (error) {
    console.error("保存配置失败:", error);
  }
}

// 添加关键词
const addKeyword = (typeIndex: number) => {
  const keyword = newKeywords.value[typeIndex].trim();
  if (keyword && !editableTaskTypes.value[typeIndex].keywords.includes(keyword)) {
    editableTaskTypes.value[typeIndex].keywords.push(keyword);
    newKeywords.value[typeIndex] = '';
  }
}

// 移除关键词
const removeKeyword = (typeIndex: number, keywordIndex: number) => {
  editableTaskTypes.value[typeIndex].keywords.splice(keywordIndex, 1);
}

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
})

// 获取当前使用的任务类型配置
const TASK_TYPES = computed(() => {
  return editableTaskTypes.value;
});

// 获取任务类型
const getTaskCategory = (item: TodoItem): string => {
  // 如果已经计算过分类，直接返回缓存结果
  if (item._cachedCategory) {
    return item._cachedCategory;
  }
  
  // 获取流程相关的所有可能字段用于判断
  const processName = item.processInstance?.name || '';
  const taskName = item.name || '';
  const category = (item.category || 
                  item.processCategory || 
                  item.processInstance?.category || 
                  '').toLowerCase();
  
  // 组合所有文本字段进行匹配
  const fullText = `${processName} ${taskName} ${category}`.toLowerCase();
  
  // 按优先级从高到低匹配关键词
  for (const typeInfo of TASK_TYPES.value) {
    // 如果分类字段直接包含类型代码，优先使用
    if (category.includes(typeInfo.type)) {
      item._cachedCategory = typeInfo.type;
      return typeInfo.type;
    }
    
    // 检查关键词匹配
    if (typeInfo.keywords && typeInfo.keywords.length > 0) {
      for (const keyword of typeInfo.keywords) {
        if (fullText.includes(keyword.toLowerCase())) {
          item._cachedCategory = typeInfo.type;
          return typeInfo.type;
        }
      }
    }
  }
  
  // 没有匹配到任何类型，返回"其他"
  item._cachedCategory = 'other';
  return 'other';
}

// 获取任务类型名称
const getTaskTypeName = (category: string): string => {
  const typeInfo = TASK_TYPES.value.find(t => t.type === category);
  return typeInfo ? typeInfo.label : '其他';
}

// 获取任务标签样式类
const getTagClass = (item: TodoItem): string => {
  const category = getTaskCategory(item);
  const typeInfo = TASK_TYPES.value.find(t => t.type === category);
  return typeInfo ? typeInfo.color : 'tag-primary';
}

// 设置当前任务类型
const setActiveType = (type: string) => {
  activeType.value = type;
}

// 计算任务统计
const taskStats = computed(() => {
  // 使用Map来统计各类型任务数量，减少重复计算
  const countMap = new Map<string, number>();
  
  // 初始化所有类型计数为0
  TASK_TYPES.value.forEach(type => {
    countMap.set(type.type, 0);
  });
  
  // 一次遍历统计所有类型数量
  for (const item of todoList.value) {
    const category = getTaskCategory(item);
    countMap.set(category, (countMap.get(category) || 0) + 1);
  }
  
  // 转换为需要的输出格式
  const stats: Array<{ type: string; label: string; count: number }> = [];
  
  for (const typeInfo of TASK_TYPES.value) {
    const count = countMap.get(typeInfo.type) || 0;
    if (count > 0) {
      stats.push({
        type: typeInfo.type,
        label: typeInfo.label,
        count: count
      });
    }
  }
  
  return stats;
});

// 总任务数
const totalTasks = computed(() => todoList.value.length);

// 过滤后的任务列表
const filteredTodoList = computed(() => {
  if (!activeType.value) {
    return todoList.value;
  }
  return todoList.value.filter(item => 
    getTaskCategory(item) === activeType.value
  );
});

// 获取待办任务列表
const getTodoList = async () => {
  isLoading.value = true
  try {
    const res = await TaskApi.getTaskTodoPage({
      pageNo: 1,
      pageSize: 99
    })
    // 移除不必要的日志输出，提高性能
    todoList.value = res.list || []
  } finally {
    isLoading.value = false
  }
}

// 创建防抖版本的获取任务列表函数
const debouncedGetTodoList = debounce(getTodoList, 300)

// 点击待办任务
const handleTodoClick = (item) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: item.processInstance.id,
      taskId: item.id
    }
  })
}

// 查看全部待办任务
const viewAllTasks = () => {
  router.push({
    name: 'BpmTodoTask'
  })
}

// 初始化
onMounted(() => {
  loadSavedConfig();
  // 使用setTimeout延迟加载，避免阻塞页面渲染
  setTimeout(() => {
    getTodoList();
  }, 100);
})
</script>

<style lang="scss" scoped>
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.category-divider {
  margin: 10px 0;
}

.settings-panel {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 15px;
  
  .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    h3 {
      margin: 0;
      font-size: 16px;
    }
  }
  
  .type-setting {
    margin-bottom: 15px;
    
    .type-title {
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .add-keyword {
      margin-top: 10px;
    }
  }
}

.todo-list-container {
  /* 固定高度，相当于8个任务项的高度 */
  max-height: 390px;
  overflow: hidden;
  position: relative;
}

.todo-list {
  max-height: 390px;
  overflow-y: auto;
  scrollbar-width: thin;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #ccc;
  }
  
  .todo-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f5f7fa;
    }

    .todo-content {
      flex: 1;
      margin-right: 12px;
      
      .todo-title {
        font-size: 14px;
        margin-bottom: 4px;
        color: #303133;
      }

      .todo-info {
        font-size: 12px;
        color: #909399;

        .todo-time {
          margin-left: 12px;
        }
      }
    }
  }
}

.task-type-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
  line-height: 1.2;
}

.tag-primary {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

.tag-success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #e1f3d8;
}

.tag-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #faecd8;
}

.tag-danger {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fde2e2;
}

.tag-info {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #e9e9eb;
}

.task-stats {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  
  .task-stat-item {
    flex: 1;
    min-width: 80px;
    text-align: center;
    padding: 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    margin: 0 4px 8px 0;
    background-color: #f5f7fa;
    
    &:hover {
      background-color: #e6f1fc;
    }
    
    &.active {
      background-color: #e6f1fc;
      color: var(--el-color-primary);
      border: 1px solid var(--el-color-primary);
    }
    
    .stat-count {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 4px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #606266;
    }
  }
}

.view-all {
  text-align: center;
  margin-top: 12px;
}

.mr-5px {
  margin-right: 5px;
}

.mb-5px {
  margin-bottom: 5px;
}

.mb-15px {
  margin-bottom: 15px;
}
</style> 