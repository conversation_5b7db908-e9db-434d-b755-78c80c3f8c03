<template>

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="流程标题" prop="queryVariables.form_title">
        <el-input
          v-model="queryParams.queryVariables.form_title"
          placeholder="请输入流程标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="流程状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择流程状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="流程名称" align="center" prop="name" min-width="200px" fixed="left" />
      <el-table-column label="流程标题" align="center" prop="processInstanceExt.processInstanceTitle" min-width="200px" />
      <el-table-column
        label="流程分类"
        align="center"
        prop="categoryName"
        min-width="100"
        fixed="left"
      />
      <el-table-column label="流程状态" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="发起时间"
        align="center"
        prop="startTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column align="center" label="耗时" prop="durationInMillis" width="160">
        <template #default="scope">
          {{ scope.row.durationInMillis > 0 ? formatPast2(scope.row.durationInMillis) : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="当前审批任务" align="center" prop="tasks" min-width="120px">
        <template #default="scope">
          <el-button type="primary" v-for="task in [...new Map(scope.row.tasks?.map(task => [task.name, task]) || []).values()]" :key="task.id" link>
            <span>{{ task.name }}</span>
          </el-button>
        </template>
      </el-table-column>
<!--      <el-table-column label="流程编号" align="center" prop="id" min-width="320px" />-->
      <el-table-column label="操作" align="center" fixed="right" width="180">
        <template #default="scope">
          <el-button
            link
            type="primary"
            v-hasPermi="['bpm:process-instance:cancel']"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
<!--          <el-button
            link
            type="primary"
            v-if="scope.row.status === 1"
            v-hasPermi="['bpm:process-instance:query']"
            @click="handleCancel(scope.row)"
          >
            取消
          </el-button>-->
<!--          <el-button link type="primary" v-else @click="handleCreate(scope.row)">-->
<!--            重新发起-->
<!--          </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, formatPast2 } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { CategoryApi, categoryRoute } from '@/api/bpm/category'
import { ProcessInstanceVO } from '@/api/bpm/processInstance'
import * as DefinitionApi from '@/api/bpm/definition'
import { cloneDeep } from 'lodash-es'
import { childSelector } from 'markmap-common'

defineOptions({ name: 'BpmProcessInstanceMy' })

const router = useRouter() // 路由
const route = useRoute() // 路由
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const category=ref(null)
const xsbd_wtlb = ref(null) //线索表单问题类别
const dict_xf_wtlb =ref(null) //信访表单问题类别
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  processDefinitionKey: undefined,
  category: undefined,
  status: undefined,
  createTime: [],
  queryVariables:{
    form_title:'',//流程标题
  }
})

const queryFormRef = ref() // 搜索的表单
const props = {
  expandTrigger: 'hover' as const,
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.category = category.value
    // 将queryVariable中的对象转化为json字符串便于后端处理
    const query = cloneDeep(queryParams)
    // console.log("query.queryVariables.wtlb",query.queryVariables.wtlb)
    // query.queryVariables.wtlb = query.queryVariables.wtlb+'' //转换为字符串方便后端处理
    const json = JSON.stringify(query.queryVariables)
    query.queryVariables = json
    const data = await ProcessInstanceApi.getProcessInstanceMyPage(query)
    
    // 对列表数据进行处理 - 仅处理办理时限
    list.value = data.list.map(item => {
      // 处理办理时限，使用processInstanceExt.processInstanceLimitDate
      if (item.processInstanceExt && item.processInstanceExt.processInstanceLimitDate) {
        // 如果没有formVariables，则初始化为空对象
        if (!item.formVariables) {
          item.formVariables = {};
        }
        // 设置办理时限
        item.formVariables.blsx = item.processInstanceExt.processInstanceLimitDate;
      }
      
      return item;
    });
    
    total.value = data.total
    console.info("list",JSON.stringify(list.value))
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 发起流程操作 **/
const handleCreate = async (row?: ProcessInstanceVO) => {
  // 如果是【业务表单】，不支持重新发起
  if (row?.id) {
    const processDefinitionDetail = await DefinitionApi.getProcessDefinition(
      row.processDefinitionId
    )
    debugger
    if (processDefinitionDetail.formType === 20) {
      message.error('重新发起流程失败，原因：该流程使用业务表单，不支持重新发起')
      return
    }
  }
  // 跳转发起流程界面
  await router.push({
    name: 'BpmProcessInstanceCreate',
    query: { processInstanceId: row?.id }
  })
}

/** 查看详情 */
const handleDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消按钮操作 */
const handleCancel = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.id, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

const getCategory=async ()=>{
  category.value=await categoryRoute.getCategoryByRouteParam(route)
}

const options = [
  {
    value: '1',
    label: '违纪行为',
    children: [
      {
        value: '2',
        label: '违反政治纪律',
      },
      {
        value: '3',
        label: '违反组织纪律',
      },
      {
        value: '4',
        label: '违反廉洁纪律',
      },
      {
        value: '5',
        label: '违反群众纪律',
      },
      {
        value: '6',
        label: '违反工作纪律',
      },
      {
        value: '7',
        label: '违反生活纪律',
      }
    ],
  },
  {
    value: '8',
    label: '职务违法犯罪行为',
    children: [
      {
        value: '9',
        label: '贪污贿赂',
      },
      {
        value: '10',
        label: '滥用职权',
      },  
      {
        value: '11',
        label: '玩忽职守',
      },  
      {
        value: '12',
        label: '徇私舞弊',
      },  
      {
        value: '13',
        label: '重大责任事故',
      },
      {
        value: '14',
        label: '其他职务违法犯罪行为',
      }       
    ]
  }, 
  {
    value: '15',
    label: '其他违法犯罪行为',
    children: [
      {
        value: '16',
        label: '侵犯财产',
      },
      {
        value: '17',
        label: '破坏社会主义市场经济秩序',
      },  
      {
        value: '18',
        label: '妨害社会管理秩序',
      },  
      {
        value: '19',
        label: '侵犯公民人身权利民主权利',
      }  
    ]
  },  
  {
    value: '20',
    label: '其他',
    children:[
      {
        value: '21',
        label: '是否违反中央八项规定精神',
      }  
    ]
  }    
  
]
/** 激活时 **/
onActivated(() => {
  getList(category.value)
})

/** 初始化 **/
onMounted(async () => {
  getCategory().then(() => {
     getList()
  })

 // categoryList.value = await CategoryApi.getCategorySimpleList()
})
</script>
