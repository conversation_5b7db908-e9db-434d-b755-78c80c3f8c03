import request from '@/config/axios'

// 统计数据的返回类型定义
export interface StatisticsResponseData {
  total: {
    totalCount: number | null
    runningCount: number | null
    approvedCount: number | null
    rejectedCount: number | null
    cancelCount: number | null
    approvedRate: string | null
  }
  page: {
    list: Array<{
      createTime: number
      updateTime: number
      creator: string
      updater: string
      deleted: boolean
      processInstanceId: string
      deptId: number
      deptName: string
      caseNumber: string | null
      processInstanceTitle: string
      reviewerUserId: number
      organizeUserId: number
      organizeUserName: string
      processInstanceAcceptanceDate: string | null
      processXfCategory?: number
      status?: string
    }>
    total: number
  }
}

// 获取线索统计数据
export const getStatistics = (params: {
  category?: string
  pageNo?: number
  pageSize?: number
  startTime?: string
  endTime?: string
  deptId?: number | string
  organizingDeptId?: number | string
  clueId?: string
  status?: string[]
  handlingUnit?: string
  handler?: string
  acceptanceDateRange?: string[]
  timeLimitStatus?: string
}) => {
  return request.get({
    url: '/bpm/process-instance/statistics/statistics',
    params: {
      category: 'oa_xs',
      pageNo: 1,
      pageSize: 10,
      ...params
    }
  }) as Promise<StatisticsResponseData>
} 