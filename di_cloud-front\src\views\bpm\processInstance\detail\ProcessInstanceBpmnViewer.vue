<template>
  <el-card v-loading="loading" class="box-card">
    <template #header>
      <span class="el-icon-picture-outline">流程图</span>
    </template>
    <MyProcessViewer
      key="designer"
      :activityData="activityList"
      :prefix="bpmnControlForm.prefix"
      :processInstanceData="processInstance"
      :taskData="tasks"
      :value="bpmnXml"
      v-bind="bpmnControlForm"
    />
  </el-card>
</template>
<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { MyProcessViewer } from '@/components/bpmnProcessDesigner/package'
import * as ActivityApi from '@/api/bpm/activity'

defineOptions({ name: 'BpmProcessInstanceBpmnViewer' })

const props = defineProps({
  loading: propTypes.bool, // 是否加载中
  id: propTypes.string, // 流程实例的编号
  processInstance: propTypes.any, // 流程实例的信息
  tasks: propTypes.array, // 流程任务的数组
  bpmnXml: propTypes.string // BPMN XML
})

const bpmnControlForm = ref({
  prefix: 'flowable'
})
const activityList = ref([]) // 任务列表

/** 只有 loading 完成时，才去加载流程列表 */
watch(
  () => props.loading,
  async (loading) => {
    if (props.id) {
      // 不管loading状态如何，都重新获取活动列表
      try {
        // 先清空现有活动列表，确保能够触发变更
        activityList.value = []
        // 等待DOM更新
        await nextTick()
        // 重新获取最新的活动列表数据
        const result = await ActivityApi.getActivityList({
          processInstanceId: props.id
        })
        console.log('加载流程活动列表:', result ? result.length : 0, '个节点')
        // 使用timeout确保Vue有足够时间处理状态变化
        setTimeout(() => {
          activityList.value = result || []
        }, 100)
      } catch (e) {
        console.error('获取活动列表失败:', e)
      }
    }
  },
  { immediate: true } // 立即执行一次
)

// 监听bpmnXml变化，确保在XML更新时主动刷新活动列表
watch(
  () => props.bpmnXml,
  async () => {
    if (props.id) {
      try {
        // 获取最新的活动列表数据
        const result = await ActivityApi.getActivityList({
          processInstanceId: props.id
        })
        console.log('XML变化，重新加载流程活动列表:', result ? result.length : 0, '个节点')
        activityList.value = result || []
      } catch (e) {
        console.error('获取活动列表失败:', e)
      }
    }
  }
)

// 监听taskData变化，确保任务数据更新时也刷新活动列表
watch(
  () => props.tasks,
  async () => {
    if (props.id && props.tasks && props.tasks.length > 0) {
      try {
        // 获取最新的活动列表数据
        const result = await ActivityApi.getActivityList({
          processInstanceId: props.id
        })
        console.log('任务数据变化，重新加载流程活动列表:', result ? result.length : 0, '个节点')
        activityList.value = []
        // 使用nextTick和timeout确保DOM能够正确更新
        await nextTick()
        setTimeout(() => {
          activityList.value = result || []
        }, 100)
      } catch (e) {
        console.error('获取活动列表失败:', e)
      }
    }
  }
)
</script>
<style>
.box-card {
  width: 100%;
  margin-bottom: 20px;
}
</style>
