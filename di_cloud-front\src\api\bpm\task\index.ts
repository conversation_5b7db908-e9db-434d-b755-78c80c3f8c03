import request from '@/config/axios'

/**
 * 任务状态枚举
 */
export enum TaskStatusEnum {
  /**
   * 未开始
   */
  NOT_START = -1,

   /**
   * 待审批
   */
   WAIT = 0,
  /**
   * 审批中
   */
  RUNNING = 1,
  /**
   * 审批通过
   */
  APPROVE = 2,

  /**
   * 审批不通过
   */
  REJECT = 3,
  
  /**
   * 已取消
   */
  CANCEL = 4,
  /**
   * 已退回
   */
  RETURN = 5,
  /**
   * 委派中
   */
  DELEGATE = 6,
  /**
   * 审批通过中
   */
  APPROVING = 7,

}

export type TaskVO = {
  id: number
}

export const getTaskTodoPage = async (params: any) => {
  return await request.get({ url: '/bpm/task/todo-page', params })
}

export const getTaskDonePage = async (params: any) => {
  return await request.get({ url: '/bpm/task/done-page', params })
}

export const getTaskManagerPage = async (params: any) => {
  return await request.get({ url: '/bpm/task/manager-page', params })
}

export const approveTask = async (data: any) => {
  return await request.put({ url: '/bpm/task/approve', data })
}

export const rejectTask = async (data: any) => {
  return await request.put({ url: '/bpm/task/reject', data })
}

export const getTaskListByProcessInstanceId = async (processInstanceId: string) => {
  return await request.get({
    url: '/bpm/task/list-by-process-instance-id?processInstanceId=' + processInstanceId
  })
}

// 获取所有可退回的节点
export const getTaskListByReturn = async (id: string) => {
  return await request.get({ url: '/bpm/task/list-by-return', params: { id } })
}

// 退回
export const returnTask = async (data: any) => {
  return await request.put({ url: '/bpm/task/return', data })
}

// 退回到发起节点（新接口，具体地址待确定）
export const returnToStartNode = async (data: any) => {
  // TODO: 具体接口地址待后端确定
  return await request.put({ url: '/bpm/task/return-to-start', data })
}

// 委派
export const delegateTask = async (data: any) => {
  return await request.put({ url: '/bpm/task/delegate', data })
}

// 转派
export const transferTask = async (data: any) => {
  return await request.put({ url: '/bpm/task/transfer', data })
}

// 加签
export const signCreateTask = async (data: any) => {
  return await request.put({ url: '/bpm/task/create-sign', data })
}

// 减签
export const signDeleteTask = async (data: any) => {
  return await request.delete({ url: '/bpm/task/delete-sign', data })
}

// 获取减签任务列表
export const getChildrenTaskList = async (id: string) => {
  return await request.get({ url: '/bpm/task/list-by-parent-task-id?parentTaskId=' + id })
}

// 获取当前任务的上一个任务
export const getPreviousTask = async (id: string) => {
  return await request.get({ url: '/bpm/task/previous-task-by-current-task', params: { id } })
}

//申请延期
export const applyTaskDelay = async (data: any) => {
  return await request.put({ url: '/bpm/task/apply-delay', data })
}

/** 检查任务是否是申请延期任务 */
export const checkTaskApplyDelay = (id: string) => {
  return request.get({ url: '/bpm/task/check-task-apply-delay', params: { id } })
}

/** 审批延期申请任务 */
export const approveApplyDelay = (data: any) => {
  return request.put({ url: '/bpm/task/approve-apply-delay', data })
}

// export const getTask = async (id: string) => {
//   return await request.get({ url: '/bpm/task/get?id=' + id })
// }
