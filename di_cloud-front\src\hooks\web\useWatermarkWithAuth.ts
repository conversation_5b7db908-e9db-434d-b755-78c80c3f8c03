import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import { useWatermark } from './useWatermark'
import { useUserStore } from '@/store/modules/user'

/**
 * 带有认证状态的水印钩子
 * 仅在用户登录后显示水印，用户退出后自动移除水印
 * @param appendEl 水印附加的DOM元素，默认为document.body
 * @returns 水印管理对象
 */
export function useWatermarkWithAuth(appendEl: HTMLElement | null = document.body) {
  const { setWatermark, clear } = useWatermark(appendEl)
  const watermarkEnabled = ref(false)
  const userStore = useUserStore()
  
  // 添加水印
  const applyWatermark = () => {
    const username = userStore.getUser.nickname || '用户'
    setWatermark(username)
    watermarkEnabled.value = true
  }
  
  // 移除水印
  const removeWatermark = () => {
    clear()
    watermarkEnabled.value = false
  }
  
  // 监听用户登录状态 - 注意：必须在定义了 applyWatermark 和 removeWatermark 之后
  watch(
    () => userStore.getIsSetUser,
    (isLoggedIn) => {
      if (isLoggedIn) {
        // 登录状态，设置水印
        applyWatermark()
      } else {
        // 未登录状态，清除水印
        removeWatermark()
      }
    },
    { immediate: true }
  )
  
  // 组件挂载时检查状态
  onMounted(() => {
    if (userStore.getIsSetUser) {
      applyWatermark()
    }
  })
  
  // 组件卸载前清除
  onBeforeUnmount(() => {
    removeWatermark()
  })
  
  return {
    applyWatermark,
    removeWatermark,
    watermarkEnabled,
    // 原始方法
    setWatermark,
    clear
  }
} 