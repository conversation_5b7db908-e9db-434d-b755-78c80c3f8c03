export interface TaskData {
  id: string
  taskName: string
  taskType: string
  taskTypeLabel?: string
  completionRate?: number
  feedbackCount?: number
  totalCount?: number
  createTime?: string
  dueTime?: string
  receiverName?: string
  description?: string
  taskEngineReleaseDate?: string
  taskEngineReleaseTime?: string
}

export interface TaskFeedback {
  id: string
  taskId: string
  taskName: string
  taskType: string
  taskTypeLabel?: string
  processInstanceId: string
  deptId?: number
  deptName?: string
  sendTime: string
  dueTime: string
  receiverName: string
  status: 'pending' | 'completed'
  processInstanceState: boolean
  processInstanceTaskState: number
  isUrged: boolean
  processInstanceUrgingNum?: number
  description?: string
  receiveTime?: string
  feedbackContent?: string
  feedbackTime?: string
  feedbackPerson?: string
  attachments?: Array<{
    name: string
    url: string
  }>
  groupAttachments?: Array<{
    name: string
    url: string
  }>
  tasks?: Array<{
    id: string
    assignee: string
    assigneeName: string
  }>
} 