<!-- 工作流 - 抄送我的流程 -->
<template>


  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" class="-mb-15px" label-width="68px">
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.processInstanceName"
          @keyup.enter="handleQuery"
          class="!w-240px"
          clearable
          placeholder="请输入流程名称"
        />
      </el-form-item>
      <el-form-item label="抄送时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="流程名" prop="processInstanceName" min-width="180" />
      <el-table-column align="center" label="流程标题" min-width="200">
        <template #default="scope">
          {{ scope.row.processInstanceExt?.processInstanceTitle || '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="当前审批任务" align="center" min-width="200" :show-overflow-tooltip="false">
        <template #default="scope">
          <div class="wrap-task-buttons">
            <el-button type="primary" v-for="task in scope.row.tasks" :key="task.id" link>
              <span class="task-name">{{ task.name }}</span>
            </el-button>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="流程状态" prop="status" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <!-- 新增办理时限列 -->
      <el-table-column
        label="办理时限"
        align="center"
        width="180"
        v-if="category === 'oa_xs'"
      >
        <template #default="scope">
          {{ scope.row.formVariables?.blsx || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="流程发起人" prop="startUserName" min-width="100" />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="流程发起时间"
        prop="processInstanceStartTime"
        width="180"
      />
      <el-table-column align="center" label="抄送任务" prop="taskName" min-width="180" />
      <el-table-column align="center" label="抄送人" prop="creatorName" min-width="100" />
      <el-table-column
        align="center"
        label="抄送时间"
        prop="createTime"
        width="180"
        :formatter="dateFormatter"
      />

      <el-table-column align="center" label="操作" fixed="right" width="160">
        <template #default="scope">
          <div class="button-group">
            <el-button link type="primary" @click="handleAudit(scope.row)">详情</el-button>
            <el-button 
              v-if="category === 'oa_xs' && !scope.row.processInstanceState && userStore.getUser.id === scope.row.processInstanceExt?.reviewerUserId&&scope.row.status === 1" 
              link 
              type="warning" 
              v-hasPermi="['bpm:task:urge']"	
              @click="handleUrge(scope.row)"
            >
              催办
            </el-button>
            <el-button 
              v-if="scope.row.formVariables?.blsx" 
              link 
              type="primary" 
              @click="handleViewHistory(scope.row)"
            >
            延期历史记录
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { dateFormatter } from '@/utils/formatTime'
import { getTaskCopyPage } from '@/api/bpm/task'
import { DICT_TYPE } from '@/utils/dict'
import { urgeTask } from '@/api/bpm/processInstance'
import { useAppStore } from '@/store/modules/app'
import { useMessage } from '@/hooks/web/useMessage'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { categoryRoute } from "@/api/bpm/category";
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'BpmProcessInstanceCopy' })
const message = useMessage() // 消息弹窗
const { push } = useRouter() // 路由
const route = useRoute()
const userStore = useUserStore() // 用户 store
const loading = ref(false) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const category= ref('')// 流程分类
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  processInstanceId: '',
  processInstanceName: '',
  createTime: [],
  processCategoryCode:''
})
const queryFormRef = ref() // 搜索的表单

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.processCategoryCode = category
    const data = await ProcessInstanceApi.getProcessInstanceCopyPage(queryParams)
    list.value = data.list
    total.value = data.total
    
    // 处理流程数据 - 添加processInstanceExt对象
    if (list.value && list.value.length > 0) {
      list.value.forEach((item, index) => {
        // 确保processInstanceExt对象存在
        if (!item.processInstanceExt) {
          item.processInstanceExt = {};
        }
        
        // 如果有processInstanceTitle但没有在processInstanceExt中，则添加
        if (item.processInstanceTitle && !item.processInstanceExt.processInstanceTitle) {
          item.processInstanceExt.processInstanceTitle = item.processInstanceTitle;
          console.log(`设置抄送记录[${item.id}]的流程标题: ${item.processInstanceExt.processInstanceTitle}`);
        }
        
        // 处理办理时限数据，使用processInstanceExt.processInstanceLimitDate字段
        if (item.processInstanceExt && item.processInstanceExt.processInstanceLimitDate) {
          // 获取日期字符串，格式为YYYY-MM-DD
          const formattedDate = item.processInstanceExt.processInstanceLimitDate;
          
          // 将办理时限添加到formVariables中
          if (!item.formVariables) {
            item.formVariables = {};
          }
          item.formVariables.blsx = formattedDate;
          console.log(`设置抄送记录[${item.id}]的办理时限: ${formattedDate}`);
        }
      });
      console.log("所有抄送数据处理完成");
    }
  } finally {
    loading.value = false
  }
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  const query = {
    id: row.processInstanceId,
    activityId: undefined
  }
  if (row.activityId) {
    query.activityId = row.activityId
  }
  push({
    name: 'BpmProcessInstanceDetail',
    query: query
  })
}

/** 催办操作 */
const handleUrge = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要催办该流程吗？', '催办确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 调用催办API
    await ProcessInstanceApi.urgeProcessInstances({
      processInstanceIds: [row.processInstanceId]
    })
    
    ElMessage.success('催办成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('催办失败:', error)
      ElMessage.error('催办失败，请稍后重试')
    }
  }
}

/** 查看历史申请记录 */
const handleViewHistory = (row) => {
  console.log('查看历史申请记录', row)
  if (!row.id) {
    message.warning('无法获取流程信息')
    return
  }
  
  console.log('查看历史申请记录', row.processInstanceId)
  
  // 跳转到历史申请列表页面
  push({
    name: 'BpmProcessInstanceHistory',
    query: {
      processInstanceId: row.processInstanceId
    }
  })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList(category.value)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
const getCategory = async () => {
  category.value = await categoryRoute.getCategoryByRouteParam(route)

}
/** 初始化 **/
onMounted(() => {
  getCategory().then(() => {
    if(!category.value){
      ElMessage.error("缺少访问参数！")
      return
    }
    getList(category.value)
  })
})
</script>

<style lang="scss" scoped>
.wrap-cell {
  .wrap-text {
    white-space: pre-wrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 60px;
  }
}

.wrap-task-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  
  .task-name {
    display: inline-block;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 5px;
}
</style>
