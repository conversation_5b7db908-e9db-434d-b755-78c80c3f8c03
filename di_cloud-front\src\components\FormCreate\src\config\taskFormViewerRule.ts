import { makeRequiredRule, localeProps } from '@/components/FormCreate/src/utils'
import { markRaw } from 'vue'
import { Document } from '@element-plus/icons-vue'
import type { Rule } from '@form-create/element-ui'

const name = 'TaskFormViewer'

export const useTaskFormViewerRule = () => {
  return {
    // 插入菜单位置
    menu: 'aide',
    // 图标
    icon: 'icon-document',
    // 组件名称
    label: '任务表单查看器',
    // 组件ID，唯一
    name: name,
    // 是否可以操作
    mask: false,
    // 定义组件的事件
    event: ['change'],
    // 定义组件的渲染规则
    rule({ t }) {
      return {
        type: name,
        title: t(name + '.title', '任务表单查看'),
        field: 'taskFormViewer',
        props: {},
      }
    },
    // 自定义组件的属性配置
    props(_, { t }) {
      return localeProps(t, name + '.props', [
        makeRequiredRule(),
        {
          type: 'input',
          field: 'field',
          title: '字段名称',
          info: '表单提交时的字段名',
          props: {
            placeholder: '请输入字段名称'
          }
        },
        {
          type: 'input',
          field: 'buttonText',
          title: '按钮文字',
          value: '查看表单',
          props: {
            placeholder: '请输入按钮文字'
          }
        },
        {
          type: 'input',
          field: 'dialogTitle',
          title: '对话框标题',
          value: '任务表单详情',
          props: {
            placeholder: '请输入对话框标题'
          }
        },
        {
          type: 'input',
          field: 'processInstanceId',
          title: '流程实例ID',
          value: '',
          props: {
            placeholder: '请输入流程实例ID'
          },
          info: '当前流程实例的ID，如启用自动获取则可不填'
        },
        {
          type: 'switch',
          field: 'autoGetProcessInstanceId',
          title: '自动获取流程实例ID',
          value: false,
          info: '启用后将自动从上下文中获取流程实例ID'
        },
        {
          type: 'input',
          field: 'taskDefinitionKey',
          title: '任务定义Key',
          value: '',
          props: {
            placeholder: '例如：userTask1'
          },
          info: '要查看的任务的taskDefinitionKey'
        },
        {
          type: 'select',
          field: 'buttonType',
          title: '按钮类型',
          value: 'primary',
          options: [
            { label: '主要按钮', value: 'primary' },
            { label: '成功按钮', value: 'success' },
            { label: '警告按钮', value: 'warning' },
            { label: '危险按钮', value: 'danger' },
            { label: '信息按钮', value: 'info' },
            { label: '文本按钮', value: 'text' }
          ]
        },
        {
          type: 'select',
          field: 'buttonSize',
          title: '按钮大小',
          value: 'small',
          options: [
            { label: '大', value: 'large' },
            { label: '默认', value: 'default' },
            { label: '小', value: 'small' }
          ]
        },
        {
          type: 'switch',
          field: 'showIcon',
          title: '显示图标',
          value: true
        }
      ])
    }
  }
}

/**
 * 注册组件: FormCreate 表单设计器
 */
export default function createTaskFormViewerRule(): Rule {
  return {
    type: 'taskFormViewer',
    field: 'taskFormViewer',
    title: '任务表单查看器',
    native: false,
    info: '',
    // 组件的属性
    props: {
      processInstanceId: '',
      autoGetProcessInstanceId: false,
      taskDefinitionKey: '',
      buttonText: '查看表单',
      dialogTitle: '任务表单详情',
      buttonType: 'primary',
      buttonSize: 'small',
      showIcon: true
    },
    // 组件的事件
    on: {},
    // 组件的插槽
    children: [],
    // 组件的样式
    style: {},
    // 配置项
    options: [
      {
        name: 'processInstanceId',
        label: '流程实例ID',
        type: 'input',
        info: '流程实例ID，必填。可通过自动获取配置实现自动填充',
        placeholder: '请输入流程实例ID',
        required: true
      },
      {
        name: 'autoGetProcessInstanceId',
        label: '自动获取流程实例ID',
        type: 'switch',
        info: '开启后将自动从当前页面上下文获取流程实例ID，无需手动配置'
      },
      {
        name: 'taskDefinitionKey',
        label: '任务定义Key',
        type: 'input',
        info: '任务定义Key，必填',
        placeholder: '请输入任务定义Key',
        required: true
      },
      {
        name: 'buttonText',
        label: '按钮文本',
        type: 'input',
        info: '按钮上显示的文本',
        placeholder: '请输入按钮文本'
      },
      {
        name: 'dialogTitle',
        label: '对话框标题',
        type: 'input',
        info: '对话框标题',
        placeholder: '请输入对话框标题'
      },
      {
        name: 'buttonType',
        label: '按钮类型',
        type: 'select',
        info: '按钮的类型',
        options: [
          { label: '主要按钮', value: 'primary' },
          { label: '成功按钮', value: 'success' },
          { label: '信息按钮', value: 'info' },
          { label: '警告按钮', value: 'warning' },
          { label: '危险按钮', value: 'danger' },
          { label: '默认按钮', value: 'default' }
        ]
      },
      {
        name: 'buttonSize',
        label: '按钮尺寸',
        type: 'select',
        info: '按钮的尺寸',
        options: [
          { label: '大', value: 'large' },
          { label: '默认', value: 'default' },
          { label: '小', value: 'small' }
        ]
      },
      {
        name: 'showIcon',
        label: '显示图标',
        type: 'switch',
        info: '是否在按钮上显示图标'
      }
    ],
    // 表单项规则
    validate: []
  }
} 