<template>
  <div class="dept-field-container">
    <!-- 文本显示模式 -->
    <div v-if="displayType === 'text'" class="dept-text-display">
      <el-descriptions :column="1" border>
        <el-descriptions-item :label="fieldLabel">
          {{ deptName || '加载中...' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    
    <!-- 下拉选择模式 -->
    <div v-else-if="displayType === 'select'" class="dept-select-display">
      <el-form-item :label="fieldLabel">
        <el-select
          v-model="selectedDeptValue"
          class="dept-select"
          :disabled="disabled"
          :loading="loading"
          @change="handleDeptChange"
        >
          <el-option
            v-for="item in deptOptions"
            :key="item.id"
            :label="item.name"
            :value="valueType === 'id' ? item.id : (valueType === 'name' ? item.name : JSON.stringify(item))"
          />
        </el-select>
      </el-form-item>
    </div>
    
    <!-- 隐藏字段模式 - 实际上什么都不显示，但会在表单中提交值 -->
    <div v-else-if="displayType === 'hidden'" style="display: none;"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getUserProfile } from '@/api/system/user/profile'

defineOptions({ name: 'DeptField' })

type DisplayType = 'text' | 'hidden' | 'select'
type ValueType = 'id' | 'name' | 'both'

interface DeptInfo {
  id: number
  name: string
}

const props = defineProps({
  // 绑定值（表单数据对象的字段名）
  modelValue: {
    type: [String, Number, Object],
    default: ''
  },
  // 字段名称
  field: {
    type: String,
    default: ''
  },
  // 显示类型
  displayType: {
    type: String as () => DisplayType,
    default: 'text'
  },
  // 字段标签
  fieldLabel: {
    type: String,
    default: '部门'
  },
  // 值类型
  valueType: {
    type: String as () => ValueType,
    default: 'id'
  },
  // 是否禁用（用于select类型）
  disabled: {
    type: Boolean,
    default: false
  },
  // 兼容formCreate的注入属性
  formCreateInject: {
    type: Object,
    default: () => ({})
  }
})

// 支持v-model
const emit = defineEmits(['update:modelValue', 'change'])

// 状态变量
const loading = ref(true)
const deptInfo = ref<DeptInfo | null>(null)
const deptOptions = ref<DeptInfo[]>([])
const selectedDeptValue = ref<string | number | null>(null)

// 计算属性：部门名称
const deptName = computed(() => deptInfo.value?.name || '')

// 计算属性：部门ID
const deptId = computed(() => deptInfo.value?.id || null)

// 处理部门选择变更
const handleDeptChange = (value: string | number) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// 根据值类型格式化部门值
const formatDeptValue = (dept: DeptInfo | null): string | number | object | null => {
  if (!dept) return null
  
  switch (props.valueType) {
    case 'id':
      return dept.id
    case 'name':
      return dept.name
    case 'both':
      return { id: dept.id, name: dept.name }
    default:
      return dept.id
  }
}

// 加载当前用户的部门信息
const loadUserDeptInfo = async () => {
  loading.value = true
  try {
    const userProfile = await getUserProfile()
    
    if (userProfile && userProfile.dept) {
      deptInfo.value = {
        id: userProfile.dept.id,
        name: userProfile.dept.name
      }
      
      // 将部门添加到下拉选项中
      deptOptions.value = [deptInfo.value]
      
      // 设置选中值
      const formattedValue = formatDeptValue(deptInfo.value)
      selectedDeptValue.value = props.valueType === 'both' 
        ? JSON.stringify(formattedValue) 
        : formattedValue as string | number
      
      // 更新表单值
      emit('update:modelValue', formattedValue)
      emit('change', formattedValue)
      
      console.log('已加载用户部门信息:', deptInfo.value)
    } else {
      console.warn('未能获取到用户部门信息')
      ElMessage.warning('未能获取到用户部门信息')
    }
  } catch (error) {
    console.error('加载用户部门信息失败:', error)
    ElMessage.error('加载用户部门信息失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadUserDeptInfo()
})

// 监听值类型变化，更新表单值
watch(() => props.valueType, () => {
  if (deptInfo.value) {
    const formattedValue = formatDeptValue(deptInfo.value)
    selectedDeptValue.value = props.valueType === 'both' 
      ? JSON.stringify(formattedValue) 
      : formattedValue as string | number
    
    emit('update:modelValue', formattedValue)
    emit('change', formattedValue)
  }
})
</script>

<style scoped>
.dept-field-container {
  width: 100%;
}

.dept-text-display {
  width: 100%;
}

.dept-select-display {
  width: 100%;
}

.dept-select {
  width: 100%;
}
</style> 