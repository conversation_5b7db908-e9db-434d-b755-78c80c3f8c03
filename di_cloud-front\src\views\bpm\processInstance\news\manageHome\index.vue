<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <!-- <el-form-item label="流程状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择流程状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="发布日期" prop="publishDate">
        <el-date-picker
          v-model="queryParams.publishDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="新闻标题" prop="title">
        <el-input
          v-model="queryParams.queryVariables.form_title"
          placeholder="请输入新闻标题"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="新闻类别" prop="category">
        <el-select
          v-model="queryParams.queryVariables.category"
          placeholder="请选择新闻类别"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_NEWS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
<!--        <el-button
          type="primary"
          plain
          v-hasPermi="['bpm:process-instance:create']"
          @click="handleCreate(undefined)"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 发起流程
        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="新闻标题" align="center" prop="title" min-width="200px" fixed="left" />
      <el-table-column label="新闻类别" align="center" prop="categoryLabel" min-width="60px" fixed="left" />
      <el-table-column label="发布日期" align="center" prop="publishDate" min-width="100px" fixed="left">
        <template #default="scope">
          {{ formatDate(scope.row.publishDate) }}
        </template>
      </el-table-column>
      <el-table-column label="是否上架" align="center" prop="displayed" min-width="60px" fixed="left">
        <template #default="scope">
          <!-- <el-switch
            v-model="scope.row.displayed" 
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value= "1"
            :inactive-value= "0"
          /> -->
          {{ scope.row.displayed === 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="是否轮播" align="center" prop="carousel" min-width="60px" fixed="left">
        <template #default="scope">
          <!-- <el-switch
            v-model="scope.row.carousel" 
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value= "1"
            :inactive-value= "0"
          /> -->
          {{ scope.row.carousel === 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" min-width="60px" fixed="left">
        <template #default="scope">
          <!-- <el-input-number v-model="scope.row.sort" :min="1" :max="10"/> -->
        {{ scope.row.sort }}          
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row.id)"
          >
            新闻详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleInstance(scope.row.processInstanceId)"
          >
            流程详情
          </el-button>
          <!-- <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:dept:delete']"
          >
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <Dialog v-model="dialogVisible" title="首页管理">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      label-width="110px"
    >
    <el-form-item label="是否上架" prop="displayed">
          <el-switch
            v-model="formData.displayed" 
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value= "1"
            :inactive-value= "0"
          />
      </el-form-item>
      <el-form-item label="是否轮播" prop="carousel">
          <el-switch
            v-model="formData.carousel" 
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value= "1"
            :inactive-value= "0"
          />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" :max="10"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="newsQueryParams.pageNo"
      v-model:limit="newsQueryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, formatPast2 } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { CategoryApi, categoryRoute } from '@/api/bpm/category'
import { ProcessInstanceVO } from '@/api/bpm/processInstance'
import * as DefinitionApi from '@/api/bpm/definition'
import { cloneDeep } from 'lodash-es'
import { ref } from 'vue'
defineOptions({ name: 'BpmProcessInstanceMy' })

const router = useRouter() // 路由
const route = useRoute() // 路由
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const category=ref(null)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  processDefinitionKey: undefined,
  category: undefined,
  status: undefined,
  createTime: [],
  publishDate: [], // 新增发布日期查询字段
  queryVariables:{
    form_title:'',//流程标题
    category:""//新闻分类
  }
})
const newsQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: '',      // 新闻标题
  category: null, // 新闻类别
  startPublishDate: '', // 发布日期开始
  endPublishDate: ''    // 发布日期结束
})
const xf = ref("oa_xf")// 类别为信访
const xs = ref("oa_xs")//类别为线索
const queryFormRef = ref() // 搜索的表单
const categoryList = ref([]) // 流程分类列表
const news_type = ref(null) // 新闻类别
const formLoading = ref(false) // 表单的加载中
const dialogVisible = ref(false) // 表单的显示
const formData = ref({displayed:0,carousel:0,sort:0,id:1}) // 表单的数据
/** 查询列表 */
// const getList = async () => {
//   loading.value = true
//   try {
//     queryParams.category = category.value
//     // 将queryVariable中的对象转化为json字符串便于后端处理
//     const json = JSON.stringify(queryParams.queryVariables)
//     const query = cloneDeep(queryParams)
//     query.queryVariables = json
//     const data = await ProcessInstanceApi.getProcessInstanceMyPage(query)
//     list.value = data.list
//     // console.log("0225list",list.value)
//     total.value = data.total
//     // console.info("list",JSON.stringify(list.value))
//   } finally {
//     loading.value = false
//   }
// }
//获取新闻列表
const getList = async () => {
  loading.value = true
  try{
    // 添加调试日志，查看搜索参数
    console.log('查询参数：', newsQueryParams)
    const newsData = await ProcessInstanceApi.getNewsList(newsQueryParams)
    list.value = newsData.list
    total.value = newsData.total
    // 生成新闻分类标签
    if (news_type.value && list.value) {
      list.value.forEach((item) => {
        news_type.value.forEach((v) => {
          if(item.category == v.value){
            item.categoryLabel = v.label
          }
        })
      })
    }
  } finally{
    loading.value = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = async(type: string, id?: number) => {
  // console.log("type",type,"id",id)
  try{
    // formData.value = await ProcessInstanceApi.getNewsById(id)
    dialogVisible.value = true
    formLoading.value = true
    const data = await ProcessInstanceApi.getNewsById({id:id})
    // console.log("Data",data)
    formData.value.carousel=data.carousel
    formData.value.displayed=data.displayed
    formData.value.sort=data.sort
    formData.value.id=data.id
  }finally{
    formLoading.value = false
  }
}
// 提交表单
const submitForm = async () => {
  formLoading.value = true
  try {
    const params = formData.value
    params.id = formData.value.id
    console.log("response",await ProcessInstanceApi.updateNewsStatusById(params))

  } finally {
    formLoading.value = false
    dialogVisible.value = false
    getList()

  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  // 重置查询页码
  newsQueryParams.pageNo = 1
  
  // 将查询条件从表单复制到查询参数中
  newsQueryParams.title = queryParams.queryVariables.form_title || ''
  newsQueryParams.category = queryParams.queryVariables.category || null
  
  // 处理发布日期区间
  if (queryParams.publishDate && queryParams.publishDate.length === 2) {
    newsQueryParams.startPublishDate = queryParams.publishDate[0]
    newsQueryParams.endPublishDate = queryParams.publishDate[1]
  } else {
    newsQueryParams.startPublishDate = ''
    newsQueryParams.endPublishDate = ''
  }
  
  // 执行查询
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  // 清空查询参数
  newsQueryParams.title = ''
  newsQueryParams.category = null
  newsQueryParams.startPublishDate = ''
  newsQueryParams.endPublishDate = ''
  queryParams.queryVariables.category = ''
  queryParams.queryVariables.form_title = ''
  queryParams.publishDate = []

  // 执行查询
  handleQuery()
}

/** 查看详情 */
const handleDetail = (id) => {
  router.push({
    name: 'newsPage',
    query: {
      id: id
    }
  })
}

const handleInstance = (processInstanceId)=> {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: processInstanceId
    }
  })
}

const getCategory=async ()=>{
  category.value=await categoryRoute.getCategoryByRouteParam(route)
}

//获取新闻类别
const getNewsType = async () => {
   news_type.value = await getIntDictOptions(DICT_TYPE.SYSTEM_NEWS_TYPE)
}
/** 激活时 **/
// onActivated(() => {
//   getList(category.value)
// })

/** 初始化 **/
onMounted(async () => {
  await getNewsType()
  // 初次获取列表
  getList()
})

// 格式化日期显示
const formatDate = (date) => {
  if (!date) return '';
  try {
    return new Date(date).toLocaleDateString('zh-CN');
  } catch (e) {
    return date;
  }
}
</script>
