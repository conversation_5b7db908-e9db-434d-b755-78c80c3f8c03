﻿<template>


  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入任务名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="流程" prop="processInstance.name" min-width="180"/>
      <el-table-column align="center" label="流程标题" min-width="200">
        <template #default="scope">
          {{ scope.row.processInstanceExt?.processInstanceTitle || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="发起人"
        prop="processInstance.startUser.nickname"
        width="100"
      />
      <el-table-column
        label="办理时限"
        align="center"
        width="180"
        v-if="category === 'oa_xs'"
      >
        <template #default="scope">
          {{ scope.row.processInstance?.formVariables?.blsx || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="发起时间"
        prop="createTime"
        width="180"
      />
      <el-table-column align="center" label="当前任务" prop="name" width="180" />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="任务开始时间"
        prop="createTime"
        width="180"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="任务结束时间"
        prop="endTime"
        width="180"
      />

      <!-- <el-table-column align="center" label="审批状态" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column> -->
      <!-- <el-table-column align="center" label="审批建议" prop="reason" min-width="180" /> -->
      <el-table-column align="center" label="耗时" prop="durationInMillis" width="160">
        <template #default="scope">
          {{ formatPast2(scope.row.durationInMillis) }}
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="流程编号" prop="id" :show-overflow-tooltip="true" /> -->
      <!-- <el-table-column align="center" label="任务编号" prop="id" :show-overflow-tooltip="true" /> -->
      <el-table-column align="center" label="操作" fixed="right" width="160">
        <template #default="scope">
          <div class="button-group">
            <el-button link type="primary" @click="handleAudit(scope.row)">历史</el-button>
            <!-- 问题线索申请延期流程的查看父流程按钮 -->
            <el-button
              v-if="scope.row.processInstance?.processDefinitionId?.split(':')[0] === 'wtxssqyq_bpm'"
              link
              type="primary"
              @click="handleViewParentDetail(scope.row)"
            >
              查看上级流程
            </el-button>
            <!-- 查看历史申请按钮，只在有办理时限的任务中显示 -->
            <el-button 
              v-if="scope.row.processInstance?.formVariables?.blsx" 
              link 
              type="primary" 
              @click="handleViewHistory(scope.row)"
            >
              延期历史记录
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter, formatPast2 } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { categoryRoute } from "@/api/bpm/category";
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'BpmTodoTask' })

const { push } = useRouter() // 路由
const route = useRoute()
const message = useMessage() // 消息弹窗
const userStore = useUserStore() // 用户 store
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const category= ref('')// 流程分类
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  createTime: [],
  processCategoryCode:''
})
const queryFormRef = ref() // 搜索的表单

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.processCategoryCode = category
    console.log("查询任务列表中...", queryParams)
    const data = await TaskApi.getTaskDonePage(queryParams)
    list.value = data.list
    total.value = data.total
    
    // 处理流程数据
    if (list.value && list.value.length > 0) {
      list.value.forEach((task, index) => {
        // 处理流程标题 - 确保processInstanceExt对象存在
        if (!task.processInstanceExt) {
          task.processInstanceExt = {};
        }
        
        // 从旧的数据结构中获取流程标题（如果存在）
        if (task.processInstance?.processInstanceTitle && !task.processInstanceExt.processInstanceTitle) {
          task.processInstanceExt.processInstanceTitle = task.processInstance.processInstanceTitle;
          console.log(`设置任务[${task.id}]的流程标题: ${task.processInstanceExt.processInstanceTitle}`);
        }
        
        // 处理办理时限数据，使用processInstanceExt.processInstanceLimitDate字段
        if (task.processInstanceExt && 
            task.processInstanceExt.processInstanceLimitDate) {
          
          // 获取日期字符串，格式为YYYY-MM-DD
          const formattedDate = task.processInstanceExt.processInstanceLimitDate;
          
          // 将办理时限添加到formVariables中
          if (!task.processInstance.formVariables) {
            task.processInstance.formVariables = {};
          }
          task.processInstance.formVariables.blsx = formattedDate;
          console.log(`设置任务[${task.id}]的流程[${task.processInstance.id}]的办理时限: ${formattedDate}`);
        } else {
          console.log(`任务[${task.id}]的流程没有processInstanceExt.processInstanceLimitDate数据`);
        }
        
        // 添加流程状态
        if (task.processInstance.status === undefined) {
          task.processInstance.status = task.processInstance.result;
        }
        
        // 如果是申请延期流程，获取流程的流程定义ID
        if (task.processInstance.processDefinition?.key) {
          task.processInstance.processDefinitionId = 
            `${task.processInstance.processDefinition.key}:${task.processInstance.processDefinition.version}:${task.processInstance.processDefinition.id}`;
        }
      });
      console.log("所有任务数据处理完成");
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList(category.value)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id,
      taskId: row.id
    }
  })
}

const getCategory = async () => {
  category.value = await categoryRoute.getCategoryByRouteParam(route)
}

/** 查看父流程详情操作 */
const handleViewParentDetail = async (row) => {
  try {
    if (!row.processInstance || !row.processInstance.id) {
      message.warning('无法获取流程信息')
      return
    }
    
    // 先调用API获取详细数据
    console.log("查看父流程详情，获取实例数据中...", row.processInstance.id)
    const processDetail = await ProcessInstanceApi.getProcessInstance(row.processInstance.id)
    
    // 从返回的数据中提取lcid字段
    const parentProcessId = processDetail.formVariables?.lcid
    if (!parentProcessId) {
      message.warning('无法获取父流程信息')
      return
    }
    
    console.log("获取到父流程ID:", parentProcessId)
    
    // 跳转到流程实例详情页面
    push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: parentProcessId,
        hideApproval: 'true'
      }
    })
  } catch (error) {
    console.error("获取流程详情失败:", error)
    message.error('获取流程详情失败')
  }
}

/** 查看历史申请记录操作 */
const handleViewHistory = (row) => {
  console.log('查看历史申请记录', row)
  if (!row.processInstance || !row.processInstance.id) {
    message.warning('无法获取流程信息')
    return
  }
  
  console.log('查看历史申请记录', row.processInstance.id)
  
  // 跳转到历史申请列表页面
  push({
    name: 'BpmProcessInstanceHistory',
    query: {
      processInstanceId: row.processInstance.id
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getCategory().then(() => {
    if(!category.value){
      message.error("缺少访问参数！")
      return
    }
    getList(category.value)
  })
})
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 5px;
}
</style>
