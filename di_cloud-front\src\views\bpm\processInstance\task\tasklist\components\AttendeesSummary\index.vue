<template>
  <ContentWrap>
    <div class="attendees-summary-page">
      <!-- 页面标题区域 -->
      <div class="page-header mb-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-button @click="goBack" plain>
              <Icon icon="ep:arrow-left" class="mr-1" /> 返回
            </el-button>
          </div>
          <div class="flex items-center">
            <el-button type="success" class="mr-2" @click="exportToExcel">
              <Icon icon="ep:download" class="mr-1" /> 导出Excel
            </el-button>
            <el-upload
              class="attendees-upload"
              action="#"
              :show-file-list="false"
              :before-upload="beforeImportExcel"
              accept=".xlsx,.xls"
            >
              <el-button type="primary">
                <Icon icon="ep:upload-filled" class="mr-1" /> 导入Excel
              </el-button>
            </el-upload>
          </div>
        </div>
      </div>

      <!-- 会议基本信息 -->
      <div class="meeting-info mb-6">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <h3 class="text-xl font-bold">{{ taskInfo.taskName }}</h3>
              <el-tag type="warning">会议通知</el-tag>
            </div>
          </template>
          <div class="info-content">
            <div class="info-row">
              <span class="info-label">接收单位：</span>
              <span class="info-value">
                <template v-if="taskInfo.isAllUnits">
                  <el-tag type="success" effect="plain">所有接收单位汇总</el-tag>
                </template>
                <template v-else>
                  {{ taskInfo.receiverName || '未指定' }}
                </template>
              </span>
            </div>
            <div v-if="taskInfo.isAllUnits" class="info-row mt-2">
              <span class="info-label">说明：</span>
              <span class="info-value text-gray-500">
                此页面展示了所有接收单位的参会人员信息汇总
              </span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 参会人员信息统计 -->
      <div class="statistics mb-4">
        <el-alert
          type="info"
          :closable="false"
          show-icon
        >
          共有 <strong>{{ totalAttendees }}</strong> 人参会，来自 <strong>{{ departmentCount }}</strong> 个单位
        </el-alert>
      </div>

      <!-- 参会人员信息表格 -->
      <el-card class="box-card mb-6">
        <template #header>
          <div class="card-header">
            <span>参会人员信息汇总表</span>
          </div>
        </template>
        <div class="attendees-table">
          <el-table
            v-loading="loading"
            :data="allAttendees"
            border
            stripe
            size="large"
            max-height="600"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column label="接收单位" prop="receiverUnit" min-width="150" align="center" show-overflow-tooltip />
            <el-table-column label="单位" prop="unit" min-width="150" align="center" show-overflow-tooltip />
            <el-table-column label="姓名" prop="name" min-width="100" align="center" />
            <el-table-column label="职务" prop="position" min-width="120" align="center" show-overflow-tooltip />
            <el-table-column label="手机号" prop="phone" min-width="130" align="center" />
          </el-table>
        </div>
      </el-card>

      <!-- 按接收单位分组展示 -->
      <div class="department-section">
        <el-collapse v-model="activeCollapse">
          <el-collapse-item 
            v-for="(receiverData, receiverName) in attendeesByReceiver" 
            :key="receiverName" 
            :title="`${receiverName} (${receiverData.length}人)`" 
            :name="receiverName"
          >
            <el-table
              :data="receiverData"
              border
              stripe
              size="small"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
            >
              <el-table-column type="index" label="序号" width="50" align="center" />
              <el-table-column label="单位" prop="unit" min-width="150" align="center" show-overflow-tooltip />
              <el-table-column label="姓名" prop="name" min-width="80" align="center" />
              <el-table-column label="职务" prop="position" min-width="100" align="center" show-overflow-tooltip />
              <el-table-column label="手机号" prop="phone" min-width="120" align="center" />
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { getTaskEngineListByTaskEngineId } from '@/api/bpm/processInstance'
import * as TaskApi from '@/api/bpm/task'
import * as XLSX from 'xlsx'

defineOptions({ name: 'TaskAttendeesSummary' })

// 路由和消息相关
const route = useRoute()
const router = useRouter()
const message = useMessage()

// 加载状态
const loading = ref(true)

// 页面折叠面板激活项
const activeCollapse = ref([])

// 任务基本信息
const taskInfo = reactive({
  id: route.params.id as string,
  processInstanceId: route.params.processInstanceId as string,
  taskName: route.query.taskName as string,
  taskType: route.query.taskType as string,
  receiverName: route.query.receiverName as string,
  isAllUnits: route.query.isAllUnits === 'true'  // 是否为所有接收单位的汇总
})

// 所有参会人员数据
const allAttendees = ref<Array<{
  receiverUnit: string; // 接收单位
  unit: string;
  name: string;
  position: string;
  phone: string;
}>>([])

// 按接收单位分组的参会人员数据
const attendeesByReceiver = computed(() => {
  const result: Record<string, Array<{
    receiverUnit: string;
    unit: string;
    name: string;
    position: string;
    phone: string;
  }>> = {}
  
  allAttendees.value.forEach(attendee => {
    if (!attendee.receiverUnit) return
    
    if (!result[attendee.receiverUnit]) {
      result[attendee.receiverUnit] = []
    }
    
    result[attendee.receiverUnit].push(attendee)
  })
  return result
})

// 统计信息
const totalAttendees = computed(() => allAttendees.value.length)
const departmentCount = computed(() => Object.keys(attendeesByReceiver.value).length)

// 获取参会人员数据
const fetchAttendeesData = async () => {
  loading.value = true
  try {
    // 根据是否为所有单位汇总来确定获取数据的方式
    if (taskInfo.isAllUnits) {
      // 获取所有接收单位的数据
      const response = await getTaskEngineListByTaskEngineId({
        taskEngineId: taskInfo.id
      })
      
      if (!response || response.length === 0) {
        message.warning('未获取到任务数据')
        allAttendees.value = []
        loading.value = false
        return
      }
      
      // 获取所有单位的流程实例ID数组
      const processInstanceIds = response.map(item => item.processInstanceId).filter(Boolean)
      
      if (!processInstanceIds.length) {
        message.warning('未找到有效的流程实例')
        allAttendees.value = []
        loading.value = false
        return
      }
      
      // 合并所有参会人员数据
      const attendeesData: Array<{
        receiverUnit: string;
        unit: string;
        name: string;
        position: string;
        phone: string;
      }> = []
      
      // 对每个流程实例ID获取任务列表
      for (const processInstanceId of processInstanceIds) {
        const taskListResponse = await TaskApi.getTaskListByProcessInstanceId(processInstanceId)
        
        if (!taskListResponse?.length) continue
        
        // 查找所有二级公司办理任务，并且仅过滤审核通过的任务
        // 打印任务状态，方便调试
        taskListResponse.forEach(task => {
          if (task.formId === 75 || task.formId === 82) {
            console.log(`任务[${task.id}]状态详情:`, 
              `formId: ${task.formId}, `,
              `status: ${task.status}, `,
              `completed: ${task.completed}, `,
              `endTime: ${task.endTime ? '已完成' : '未完成'}, `,
              `processInstanceState: ${task.processInstanceState}`)
          }
        });
        
        // 查找所有二级公司办理任务，并且仅过滤审核通过的任务
        const secondaryCompanyTasks = taskListResponse.filter(task => {
          // 任务必须是表单ID为75或82的
          const isTargetForm = (task.formId === 75 || task.formId === 82);
          
          // 审核通过条件：task.status === 2 (已审核通过) 或 
          // task.endTime 存在表示任务已完成 或
          // task.completed === true 表示任务已完成
          const isApproved = task.status === 2 || 
                            (task.endTime !== null && task.endTime !== undefined) || 
                            task.completed === true;
          
          // 返回结果：必须是目标表单且已审核通过
          return isTargetForm && isApproved;
        });
        
        if (!secondaryCompanyTasks.length) continue
        
        // 处理每个任务的参会人员数据
        secondaryCompanyTasks.forEach(task => {
          let taskAttendees: any[] = []
          
          // 提取表单中的参会人员数据
          if (task.formId === 75) {
            taskAttendees = task.formVariables?.attendees?.attendees || []
          } else if (task.formId === 82) {
            taskAttendees = task.formVariables?.attendees || []
          }
          
          // 确保单位信息存在
          if (taskAttendees.length > 0) {
            // 找到接收单位名称
            const receiverUnit = response.find(item => item.processInstanceId === processInstanceId)?.deptName || '未知单位'
            
            taskAttendees = taskAttendees.map(attendee => ({
              receiverUnit: receiverUnit, // 接收单位
              unit: attendee.unit || receiverUnit, // 参会人员单位
              name: attendee.name || '',
              position: attendee.position || '',
              phone: attendee.phone || ''
            }))
            
            attendeesData.push(...taskAttendees)
          }
        })
      }
      
      allAttendees.value = attendeesData
    } else {
      // 单个接收单位的数据处理（原有逻辑）
      const taskListResponse = await TaskApi.getTaskListByProcessInstanceId(taskInfo.processInstanceId)
      
      if (!taskListResponse?.length) {
        message.warning('未获取到任务数据')
        allAttendees.value = []
        loading.value = false
        return
      }
      
      // 打印任务状态，方便调试
      taskListResponse.forEach(task => {
        if (task.formId === 75 || task.formId === 82) {
          console.log(`任务[${task.id}]状态详情:`, 
            `formId: ${task.formId}, `,
            `status: ${task.status}, `,
            `completed: ${task.completed}, `,
            `endTime: ${task.endTime ? '已完成' : '未完成'}, `,
            `processInstanceState: ${task.processInstanceState}`)
        }
      });
      
      // 查找所有二级公司办理任务，并且仅过滤审核通过的任务
      const secondaryCompanyTasks = taskListResponse.filter(task => {
        // 任务必须是表单ID为75或82的
        const isTargetForm = (task.formId === 75 || task.formId === 82);
        
        // 审核通过条件：task.status === 2 (已审核通过) 或 
        // task.endTime 存在表示任务已完成 或
        // task.completed === true 表示任务已完成
        const isApproved = task.status === 2 || 
                          (task.endTime !== null && task.endTime !== undefined) || 
                          task.completed === true;
        
        // 返回结果：必须是目标表单且已审核通过
        return isTargetForm && isApproved;
      });
      
      if (!secondaryCompanyTasks.length) {
        message.warning('未找到参会人员数据')
        allAttendees.value = []
        loading.value = false
        return
      }
      
      // 合并所有参会人员数据
      const attendeesData: Array<{
        receiverUnit: string;
        unit: string;
        name: string;
        position: string;
        phone: string;
      }> = []
      
      secondaryCompanyTasks.forEach(task => {
        let taskAttendees: any[] = []
        
        // 提取表单中的参会人员数据
        if (task.formId === 75) {
          taskAttendees = task.formVariables?.attendees?.attendees || []
        } else if (task.formId === 82) {
          taskAttendees = task.formVariables?.attendees || []
        }
        
        // 确保单位信息存在
        if (taskAttendees.length > 0) {
          // 接收单位
          const receiverUnit = task.assigneeDept?.name || '未知单位'
          
          taskAttendees = taskAttendees.map(attendee => ({
            receiverUnit: receiverUnit, // 接收单位
            unit: attendee.unit || receiverUnit, // 参会人员单位
            name: attendee.name || '',
            position: attendee.position || '',
            phone: attendee.phone || ''
          }))
          
          attendeesData.push(...taskAttendees)
        }
      })
      
      allAttendees.value = attendeesData
    }
  } catch (error) {
    console.error('获取参会人员数据失败:', error)
    message.error('获取参会人员数据失败')
    allAttendees.value = []
  } finally {
    loading.value = false
  }
}

// 导出Excel
const exportToExcel = () => {
  if (!allAttendees.value.length) {
    message.warning('没有可导出的参会人员数据')
    return
  }
  
  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    
    // 创建工作表
    const ws = XLSX.utils.json_to_sheet(allAttendees.value.map(item => ({
      接收单位: item.receiverUnit || '',
      单位: item.unit || '',
      姓名: item.name || '',
      职务: item.position || '',
      手机号: item.phone || ''
    })))
    
    // 设置列宽
    const columnWidths = [
      { wch: 20 }, // 接收单位
      { wch: 15 }, // 单位
      { wch: 10 }, // 姓名
      { wch: 15 }, // 职务
      { wch: 15 }  // 手机号
    ]
    ws['!cols'] = columnWidths
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '参会人员信息汇总')
    
    // 导出文件
    XLSX.writeFile(wb, `${taskInfo.taskName}-参会人员信息汇总.xlsx`)
    
    message.success('导出成功')
  } catch (error) {
    console.error('导出Excel失败:', error)
    message.error('导出Excel失败')
  }
}

// 导入Excel前的处理
const beforeImportExcel = (file: File) => {
  if (!file) {
    message.error('请选择Excel文件')
    return false
  }
  
  // 文件类型检查
  const isExcel = /\.(xlsx|xls)$/.test(file.name.toLowerCase())
  if (!isExcel) {
    message.error('只能上传Excel文件(.xlsx, .xls)')
    return false
  }
  
  // 文件大小检查（限制为10MB）
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB')
    return false
  }
  
  // 读取Excel文件
  const reader = new FileReader()
  reader.onload = (e: ProgressEvent<FileReader>) => {
    try {
      const data = e.target?.result
      if (!data) {
        message.error('读取文件失败')
        return
      }
      
      const workbook = XLSX.read(data, { type: 'array' })
      
      // 获取第一个工作表
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      
      // 将工作表转换为JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet)
      
      if (!jsonData.length) {
        message.error('Excel文件中没有数据')
        return
      }
      
      // 处理导入数据
      processImportedData(jsonData)
    } catch (error) {
      console.error('解析Excel失败:', error)
      message.error('解析Excel失败，请检查文件格式')
    }
  }
  
  reader.readAsArrayBuffer(file)
  
  // 阻止默认上传行为
  return false
}

// 处理导入的数据
const processImportedData = (data) => {
  try {
    // 转换字段名称
    const convertedData = data.map(row => {
      // 尝试匹配中文列名到英文字段
      return {
        receiverUnit: row['接收单位'] || row['单位'] || row['单位名称'] || '',
        unit: row['单位'] || row['部门'] || row['单位名称'] || '',
        name: row['姓名'] || row['名字'] || '',
        position: row['职务'] || row['职位'] || row['岗位'] || '',
        phone: row['手机号'] || row['电话'] || row['联系电话'] || ''
      }
    })
    
    // 验证数据
    if (!validateImportData(convertedData)) {
      return
    }
    
    // 替换或合并数据
    allAttendees.value = convertedData
    
    message.success(`成功导入 ${convertedData.length} 条参会人员数据`)
  } catch (error) {
    console.error('处理导入数据失败:', error)
    message.error('处理导入数据失败')
  }
}

// 验证导入数据
const validateImportData = (data) => {
  if (!data.length) {
    message.error('导入数据为空')
    return false
  }
  
  // 检查必填字段
  const invalidRows = data.filter(row => !row.name || !row.unit)
  if (invalidRows.length > 0) {
    message.error(`有 ${invalidRows.length} 行数据缺少姓名或单位信息，请检查后重新导入`)
    return false
  }
  
  return true
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 初始化
onMounted(() => {
  fetchAttendeesData()
})
</script>

<style scoped>
.attendees-summary-page {
  font-family: PingFang SC, Microsoft YaHei UI, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  padding: 12px 0;
}

.meeting-info .el-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-content {
  padding: 10px;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
  line-height: 1.6;
}

.info-label {
  font-weight: 500;
  width: 100px;
  color: #606266;
}

.info-value {
  flex: 1;
}

.attendees-upload {
  display: inline-block;
}

.department-section {
  margin-top: 20px;
}

.statistics {
  margin: 20px 0;
}

.statistics :deep(.el-alert__content) {
  display: flex;
  align-items: center;
}

.statistics :deep(strong) {
  font-weight: bold;
  color: #409EFF;
  padding: 0 4px;
}
</style> 
