<template>
  <div class="report-methods-tech-container">
    <!-- 信访件统计 -->
    <div v-if="showXinFangSection" class="section-title">
      信访件统计
      <el-button type="primary" size="small" class="view-dashboard-btn" @click="viewSecondaryDashboard('oa_xf')">
        <span>查看详细数据</span>
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
    <el-row v-if="showXinFangSection" :gutter="20" style="margin-top: 10px;">
      <!-- 信访件数量统计 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>信访件数量统计</span>
            <el-tooltip content="重复件与非重复件数量对比" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="petitionCountChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>      
      <!-- 信访举报方式数量分布 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>信访举报方式数量分布</span>
            <el-tooltip content="各信访举报方式的数量统计" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="barChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
    </el-row>

    <el-row v-if="showXinFangSection" :gutter="20" style="margin-top: 20px;">
      <!-- 信访结果分布 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>信访结果分布</span>
            <el-tooltip content="信访结果的处理方式分布" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="resultChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
      
      <!-- 信访举报类别雷达图 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>信访举报类别分析</span>
            <el-tooltip content="不同类别信访举报的数量分布" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="categoryRadarChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 线索件统计 -->
    <div v-if="showXianSuoSection" class="section-title" style="margin-top: 30px;">
      线索件统计
      <el-button type="primary" size="small" class="view-dashboard-btn" @click="viewSecondaryDashboard('oa_xs')">
        <span>查看详细数据</span>
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
    <el-row v-if="showXianSuoSection" :gutter="20" style="margin-top: 10px;">
      <!-- 线索件数量统计 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>线索件数量统计</span>
            <el-tooltip content="线索件总数、办结数和在办数" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="clueCountChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
      <!-- 线索处置结果分析 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>线索处置结果分析</span>
            <el-tooltip content="线索处置各类结果的分布情况" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="disposalResultChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
    </el-row>

    <el-row v-if="showXianSuoSection" :gutter="20" style="margin-top: 20px;">
      <!-- 在办线索件数量 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>在办线索数量统计</span>
            <el-tooltip content="超时未办结与未超时线索数量" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="clueProgressChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
      
      <!-- 已办线索件数量 - 与在办线索图表在同一行 -->
      <el-col :xs="24" :sm="24" :md="12" v-if="showClueCompletedSection">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>已办线索数量统计</span>
            <el-tooltip content="已办线索中按期办结与超时办结数量对比" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="clueCompletedChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 线索处置方式分布 - 独占一行 -->
    <el-row v-if="showXianSuoSection" :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :sm="24" :md="24">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>线索处置方式分布</span>
            <el-tooltip content="不同线索处置方式的数量分布" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="disposalChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 线索办理主体分布 -->
    <el-row v-if="showXianSuoSection && showClueDeptSection" :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :sm="24" :md="24" v-hasPermi="['monitor:organizer']">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>线索办理主体分布</span>
            <el-tooltip content="各二级单位办理的线索数量" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="clueDeptChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 立案件统计 -->
    <div v-if="showLiAnSection" class="section-title" style="margin-top: 30px;">
      立案件统计
      <el-button type="primary" size="small" class="view-dashboard-btn" @click="viewSecondaryDashboard('oa_register')">
        <span>查看详细数据</span>
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>
    <el-row v-if="showLiAnSection" :gutter="20" style="margin-top: 10px;">
      <!-- 案件数量统计 -->
      <el-col :xs="24" :sm="24" :md="24">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>立案件数量统计</span>
            <el-tooltip content="立案总数、办结数和在办数" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="caseCountChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 立案件办理主体分布 -->
    <el-row v-if="showLiAnSection && showCaseDeptSection" :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :sm="24" :md="24" v-hasPermi="['monitor:organizer']">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>立案件办理主体分布</span>
            <el-tooltip content="各二级单位办理的案件数量" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="caseDeptChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 立案件政务处分结果和党纪处分结果 -->
    <el-row v-if="showLiAnSection" :gutter="20" style="margin-top: 20px;">
      <!-- 立案件党纪处分结果 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>立案件党纪处分结果</span>
            <el-tooltip content="立案后不同党纪处分结果的数量分布" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="partyPunishmentChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>

      <!-- 立案件政务处分结果 -->
      <el-col :xs="24" :sm="24" :md="12">
        <div class="chart-card tech-card">
          <div class="chart-header tech-header">
            <span>立案件政务处分结果</span>
            <el-tooltip content="立案后不同政务处分结果的数量分布" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="chart-content">
            <div ref="punishmentChartRef" class="chart"></div>
          </div>
          <div class="tech-corner top-left"></div>
          <div class="tech-corner top-right"></div>
          <div class="tech-corner bottom-left"></div>
          <div class="tech-corner bottom-right"></div>
        </div>
      </el-col>
      
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, defineProps, computed, nextTick, defineExpose } from 'vue'
import * as echarts from 'echarts'
import { QuestionFilled, ArrowRight } from '@element-plus/icons-vue'
import { getSecondaryDepartments, getBpmLettersStatistics, getBpmClueStatistics, getBpmFilingStatistics } from '../../../../../api/bpm/processInstance'
import { useRouter } from 'vue-router'

// 初始化路由
const router = useRouter()

// 跳转到二级看板
const viewSecondaryDashboard = (categoryType: string) => {
  // 获取当前查询参数
  let startTime = props.startDate || ''
  let endTime = props.endDate || ''
  let deptId = props.deptId || ''
  
  router.push({
    name: 'BpmSecondaryDashboard',
    query: {
      category: categoryType,
      startTime: startTime,
      endTime: endTime,  // 修改为正确的参数名称endTime
      organizingDeptId: deptId
    }
  })
}

// 接收父组件传递的查询参数
const props = defineProps({
  deptId: {
    type: String,
    default: ''
  },
  startDate: {
    type: String,
    default: ''
  },
  endDate: {
    type: String,
    default: ''
  },
  category: {
    type: String,
    default: ''
  }
})

// 各种类别是否显示图表的计算属性
const showXinFangSection = computed(() => {
  const result = props.category === '' || props.category === 'oa_xf' || props.category === 'all' || props.category === 'total'
  console.log(`信访部分显示状态: ${result}，当前category: "${props.category}"`)
  return result
})

const showXianSuoSection = computed(() => {
  const result = props.category === '' || props.category === 'oa_xs' || props.category === 'all' || props.category === 'total'
  console.log(`线索部分显示状态: ${result}，当前category: "${props.category}"`)
  return result
})

const showLiAnSection = computed(() => {
  const result = props.category === '' || props.category === 'oa_register' || props.category === 'all' || props.category === 'total'
  console.log(`立案部分显示状态: ${result}，当前category: "${props.category}"`)
  return result
})

// 添加一个新的计算属性
// 用于控制线索办理主体分布图表的显示与隐藏
const showClueDeptSection = ref(false) // 默认隐藏，直到数据加载
// 添加一个控制立案件办理主体图表显示的计算属性
const showCaseDeptSection = ref(false) // 默认隐藏，直到数据加载
// 添加一个控制已办线索数量图表显示的计算属性
const showClueCompletedSection = ref(false) // 默认隐藏，直到数据加载

// 引用元素
const pie3dChartRef = ref<HTMLElement | null>(null)
const barChartRef = ref<HTMLElement | null>(null)
const categoryRadarChartRef = ref<HTMLElement | null>(null)
const resultChartRef = ref<HTMLElement | null>(null)
const disposalChartRef = ref<HTMLElement | null>(null)
const disposalResultChartRef = ref<HTMLElement | null>(null)
const punishmentChartRef = ref<HTMLElement | null>(null)
const partyPunishmentChartRef = ref<HTMLElement | null>(null)
const petitionCountChartRef = ref<HTMLElement | null>(null)
const clueCountChartRef = ref<HTMLElement | null>(null)
const clueProgressChartRef = ref<HTMLElement | null>(null)
const clueDeptChartRef = ref<HTMLElement | null>(null)
const caseCountChartRef = ref<HTMLElement | null>(null)
const caseDeptChartRef = ref<HTMLElement | null>(null)
const clueCompletedChartRef = ref<HTMLElement | null>(null)

// ECharts实例
let pie3dChart: echarts.ECharts | null = null
let barChart: echarts.ECharts | null = null
let categoryRadarChart: echarts.ECharts | null = null
let resultChart: echarts.ECharts | null = null
let disposalChart: echarts.ECharts | null = null
let disposalResultChart: echarts.ECharts | null = null
let punishmentChart: echarts.ECharts | null = null
let partyPunishmentChart: echarts.ECharts | null = null
let petitionCountChart: echarts.ECharts | null = null
let clueCountChart: echarts.ECharts | null = null
let clueProgressChart: echarts.ECharts | null = null
let clueDeptChart: echarts.ECharts | null = null
let caseCountChart: echarts.ECharts | null = null
let caseDeptChart: echarts.ECharts | null = null
let clueCompletedChart: echarts.ECharts | null = null

// 模拟数据
// 以下数据将作为静态数据使用
const reportMethodsData = [
  { name: '来信', value: 0},
  { name: '来访', value: 0},
  { name: '来电', value: 0},
  { name: '网络举报', value: 0},
  { name: '其他', value: 0}
]

// 信访举报类别数据 - 使用可变数组以支持动态更新
const reportCategoryData = Array.from([
  { name: '检举控告类', value: 0 },
  { name: '批评建议类', value: 0 },
  { name: '申诉类', value: 0},
  { name: '无实质内容', value:0 },
  { name: '业务范围外', value: 0 }
])

// 信访结果分布数据 - 使用可变数组以支持动态更新
const reportResultData = Array.from([
  { name: '进入线索', value: 0 },
  { name: '信访阶段了结', value: 0 }
])

// 信访件数量统计数据
const petitionCountData = ref([
  { name: '重复件', value: 0 },
  { name: '非重复件', value: 0 },
  { name: '暂未分类', value: 0 }
])

// 信访件总数
const petitionTotalCount = ref(0)

// 线索处置方式数据
const disposalMethodData = [
  { name: '暂存待查', value: 0 },
  { name: '予以了结', value: 0},
  { name: '谈话函询', value: 0 },
  { name: '初步核实', value: 0 }
]

// 线索处置结果数据
const disposalResultData = [
  { name: '经过初核失实', value: 0 },
  { name: '适当处理', value: 0 },
  { name: '经过谈话函询被反映人问题未能认定', value: 0 },
  { name: '被反映人已离休或去世', value: 0 },
  { name: '反映问题失实或道听途说主观、臆测难以查证', value: 0 },
  { name: '被反映人已移送或判刑', value: 0 },
  { name: '予以了结/其他', value: 0 },
  { name: '立案审查调查', value: 0 }
]

// 立案件政务处分结果数据
const punishmentData = [
  { name: '警告', value: 0 },
  { name: '记过', value: 0 },
  { name: '记大过', value: 0 },
  { name: '降级', value: 0 },
  { name: '撤职', value: 0 },
  { name: '开除', value: 0 }
]

// 立案件党纪处分结果数据
const partyPunishmentData = [
  { name: '党内警告', value: 0 },
  { name: '党内严重警告', value: 0 },
  { name: '党内严重警告（二年）', value: 0 },
  { name: '撤销党内职务', value: 0 },
  { name: '留党察看', value: 0 },
  { name: '开除党籍', value: 0 }
]

// 在办线索超时情况
const clueProgressData = [
  { name: '超时未办结', value: 0 },
  { name: '按期办理中', value: 0 }
]

// 已办线索按期与超时情况数据
const clueCompletedData = [
  { name: '按期办结', value: 0 },
  { name: '超时办结', value: 0 }
]

// 添加原始数据存储，用于正确计算百分比
const clueCompletedOriginalData = [
  { name: '按期办结', value: 0 },
  { name: '超时办结', value: 0 }
]

// 添加回被删除的线索件数量统计数据
const clueData = {
  total: 850,
  completed: 580,
  inProgress: 270
}

// 科技感颜色
const techColorPalette = ['#00c6ff', '#0072ff', '#00ffd8', '#00ff8f', '#ffe56c']
const gradients = [
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00c6ff' },
    { offset: 1, color: '#0072ff' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00ffd8' },
    { offset: 1, color: '#00a8ff' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00ff8f' },
    { offset: 1, color: '#00c6ff' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ffe56c' },
    { offset: 1, color: '#ff9e3d' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ff71e3' },
    { offset: 1, color: '#b54cff' }
  ])
]

// 为办理主体定义更加丰富和谐的渐变色组合
const deptColorGradients = [
  // 蓝色系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00c6ff' },
    { offset: 1, color: '#0072ff' }
  ]),
  // 青绿系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00ffd8' },
    { offset: 1, color: '#00b69b' }
  ]),
  // 绿色系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00ff8f' },
    { offset: 1, color: '#01cc72' }
  ]),
  // 橙黄系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ffe56c' },
    { offset: 1, color: '#ff9e3d' }
  ]),
  // 粉紫系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#f896ff' },
    { offset: 1, color: '#ca5cec' }
  ]),
  // 天蓝系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#5cd5ff' },
    { offset: 1, color: '#4287f5' }
  ]),
  // 紫蓝系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#a6a1ff' },
    { offset: 1, color: '#7571ff' }
  ]),
  // 薄荷绿系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#6dffd3' },
    { offset: 1, color: '#3adfb6' }
  ]),
  // 青蓝系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00dfff' },
    { offset: 1, color: '#3a9dc1' }
  ]),
  // 暖黄系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ffec99' },
    { offset: 1, color: '#ffbc4f' }
  ]),
  // 玫红系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ff9cd3' },
    { offset: 1, color: '#ff5b9c' }
  ]),
  // 灰紫系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#c8c3ff' },
    { offset: 1, color: '#8b85e0' }
  ]),
  // 海绿系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#64ffda' },
    { offset: 1, color: '#00c2a0' }
  ]),
  // 靛蓝系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#4680ff' },
    { offset: 1, color: '#2b59b3' }
  ]),
  // 浅橙系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ffbe7a' },
    { offset: 1, color: '#ff8d47' }
  ]),
  // 紫罗兰系列
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#c988ff' },
    { offset: 1, color: '#a95ae6' }
  ])
]

// 为线索处置方式定义更加鲜明对比的颜色
const disposalMethodGradients = [
  // 蓝色系
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00c6ff' },
    { offset: 1, color: '#0072ff' }
  ]),
  // 橙红色系
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ff9a7f' },
    { offset: 1, color: '#ff5252' }
  ]),
  // 紫色系
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#c288ff' },
    { offset: 1, color: '#8a2be2' }
  ]),
  // 绿色系
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00ff8f' },
    { offset: 1, color: '#01cc72' }
  ]),
  // 黄色系
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ffe56c' },
    { offset: 1, color: '#ffa500' }
  ]),
  // 粉红色系
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ff71e3' },
    { offset: 1, color: '#ff1493' }
  ]),
  // 青色系
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00ffd8' },
    { offset: 1, color: '#00b4b0' }
  ]),
  // 深蓝色系
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#4169e1' },
    { offset: 1, color: '#000080' }
  ])
]

// 线索办理主体分布数据
const clueDeptData = [
  { name: '光明地产纪委', value: 0 },
  { name: '糖酒集团纪委', value: 0 },
  { name: '光明乳业纪委', value: 0 },
  { name: '康养集团纪委', value: 0 },
  { name: '光明国际纪委', value: 0 },
  { name: '光明肉业纪委', value: 0 },
  { name: '城服集团纪委', value: 0 },
  { name: '水产集团纪委', value: 0 },
  { name: '崇明农场纪委', value: 0 },
  { name: '五四农场纪委', value: 0 },
  { name: '花卉集团纪委', value: 0 },
  { name: '上海农场纪委', value: 0 },
  { name: '蔬菜集团纪委', value: 0 },
  { name: '农发集团纪委', value: 0 },
  { name: '白茅岭农场纪委', value: 0 },
  { name: '粮储公司纪委', value: 0}
]

// 案件数量统计数据
const caseData = {
  total: 0,
  completed: 0,
  inProgress: 0
}

// 案件办理主体分布数据
const caseDeptData = [
  { name: '光明地产纪委', value: 0 },
  { name: '糖酒集团纪委', value: 0 },
  { name: '光明乳业纪委', value: 0 },
  { name: '康养集团纪委', value: 0 },
  { name: '光明国际纪委', value: 0 },
  { name: '光明肉业纪委', value: 0 },
  { name: '城服集团纪委', value: 0 },
  { name: '水产集团纪委', value: 0 },
  { name: '崇明农场纪委', value: 0 },
  { name: '五四农场纪委', value: 0 },
  { name: '花卉集团纪委', value: 0 },
  { name: '上海农场纪委', value: 0 },
  { name: '蔬菜集团纪委', value: 0 },
  { name: '农发集团纪委', value: 0 },
  { name: '白茅岭农场纪委', value: 0 },
  { name: '粮储公司纪委', value: 0}
]

// 为信访结果分布定义鲜明对比的颜色数组
const resultColors = [
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00ff8f' }, // 绿色渐变，表示进入线索
    { offset: 1, color: '#01cc72' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#f896ff' }, // 紫色渐变，表示信访阶段了结
    { offset: 1, color: '#ca5cec' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ff9a7f' }, // 橙色渐变，表示其他可能的结果
    { offset: 1, color: '#ff5252' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#5cd5ff' }, // 蓝色渐变，表示其他可能的结果
    { offset: 1, color: '#4287f5' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ffec99' }, // 黄色渐变，表示其他可能的结果 
    { offset: 1, color: '#ffbc4f' }
  ])
];

// 为信访举报方式定义鲜明对比的颜色数组
const methodColors = [
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00c6ff' }, // 蓝色渐变，用于"来信"
    { offset: 1, color: '#0072ff' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ff9a7f' }, // 橙红色渐变，用于"来访"
    { offset: 1, color: '#ff5252' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#00ff8f' }, // 绿色渐变，用于"来电"
    { offset: 1, color: '#01cc72' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#f896ff' }, // 紫色渐变，用于"网络举报"
    { offset: 1, color: '#ca5cec' }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: '#ffe56c' }, // 黄色渐变，用于"其他"
    { offset: 1, color: '#ff9e3d' }
  ])
];

// 初始化3D饼图
const initPie3dChart = () => {
  if (!pie3dChartRef.value) return
  pie3dChart = echarts.init(pie3dChartRef.value)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#00c6ff'
      },
      data: reportMethodsData.map(item => item.name),
      selectedMode: true,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: '#00ffd8'
        },
        textStyle: {
          color: '#00ffd8'
        }
      }
    },
    series: [
      {
        name: '举报方式',
        type: 'pie',
        radius: '60%',
        center: ['40%', '50%'],
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(0, 198, 255, 0.2)',
          borderWidth: 2
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200 + 300;
        },
        label: {
          show: true,
          position: 'outside',
          formatter: params => {
            const percent = ((params.value / reportMethodsData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1);
            return `${params.name}\n${params.value} (${percent}%)`;
          },
          fontSize: 14,
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            color: '#00ffd8',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.5)',
            borderWidth: 3,
            borderColor: '#00ffd8',
            scale: true,
            scaleSize: 10
          }
        },
        data: reportMethodsData.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: methodColors[index % methodColors.length]
          }
        }))
      }
    ]
  }
  
  pie3dChart.setOption(option)
}

// 初始化柱状图
const initBarChart = () => {
  if (!barChartRef.value) return
  barChart = echarts.init(barChartRef.value)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    legend: {
      data: ['数量'],
      textStyle: {
        color: '#00c6ff'
      },
      right: 10
    },
    xAxis: {
      type: 'category',
      data: reportMethodsData.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.5)'
        }
      },
      axisLabel: {
        color: '#00c6ff',
        fontSize: 12
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '数量',
      nameTextStyle: {
        color: '#00c6ff'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.5)'
        }
      },
      axisLabel: {
        color: '#00c6ff',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.1)'
        }
      }
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: 20,
        animationDelay: function (idx) {
          return idx * 100 + 100;
        },
        animationDuration: 1000,
        animationEasing: 'elasticOut',
        data: reportMethodsData.map((item, index) => ({
          value: item.value,
          itemStyle: {
            color: methodColors[index % methodColors.length]
          }
        })),
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 14,
          fontWeight: 'bold',
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.7)',
            borderWidth: 1,
            borderColor: '#00ffd8'
          },
          label: {
            fontSize: 16,
            color: '#00ffd8'
          }
        }
      }
    ]
  }

  barChart.setOption(option)
}

// 初始化雷达图
const initCategoryRadarChart = () => {
  if (!categoryRadarChartRef.value) return
  categoryRadarChart = echarts.init(categoryRadarChartRef.value)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    radar: {
      indicator: reportCategoryData.map(item => ({ name: item.name + ': ' + item.value, max: Math.max(...reportCategoryData.map(d => d.value)) * 1.2 })),
      shape: 'polygon',
      splitNumber: 5,
      center: ['50%', '55%'],
      radius: '70%',
      name: {
        textStyle: {
          color: '#00c6ff',
          fontSize: 12
        }
      },
      splitLine: {
        lineStyle: {
          color: ['rgba(0, 198, 255, 0.3)'],
          width: 1
        }
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(0, 198, 255, 0.02)', 'rgba(0, 198, 255, 0.05)', 'rgba(0, 198, 255, 0.08)', 'rgba(0, 198, 255, 0.12)', 'rgba(0, 198, 255, 0.15)']
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.5)'
        }
      }
    },
    series: [
      {
        name: '举报类别',
        type: 'radar',
        symbol: 'circle',
        symbolSize: 8,
        animation: true,
        animationDuration: 1500,
        animationEasing: 'cubicOut',
        lineStyle: {
          width: 3,
          color: gradients[1]
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 198, 255, 0.5)' },
            { offset: 1, color: 'rgba(0, 114, 255, 0.1)' }
          ])
        },
        data: [
          {
            value: reportCategoryData.map(item => item.value),
            name: '举报类别数量'
          }
        ]
      }
    ]
  }

  categoryRadarChart.setOption(option)
}

// 初始化结果分布图
const initResultChart = () => {
  if (!resultChartRef.value) return
  resultChart = echarts.init(resultChartRef.value)

  // 使用已定义的鲜明对比色数组
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#00c6ff'
      },
      data: reportResultData.map(item => item.name)
    },
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: '15%',
        style: {
          text: '信访处理结果',
          fill: '#00ffd8',
          fontSize: 16,
          fontWeight: 'bold'
        }
      }
    ],
    series: [
      {
        name: '处理结果',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '55%'],
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(0, 198, 255, 0.2)',
          borderWidth: 2
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200;
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c} ({d}%)',
          fontSize: 14,
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
        },
        emphasis: {
          label: {
            fontSize: 16,
            color: '#00ffd8',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.5)',
            scale: true,
            scaleSize: 10,
            borderColor: '#00ffd8',
            borderWidth: 2
          }
        },
        data: reportResultData.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: resultColors[index % resultColors.length]
          }
        }))
      }
    ]
  }

  resultChart.setOption(option)
}

// 初始化线索处置方式分布图
const initDisposalChart = () => {
  if (!disposalChartRef.value) return
  disposalChart = echarts.init(disposalChartRef.value)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#00c6ff',
        fontSize: 10
      },
      data: disposalMethodData.map(item => item.name)
    },
    series: [
      {
        name: '处置方式',
        type: 'pie',
        radius: ['30%', '70%'],
        center: ['50%', '45%'],
        roseType: 'radius',
        animationType: 'scale',
        animationEasing: 'quarticInOut',
        animationDelay: function (idx) {
          return idx * 100 + 100;
        },
        itemStyle: {
          borderRadius: 5,
          borderColor: 'rgba(0, 198, 255, 0.2)',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}',
          fontSize: 12,
          color: '#00c6ff'
        },
        emphasis: {
          label: {
            fontSize: 14,
            fontWeight: 'bold',
            color: '#00ffd8'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.5)',
            scale: true,
            scaleSize: 5,
            borderColor: '#00ffd8',
            borderWidth: 2
          }
        },
        data: disposalMethodData.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: disposalMethodGradients[index % disposalMethodGradients.length]
          }
        }))
      }
    ]
  }

  disposalChart.setOption(option)
}

// 初始化线索处置结果图
const initDisposalResultChart = () => {
  if (!disposalResultChartRef.value) return
  
  // 确保图表容器有固定高度
  if (disposalResultChartRef.value) {
    disposalResultChartRef.value.style.height = '300px';
  }
  
  // 过滤掉值为0的数据
  const filteredData = disposalResultData.filter(item => item.value > 0);

  // 确保至少有一个有效数据项
  if (filteredData.length === 0) {
    // 如果没有有效数据，显示提示信息
    if (disposalResultChartRef.value) {
      // 销毁已有图表实例
      if (disposalResultChart) {
        disposalResultChart.dispose();
        disposalResultChart = null;
      }
      
      // 设置容器样式
      disposalResultChartRef.value.style.display = 'flex';
      disposalResultChartRef.value.style.justifyContent = 'center';
      disposalResultChartRef.value.style.alignItems = 'center';
      disposalResultChartRef.value.style.height = '300px';
      disposalResultChartRef.value.style.width = '100%';
      disposalResultChartRef.value.style.backgroundColor = 'rgba(14, 30, 48, 0.5)';
      disposalResultChartRef.value.style.borderRadius = '8px';

      // 使用更美观的"暂无数据"显示
      disposalResultChartRef.value.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
        color: rgba(0, 198, 255, 0.7);
        font-family: 'Arial', sans-serif;
        text-align: center;
        padding: 20px;
      ">
        <div style="
          font-size: 24px;
          margin-bottom: 10px;
          font-weight: normal;
          text-shadow: 0 0 10px rgba(0, 198, 255, 0.3);
        ">暂无数据</div>
        <div style="
          width: 80%;
          max-width: 200px;
          height: 1px;
          background: linear-gradient(90deg, transparent, rgba(0, 198, 255, 0.5), transparent);
          margin: 15px 0;
        "></div>
        <div style="
          font-size: 14px;
          opacity: 0.7;
          max-width: 220px;
          line-height: 1.5;
        ">当前条件下没有符合条件的数据</div>
      </div>`;
    }
    return;
  }

  // 如果有数据，则初始化图表
  if (disposalResultChartRef.value) {
    // 清空容器内容
    disposalResultChartRef.value.innerHTML = '';
    // 重置样式为块级显示
    disposalResultChartRef.value.style.display = 'block';
  }

  disposalResultChart = echarts.init(disposalResultChartRef.value)

  const colorMap = {};
  disposalResultData.forEach((item, index) => {
    colorMap[item.name] = gradients[index % gradients.length];
  });

  const sortedData = filteredData
    .map(item => ({
      name: item.name,
      value: item.value,
      itemStyle: {
        color: colorMap[item.name]
      }
    }))
    .sort((a, b) => b.value - a.value);

  // 设置固定高度，与线索件数量统计图表保持一致
  if (disposalResultChartRef.value) {
    disposalResultChartRef.value.style.height = '300px';
  }

  // 获取数据的最大值，用于设置X轴的最大值和刻度
  const maxValue = Math.max(...sortedData.map(item => item.value), 0);
  
  // 确保有合理的最大值，避免单一数据时刻度问题
  const effectiveMaxValue = Math.max(maxValue, 1);
  
  // 处理单一数据值为1的特殊情况
  const isSingleDataWithValueOne = sortedData.length === 1 && sortedData[0].value === 1;
  const isSingleData = sortedData.length === 1;
  
  // 先创建X轴配置
  let xAxisConfig: any = {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: 'rgba(0, 198, 255, 0.5)'
      }
    },
    axisTick: {
      show: true,
      alignWithLabel: true
    },
    axisLabel: {
      color: '#00c6ff',
      fontSize: 12,
      formatter: function(value) {
        return Math.floor(value);
      },
      interval: 0,
      showMinLabel: true,
      showMaxLabel: true
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: 'rgba(0, 198, 255, 0.1)'
      }
    },
    // 特殊情况处理：单一数据时的处理
    max: function(value) {
      if (isSingleDataWithValueOne) {
        return 1.5; // 对于值为1的单一数据，固定最大值为1.5
      } 
      if (isSingleData) {
        return Math.ceil(sortedData[0].value * 1.5); // 对于任何单一数据，设置更大的最大值
      }
      return Math.ceil(value.max * 1.3);
    },
    // 确保刻度全是整数
    minInterval: 1
  };
  
  // 特殊情况下设置固定刻度
  if (isSingleDataWithValueOne) {
    // 保持value类型，不再修改为category类型
    xAxisConfig.max = 1.5; // 对于值为1的单一数据，固定最大值为1.5  
    xAxisConfig.minInterval = 1;
    xAxisConfig.splitNumber = 2;
  }

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        if (params && params.length > 0) {
          const param = params[0];
          return `${param.seriesName} <br/>${param.name}: ${param.value}`;
        }
        return '';
      },
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      left: '3%',
      right: '15%',
      bottom: '8%',
      top: '8%', // 保持与其他图表一致的顶部间距
      containLabel: true
    },
    xAxis: xAxisConfig,
    yAxis: {
      type: 'category',
      // 只使用过滤后的数据作为纵轴标签
      data: sortedData.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.5)'
        }
      },
      axisLabel: {
        color: '#00c6ff',
        fontSize: 12, // 统一字体大小
        interval: 0, // 强制显示所有标签
        width: 200, // 统一标签宽度
        overflow: 'break', // 文字过长时换行显示
        lineHeight: 14, // 统一行高
        margin: 8, // 统一标签边距
        formatter: function(value) {
          // 单一数据项时如果文字太长，适当截断
          if (isSingleData && value.length > 30) {
            return value.substring(0, 30) + '...';
          }
          return value;
        }
      },
      axisTick: {
        show: false
      }
    },
    legend: {
      show: false
    },
    series: [
      {
        name: '处置结果',
        type: 'bar',
        barWidth: sortedData.length <= 3 ? 30 : 18, // 不再为单一数据使用更宽的柱子
        // 只使用过滤后的数据
        data: sortedData,
        animationDuration: 1200,
        animationEasing: 'elasticOut',
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          fontSize: 12, // 统一字体大小
          fontWeight: 'bold',
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.7)',
            borderWidth: 1,
            borderColor: '#00ffd8'
          },
          label: {
            fontSize: 14,
            color: '#00ffd8'
          }
        }
      }
    ]
  };

  // 重新初始化，应用新的高度
  if (disposalResultChart) {
    disposalResultChart.dispose();
  }
  disposalResultChart = echarts.init(disposalResultChartRef.value);
  disposalResultChart.setOption(option);
}

// 初始化立案件政务处分结果图
const initPunishmentChart = () => {
  if (!punishmentChartRef.value) return
  punishmentChart = echarts.init(punishmentChartRef.value)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}人',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    title: {
      text: '立案件政务处分统计',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#00ffd8',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    legend: {
      show: false
    },
    xAxis: {
      type: 'category',
      data: punishmentData.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.5)'
        }
      },
      axisLabel: {
        color: '#00c6ff',
        fontSize: 12,
        interval: 0,
        rotate: 15 // 旋转标签以适应窄宽度
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '人数',
      nameTextStyle: {
        color: '#00c6ff'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.5)'
        }
      },
      axisLabel: {
        color: '#00c6ff',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.1)'
        }
      }
    },
    series: [
      {
        name: '处分人数',
        type: 'bar',
        barWidth: 20, // 减小柱宽以适应窄空间
        // 动画效果
        animationDelay: function (idx) {
          return idx * 120;
        },
        animationDuration: 1500,
        animationEasing: 'elasticOut',
        // 使用渐变色
        itemStyle: {
          normal: {
            color: function(params) {
              // 根据值的大小使用不同的颜色
              const colorList = [
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#00ffd8' },
                  { offset: 1, color: '#00a8ff' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#00c6ff' },
                  { offset: 1, color: '#0072ff' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#00a8ff' },
                  { offset: 1, color: '#0050c0' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#0072ff' },
                  { offset: 1, color: '#0040a0' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#0050c0' },
                  { offset: 1, color: '#003080' }
                ]),
                new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#0040a0' },
                  { offset: 1, color: '#002060' }
                ]),
              ];
              return colorList[params.dataIndex % colorList.length];
            },
            borderRadius: [4, 4, 0, 0]
          }
        },
        data: punishmentData.map(item => item.value),
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 12, // 减小字体大小
          fontWeight: 'bold',
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.7)',
            borderWidth: 1,
            borderColor: '#00ffd8'
          },
          label: {
            fontSize: 14,
            color: '#00ffd8'
          }
        }
      }
    ]
  }

  punishmentChart.setOption(option)
}

// 初始化立案件党纪处分结果图
const initPartyPunishmentChart = () => {
  if (!partyPunishmentChartRef.value) return
  partyPunishmentChart = echarts.init(partyPunishmentChartRef.value)

  // 创建渐变色：使用红色系列渐变以区分于政务处分的蓝色系
  const partyGradients = [
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#ff9a7f' },
      { offset: 1, color: '#ff5252' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#ff7070' },
      { offset: 1, color: '#e03030' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#e03030' },
      { offset: 1, color: '#b01010' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#d02020' },
      { offset: 1, color: '#900000' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#b01010' },
      { offset: 1, color: '#700000' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#900000' },
      { offset: 1, color: '#500000' }
    ])
  ]

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}人',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#ff5252',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    title: {
      text: '立案件党纪处分统计',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#ff7070',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    legend: {
      show: false
    },
    xAxis: {
      type: 'category',
      data: partyPunishmentData.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 82, 82, 0.5)'
        }
      },
      axisLabel: {
        color: '#ff7070',
        fontSize: 12,
        interval: 0,
        rotate: 20 // 增加旋转角度以适应长文本
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '人数',
      nameTextStyle: {
        color: '#ff7070'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 82, 82, 0.5)'
        }
      },
      axisLabel: {
        color: '#ff7070',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 82, 82, 0.1)'
        }
      }
    },
    series: [
      {
        name: '处分人数',
        type: 'bar',
        barWidth: 20, // 减小柱宽以适应窄空间
        // 动画效果
        animationDelay: function (idx) {
          return idx * 120;
        },
        animationDuration: 1500,
        animationEasing: 'elasticOut',
        // 使用渐变色
        itemStyle: {
          normal: {
            color: function(params) {
              return partyGradients[params.dataIndex % partyGradients.length];
            },
            borderRadius: [4, 4, 0, 0]
          }
        },
        data: partyPunishmentData.map(item => item.value),
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 12, // 减小字体大小
          fontWeight: 'bold',
          color: '#ff7070',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(255, 82, 82, 0.7)',
            borderWidth: 1,
            borderColor: '#ffcccc'
          },
          label: {
            fontSize: 14,
            color: '#ffcccc'
          }
        }
      }
    ]
  }

  partyPunishmentChart.setOption(option)
}

// 初始化信访件数量统计图表
const initPetitionCountChart = () => {
  if (!petitionCountChartRef.value) return
  petitionCountChart = echarts.init(petitionCountChartRef.value)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#00c6ff'
      },
      data: petitionCountData.value.map(item => item.name)
    },
    title: {
      text: `信访件总数: ${petitionTotalCount.value}件`,
      left: 'center',
      top: 10,
      textStyle: {
        color: '#00ffd8',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    series: [
      {
        name: '信访件数量',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['40%', '55%'],
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(0, 198, 255, 0.2)',
          borderWidth: 2
        },
        avoidLabelOverlap: false,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200;
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c} ({d}%)',
          fontSize: 14,
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            color: '#00ffd8',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.5)',
            borderWidth: 3,
            borderColor: '#00ffd8'
          }
        },
        data: [
          {
            name: '重复件',
            value: petitionCountData.value[0].value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#ff5b9c' },
                { offset: 1, color: '#ff0066' }
              ])
            }
          },
          {
            name: '非重复件',
            value: petitionCountData.value[1].value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00ff8f' },
                { offset: 1, color: '#01cc72' }
              ])
            }
          },
          {
            name: '暂未分类',
            value: petitionCountData.value[2].value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#ffe56c' },
                { offset: 1, color: '#ff9e3d' }
              ])
            }
          }
        ]
      }
    ]
  }
  
  petitionCountChart.setOption(option)
}

// 初始化线索件数量统计图表
const initClueCountChart = () => {
  if (!clueCountChartRef.value) return
  clueCountChart = echarts.init(clueCountChartRef.value)

  // 将线索数据转换为饼图所需的数据结构
  const clueCountData = [
    { name: '办结数', value: clueData.completed },
    { name: '在办数', value: clueData.inProgress }
  ]
  
  // 计算线索总数
  const clueTotalCount = clueData.total

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#00c6ff'
      },
      data: clueCountData.map(item => item.name)
    },
    title: {
      text: `线索件总数: ${clueTotalCount}件`,
      left: 'center',
      top: 10,
      textStyle: {
        color: '#00ffd8',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    series: [
      {
        name: '线索件数量',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['40%', '55%'],
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(0, 198, 255, 0.2)',
          borderWidth: 2
        },
        avoidLabelOverlap: false,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200;
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c} ({d}%)',
          fontSize: 14,
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            color: '#00ffd8',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.5)',
            borderWidth: 3,
            borderColor: '#00ffd8'
          }
        },
        data: [
          {
            name: '办结数',
            value: clueData.completed,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00ff8f' },
                { offset: 1, color: '#01cc72' }
              ])
            }
          },
          {
            name: '在办数',
            value: clueData.inProgress,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#ffd86f' },
                { offset: 1, color: '#ffb52e' }
              ])
            }
          }
        ]
      }
    ]
  }

  clueCountChart.setOption(option)
}

// 初始化在办线索数量图表
const initClueProgressChart = () => {
  if (!clueProgressChartRef.value) return
  clueProgressChart = echarts.init(clueProgressChartRef.value)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#00c6ff'
      },
      data: clueProgressData.map(item => item.name)
    },
    series: [
      {
        name: '在办线索',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(0, 198, 255, 0.2)',
          borderWidth: 2
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200;
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c} ({d}%)',
          fontSize: 14,
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            color: '#00ffd8',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.5)'
          }
        },
        data: [
          {
            name: clueProgressData[0].name,
            value: clueProgressData[0].value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#ff5252' },
                { offset: 1, color: '#ff9e3d' }
              ])
            }
          },
          {
            name: clueProgressData[1].name,
            value: clueProgressData[1].value,
            itemStyle: {
              color: gradients[1]
            }
          }
        ]
      }
    ]
  }
  
  clueProgressChart.setOption(option)
}

// 线索办理主体分布图表
const initClueDeptChart = () => {
  if (!clueDeptChartRef.value) return
  clueDeptChart = echarts.init(clueDeptChartRef.value)

  // 对数据进行排序（按值降序）
  const sortedData = [...clueDeptData].sort((a, b) => b.value - a.value);

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '线索办理主体分布',
      left: 'center',
      top: 0,
      textStyle: {
        color: '#00ffd8',
        fontSize: 18,
        fontWeight: 'normal',
        textShadow: '0 0 5px rgba(0, 255, 216, 0.5)'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}件',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      top: '5%',
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: sortedData.map(item => item.name),
      axisLabel: {
        color: '#ffffff',
        fontSize: 13,
        interval: 0,
        rotate: 45,
        width: 100,
        overflow: 'truncate',
        ellipsis: '...',
        fontWeight: 'normal',
        textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.8)',
          width: 2
        }
      },
      axisTick: {
        show: true,
        length: 5,
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.8)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '线索数量',
      nameTextStyle: {
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 'normal',
        padding: [0, 0, 0, 5],
        textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.8)',
          width: 2
        }
      },
      axisLabel: {
        color: '#ffffff',
        fontSize: 13,
        fontWeight: 'normal',
        textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.2)'
        }
      }
    },
    series: [
      {
        name: '线索数量',
        type: 'bar',
        barWidth: '50%',
        itemStyle: {
          normal: {
            color: function(params) {
              // 为每个办理主体分配一个独特的颜色，保持一致性
              return deptColorGradients[params.dataIndex % deptColorGradients.length];
            },
            borderRadius: [4, 4, 0, 0]
          }
        },
        data: sortedData.map(item => item.value),
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 13,
          fontWeight: 'normal',
          color: '#ffffff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.7)',
            borderWidth: 1,
            borderColor: '#ffffff'
          },
          label: {
            fontSize: 14,
            color: '#00ffd8'
          }
        }
      }
    ]
  }

  clueDeptChart.setOption(option)
}

// 案件数量统计图表
const initCaseCountChart = () => {
  if (!caseCountChartRef.value) return
  caseCountChart = echarts.init(caseCountChartRef.value)

  // 将案件数据转换为饼图所需的数据结构
  const caseCountData = [
    { name: '办结数', value: caseData.completed },
    { name: '在办数', value: caseData.inProgress }
  ]
  
  // 计算案件总数
  const caseTotalCount = caseData.total

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#00c6ff'
      },
      data: caseCountData.map(item => item.name)
    },
    title: {
      text: `立案件总数: ${caseTotalCount}件`,
      left: 'center',
      top: 10,
      textStyle: {
        color: '#00ffd8',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    series: [
      {
        name: '立案件数量',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['40%', '55%'],
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(0, 198, 255, 0.2)',
          borderWidth: 2
        },
        avoidLabelOverlap: false,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200;
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c} ({d}%)',
          fontSize: 14,
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            color: '#00ffd8',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.5)',
            borderWidth: 3,
            borderColor: '#00ffd8'
          }
        },
        data: [
          {
            name: '办结数',
            value: caseData.completed,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00ffd8' },
                { offset: 1, color: '#00b69b' }
              ])
            }
          },
          {
            name: '在办数',
            value: caseData.inProgress,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#f896ff' },
                { offset: 1, color: '#ca5cec' }
              ])
            }
          }
        ]
      }
    ]
  }

  caseCountChart.setOption(option)
}

// 添加数据更新功能来模拟实时数据变化
const setupDataUpdate = () => {
  const updateInterval = setInterval(() => {
    // 不再随机更新reportMethodsData，确保使用接口返回的真实数据
    
    // 其他模拟数据可以继续随机更新
    // 例如可以在这里随机更新其他不是从接口获取的数据
  }, 10000);
  
  onUnmounted(() => {
    clearInterval(updateInterval);
  });
};

// 处理窗口大小变化
const handleResize = () => {
  pie3dChart?.resize()
  barChart?.resize()
  categoryRadarChart?.resize()
  resultChart?.resize()
  disposalChart?.resize()
  disposalResultChart?.resize()
  punishmentChart?.resize()
  partyPunishmentChart?.resize()
  petitionCountChart?.resize()
  clueCountChart?.resize()
  clueProgressChart?.resize()
  clueDeptChart?.resize()
  caseCountChart?.resize()
  caseDeptChart?.resize()
  clueCompletedChart?.resize()
}

// 获取二级单位数据
const fetchSecondaryDepts = async () => {
  try {
    const res = await getSecondaryDepartments()
    if (res) {
      // 只处理案件办理主体分布图表数据
      // 线索办理主体分布图表数据现在从fetchBpmClueStatistics的depthStats获取
      
      // 模拟案件数量数据，实际项目中应该从后端获取
      const deptCaseData = res.map(dept => {
        return {
          name: dept.deptName,
          value: Math.floor(Math.random() * 50) + 10 // 模拟数据
        }
      })
      
      // 只更新案件办理主体分布图表
      if (caseDeptChart) {
        caseDeptChart.setOption({
          legend: {
            data: deptCaseData.map(item => item.name)
          },
          title: {
            text: '案件办理主体分布',
            left: 'center',
            top: 0,
            textStyle: {
              color: '#00ffd8',
              fontSize: 18,
              fontWeight: 'normal',
              textShadow: '0 0 5px rgba(0, 255, 216, 0.5)'
            }
          },
          series: [{
            data: deptCaseData.map((item, index) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: deptColorGradients[index % deptColorGradients.length]
              }
            }))
          }]
        })
      }
    }
  } catch (error) {
    console.error('获取二级单位数据失败', error)
  }
}

// 刷新数据方法，供父组件调用
const refreshData = () => {
  // 调用获取数据的方法
  console.log('手动刷新数据')
  try {
    // 默认先隐藏线索办理主体分布图表，直到获取到有效数据
    showClueDeptSection.value = false
    
    // 准备参数
    const params = {
      deptId: props.deptId || '',
      startDate: props.startDate || '',
      endDate: props.endDate || ''
    }
    
    console.log('刷新数据使用参数:', params)
    
    // 调用相关API获取数据
    fetchBpmLettersStatistics(params)
    fetchBpmClueStatistics(params)
    fetchBpmFilingStatistics(params)
  } catch (error) {
    console.error('刷新数据失败:', error)
  }
}

// 在组件中导出 refreshData 方法
defineExpose({
  refreshData
})

// 组件挂载时初始化图表
onMounted(() => {
  console.log('ReportMethodsTechChart组件挂载，当前category:', props.category)
  console.log('显示信访图表:', showXinFangSection.value)
  console.log('显示线索图表:', showXianSuoSection.value)
  console.log('显示立案图表:', showLiAnSection.value)
  
  initPie3dChart()
  initBarChart()
  initCategoryRadarChart()
  initResultChart()
  initDisposalChart()
  initDisposalResultChart()
  initPunishmentChart()
  initPartyPunishmentChart()
  initPetitionCountChart()
  initClueCountChart()
  initClueProgressChart()
  initClueDeptChart()
  initCaseCountChart()
  initCaseDeptChart()
  initClueCompletedChart()
  
  // 图表初始化完成后，立即加载数据，确保图表能够显示
  refreshData()
  
  setupDataUpdate()
  
  window.addEventListener('resize', handleResize)
})

// 移除对props变化的监听，改为由父组件手动调用刷新数据
// watch(
//   [() => props.deptId, () => props.startDate, () => props.endDate],
//   ([newDeptId, newStartDate, newEndDate], [oldDeptId, oldStartDate, oldEndDate]) => {
//     if (newDeptId !== oldDeptId || newStartDate !== oldStartDate || newEndDate !== oldEndDate) {
//       // 当任何查询参数变化时，重新获取数据
//       fetchBpmLettersStatistics({
//         deptId: newDeptId,
//         startDate: newStartDate,
//         endDate: newEndDate
//       })
//       
//       // 同时获取线索件和立案件统计数据
//       fetchBpmClueStatistics()
//       fetchBpmFilingStatistics()
//     }
//   }
// )

// 组件卸载时清理
onUnmounted(() => {
  pie3dChart?.dispose()
  barChart?.dispose()
  categoryRadarChart?.dispose()
  resultChart?.dispose()
  disposalChart?.dispose()
  disposalResultChart?.dispose()
  punishmentChart?.dispose()
  partyPunishmentChart?.dispose()
  petitionCountChart?.dispose()
  clueCountChart?.dispose()
  clueProgressChart?.dispose()
  clueDeptChart?.dispose()
  caseCountChart?.dispose()
  caseDeptChart?.dispose()
  clueCompletedChart?.dispose()
  
  window.removeEventListener('resize', handleResize)
})

// 案件办理主体分布图表
const initCaseDeptChart = () => {
  if (!caseDeptChartRef.value) return
  caseDeptChart = echarts.init(caseDeptChartRef.value)

  // 对数据进行排序（按值降序）
  const sortedData = [...caseDeptData].sort((a, b) => b.value - a.value);

  const option = {
    backgroundColor: 'transparent',
    title: {
      text: '立案件办理主体分布',
      left: 'center',
      top: 0,
      textStyle: {
        color: '#00ffd8',
        fontSize: 18,
        fontWeight: 'normal',
        textShadow: '0 0 5px rgba(0, 255, 216, 0.5)'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}件',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    grid: {
      top: '5%',
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: sortedData.map(item => item.name),
      axisLabel: {
        color: '#ffffff',
        fontSize: 13,
        interval: 0,
        rotate: 45,
        width: 100,
        overflow: 'truncate',
        ellipsis: '...',
        fontWeight: 'normal',
        textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.8)',
          width: 2
        }
      },
      axisTick: {
        show: true,
        length: 5,
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.8)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '案件数量',
      nameTextStyle: {
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 'normal',
        padding: [0, 0, 0, 5],
        textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.8)',
          width: 2
        }
      },
      axisLabel: {
        color: '#ffffff',
        fontSize: 13,
        fontWeight: 'normal',
        textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 198, 255, 0.2)'
        }
      }
    },
    series: [
      {
        name: '案件数量',
        type: 'bar',
        barWidth: '50%',
        itemStyle: {
          normal: {
            color: function(params) {
              // 为每个办理主体使用与线索办理主体图表一致的颜色，保持视觉一致性
              // 找到对应名称的单位在线索办理主体中的索引
              const unitName = sortedData[params.dataIndex].name;
              const clueSortedData = [...clueDeptData].sort((a, b) => b.value - a.value);
              const matchingIndex = clueSortedData.findIndex(item => item.name === unitName);
              
              // 如果找到匹配项，使用相同的颜色索引，否则使用当前索引
              const colorIndex = matchingIndex !== -1 ? matchingIndex % deptColorGradients.length : params.dataIndex % deptColorGradients.length;
              return deptColorGradients[colorIndex];
            },
            borderRadius: [4, 4, 0, 0]
          }
        },
        data: sortedData.map(item => item.value),
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 13,
          fontWeight: 'normal',
          color: '#ffffff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.7)',
            borderWidth: 1,
            borderColor: '#ffffff'
          },
          label: {
            fontSize: 14,
            color: '#00ffd8'
          }
        }
      }
    ]
  }

  caseDeptChart.setOption(option)
}

// 监听category变化
watch(() => props.category, (newCategory) => {
  console.log('ReportMethodsTechChart组件检测到category变化:', newCategory)
  console.log('显示信访图表:', showXinFangSection.value, '(category === oa_xf || category === total)')
  console.log('显示线索图表:', showXianSuoSection.value, '(category === oa_xs || category === total)')
  console.log('显示立案图表:', showLiAnSection.value, '(category === oa_register || category === total)')
  
  // 当图表加载完成后，需要重新调整大小以确保正确显示
  nextTick(() => {
    handleResize()
  })
}, { immediate: true })

// 监听props变化
watch(
  () => props.category,
  (newCategory) => {
    console.log('ReportMethodsTechChart接收到新的category:', newCategory)
    console.log('各图表区域显示状态:')
    console.log('- 第一行 (类别雷达、在办件柱状图):', newCategory === '' || newCategory === 'all' || newCategory === undefined)
    console.log('- 第二行 (线索统计饼图、分类别统计饼图):', newCategory === '' || newCategory === 'clue' || newCategory === 'all' || newCategory === undefined)
    console.log('- 第三行 (信访结果分布、处置结果):', newCategory === '' || newCategory === 'clue' || newCategory === 'all' || newCategory === undefined)
    console.log('- 第四行 (部门接触统计、处置方式):', newCategory === '' || newCategory === 'clue' || newCategory === 'all' || newCategory === undefined)
    console.log('- 第五行 (案件统计饼图):', newCategory === 'case' || newCategory === 'all' || newCategory === undefined)
    console.log('- 第六行 (部门案件量统计):', newCategory === 'case' || newCategory === 'all' || newCategory === undefined)
  },
  { immediate: true }
)

// 获取信访件统计数据
const fetchPetitionStats = async (customParams?: {
  deptId?: string,
  startDate?: string,
  endDate?: string
}) => {
  // 移除自动请求的逻辑，改为由父组件手动调用refreshData方法
  console.log('fetchPetitionStats被调用，但不会自动请求数据')
}

// 移除对于watch的监听，防止props变化自动请求数据
// watch(
//   () => [props.deptId, props.startDate, props.endDate],
//   () => {
//     // 当查询条件变化时重新获取数据
//     fetchPetitionStats()
//   },
//   { immediate: true }
// )

onMounted(() => {
  // 获取信访统计数据
  // 移除对于fetchPetitionStats的调用
  // fetchPetitionStats()
  
  // 初始化各个图表
  initPie3dChart()
  initBarChart()
  initCategoryRadarChart()
  initResultChart()
  initDisposalChart()
  initDisposalResultChart()
  initPunishmentChart()
  initPartyPunishmentChart()
  initPetitionCountChart()
  initClueCountChart()
  initClueProgressChart()
  fetchSecondaryDepts()

  // 设置数据更新定时器，不再使用随机生成的数据
  setupDataUpdate();

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 移除另一个重复的updatePetitionCharts函数，保留注释
// 更新信访相关图表
// const updatePetitionCharts = () => {
//   // 不再更新举报方式的图表，因为已在fetchBpmLettersStatistics中通过接口数据更新
//   
//   // 更新雷达图
//   if (categoryRadarChart) {
//     categoryRadarChart.setOption({
//       radar: {
//         indicator: reportCategoryData.map(item => ({
//           name: item.name,
//           max: Math.max(...reportCategoryData.map(i => i.value)) * 1.2
//         }))
//       },
//       series: [{
//         data: [{
//           value: reportCategoryData.map(item => item.value),
//           name: '举报类别数量',
//           label: {
//             show: true,
//             formatter: '{c}',
//             color: '#00ffd8',
//             fontSize: 14
//           }
//         }],
//         label: {
//           show: true,
//           formatter: function(params) {
//             return params.value;
//           },
//           color: '#ffffff',
//           fontSize: 14,
//           fontWeight: 'bold',
//           textShadow: '0 0 3px rgba(0, 0, 0, 0.8)'
//         }
//       }]
//     })
//   }
//   
//   // 不再更新结果分布图，因为已在fetchBpmLettersStatistics中通过接口数据更新
//   
//   // 不再需要在这里更新信访件数量统计图表，因为已在fetchBpmLettersStatistics中进行了更新
// }

// 获取信访件统计数据
const fetchBpmLettersStatistics = async (params) => {
  try {
    console.log('获取信访件统计信息，使用参数:', params)
    const res = await getBpmLettersStatistics(params)
    console.log('获取到信访件统计信息', res)

    // 处理信访件总数和分类数据
    if (res) {
      // 处理总数数据
      if (res.totalAmountStats && res.totalAmountStats.length > 0) {
        const totalData = res.totalAmountStats.find(item => item.name === '总数')
        petitionTotalCount.value = totalData ? totalData.amount : 0
      }
      
      // 处理重复件和非重复件数据
      if (res.repeatAmountStats && res.repeatAmountStats.length > 0) {
        const repeatData = res.repeatAmountStats.find(item => item.name === '重复件')
        const nonRepeatData = res.repeatAmountStats.find(item => item.name === '非重复件')
        
        // 更新重复件和非重复件的值
        if (repeatData) {
          petitionCountData.value[0].value = repeatData.amount
        }
        
        if (nonRepeatData) {
          petitionCountData.value[1].value = nonRepeatData.amount
        }
        
        // 计算暂未分类的数量
        const repeatedCount = repeatData ? repeatData.amount : 0
        const nonRepeatedCount = nonRepeatData ? nonRepeatData.amount : 0
        
        // 暂未分类 = 总数 - 重复件数量 - 非重复件数量
        petitionCountData.value[2].value = Math.max(0, petitionTotalCount.value - repeatedCount - nonRepeatedCount)
        
        // 更新信访件数量统计图表
        if (petitionCountChart) {
          petitionCountChart.setOption({
            title: {
              text: `信访件总数: ${petitionTotalCount.value}件`
            },
            legend: {
              data: petitionCountData.value.map(item => item.name)
            },
            series: [{
              data: [
                {
                  name: '重复件',
                  value: petitionCountData.value[0].value,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#ff5b9c' },
                      { offset: 1, color: '#ff0066' }
                    ])
                  }
                },
                {
                  name: '非重复件',
                  value: petitionCountData.value[1].value,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#00ff8f' },
                      { offset: 1, color: '#01cc72' }
                    ])
                  }
                },
                {
                  name: '暂未分类',
                  value: petitionCountData.value[2].value,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#ffe56c' },
                      { offset: 1, color: '#ff9e3d' }
                    ])
                  }
                }
              ]
            }]
          })
        }
      }
      
      // 更新信访举报类别数据
      if (res.typeStats && res.typeStats.length > 0) {
        console.log('更新信访举报类别数据:', res.typeStats)
        console.log('更新前 reportCategoryData:', JSON.stringify([...reportCategoryData]))
        
        // 将API返回的类别数据转换为图表所需格式
        try {
          // 清空原有数据
          reportCategoryData.splice(0, reportCategoryData.length)
          
          // 添加新数据，并确保数据格式正确
          res.typeStats.forEach(item => {
            // 确保 name 和 amount 存在
            if (item && typeof item.name === 'string' && (typeof item.amount === 'number' || !isNaN(Number(item.amount)))) {
              reportCategoryData.push({
                name: item.name,
                value: Number(item.amount) // 确保值是数字类型
              })
            }
          })
          
          console.log('更新后 reportCategoryData:', JSON.stringify(reportCategoryData))
          
          if (reportCategoryData.length === 0) {
            console.warn('处理后的类别数据为空，添加默认数据')
            // 如果处理后没有有效数据，添加一些默认数据
            reportCategoryData.push(
              { name: '检举控告类', value: 0 },
              { name: '批评建议类', value: 0 },
              { name: '申诉类', value: 0 },
              { name: '其他', value: 0 }
            )
          }
        } catch (error) {
          console.error('处理类别数据时出错:', error)
        }
        
        // 更新雷达图
        if (categoryRadarChart) {
          // 记录引用
          const chartDom = categoryRadarChartRef.value;
          
          // 销毁旧的图表实例
          categoryRadarChart.dispose();
          
          // 创建全新的图表实例
          categoryRadarChart = echarts.init(chartDom);
          
          // 完全重建雷达图的配置
          // 确保在数据为空或者数值很小时也有合理的显示
          let maxVal = Math.max(...reportCategoryData.map(d => d.value), 10) * 1.2;
          if (!isFinite(maxVal) || maxVal <= 0) {
            maxVal = 100; // 设置默认最大值
          }
          console.log('雷达图使用的最大值:', maxVal);
          const newOption = {
            backgroundColor: 'transparent',
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c}',
              backgroundColor: 'rgba(10, 20, 30, 0.8)',
              borderColor: '#00c6ff',
              borderWidth: 1,
              textStyle: {
                color: '#fff'
              }
            },
            radar: {
              indicator: reportCategoryData.map(item => ({ 
                name: item.name + ': ' + item.value, 
                max: maxVal 
              })),
              shape: 'polygon',
              splitNumber: 5,
              center: ['50%', '55%'],
              radius: '70%',
              name: {
                textStyle: {
                  color: '#00c6ff',
                  fontSize: 12
                }
              },
              splitLine: {
                lineStyle: {
                  color: ['rgba(0, 198, 255, 0.3)'],
                  width: 1
                }
              },
              splitArea: {
                show: true,
                areaStyle: {
                  color: ['rgba(0, 198, 255, 0.02)', 'rgba(0, 198, 255, 0.05)', 'rgba(0, 198, 255, 0.08)', 'rgba(0, 198, 255, 0.12)', 'rgba(0, 198, 255, 0.15)']
                }
              },
              axisLine: {
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.5)'
                }
              }
            },
            series: [
              {
                name: '举报类别',
                type: 'radar',
                symbol: 'circle',
                symbolSize: 8,
                animation: true,
                lineStyle: {
                  width: 3,
                  color: gradients[1]
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(0, 198, 255, 0.5)' },
                    { offset: 1, color: 'rgba(0, 114, 255, 0.1)' }
                  ])
                },
                data: [
                  {
                    value: reportCategoryData.map(item => item.value),
                    name: '举报类别数量'
                  }
                ]
              }
            ]
          };
          
          console.log('雷达图重新创建并使用新数据:', reportCategoryData);
          // 应用配置到新的图表实例
          categoryRadarChart.setOption(newOption);
        }
      }
      
      // 更新信访结果分布数据
      if (res.resultStats && res.resultStats.length > 0) {
        console.log('更新信访结果分布数据:', res.resultStats)
        
        // 清空原有数据
        reportResultData.splice(0, reportResultData.length)
        
        // 添加新数据
        res.resultStats.forEach(item => {
          reportResultData.push({
            name: item.name,
            value: item.amount
          })
        })
        
        // 更新结果分布图
        if (resultChart) {
          resultChart.setOption({
            legend: {
              orient: 'vertical',
              right: 10,
              top: 'center',
              textStyle: {
                color: '#00c6ff'
              },
              data: reportResultData.map(item => item.name)
            },
            series: [{
              data: reportResultData.map((item, index) => ({
                name: item.name,
                value: item.value,
                itemStyle: {
                  color: resultColors[index % resultColors.length]
                }
              }))
            }]
          })
        }
      }
    }

    // 使用methodStats真实数据更新信访举报方式数据
    if (res && res.methodStats && res.methodStats.length > 0) {
      // 将接口返回的methodStats转换为reportMethodsData需要的格式
      const newMethodsData = res.methodStats.map(item => ({
        name: item.name,
        value: item.amount
      }))
      
      // 清空原有数据并添加新数据
      reportMethodsData.splice(0, reportMethodsData.length, ...newMethodsData)
      
      // 更新信访举报方式相关图表
      if (barChart) {
        barChart.setOption({
          xAxis: {
            data: reportMethodsData.map(item => item.name)
          },
          series: [{
            data: reportMethodsData.map((item, index) => ({
              value: item.value,
              itemStyle: {
                color: methodColors[index % methodColors.length]
              }
            }))
          }]
        })
      }
      
      if (pie3dChart) {
        pie3dChart.setOption({
          legend: {
            data: reportMethodsData.map(item => item.name)
          },
          series: [{
            data: reportMethodsData.map((item, index) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: methodColors[index % methodColors.length]
              }
            }))
          }]
        })
      }
    }
  } catch (error) {
    console.error('获取信访件统计数据失败:', error)
  }
}

// 获取线索件统计数据
const fetchBpmClueStatistics = async (params) => {
  try {
    // 发送请求获取线索件统计数据
    console.log('获取线索件统计信息，使用参数:', params)
    const res = await getBpmClueStatistics(params)
    console.log('获取到线索件统计信息：', res)
    
    // 更新线索相关图表数据
    if (res) {
      // 处理线索件总数和分类数据
      if (res.totalAmountStats && res.totalAmountStats.length > 0) {
        // 更新clueData对象中的数据
        const totalData = res.totalAmountStats.find(item => item.name === '总数')
        const completedData = res.totalAmountStats.find(item => item.name === '办结数')
        const inProgressData = res.totalAmountStats.find(item => item.name === '在办数')
        
        // 确保数据存在才进行更新
        if (totalData) {
          clueData.total = totalData.amount || 0
        }
        
        if (completedData) {
          clueData.completed = completedData.amount || 0
        }
        
        if (inProgressData) {
          clueData.inProgress = inProgressData.amount || 0
        }
        
        // 更新线索件数量统计图表
        if (clueCountChart) {
          clueCountChart.setOption({
            title: {
              text: `线索件总数: ${clueData.total}件`
            },
            series: [{
              data: [
                {
                  name: '办结数',
                  value: clueData.completed,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#00ff8f' },
                      { offset: 1, color: '#01cc72' }
                    ])
                  }
                },
                {
                  name: '在办数',
                  value: clueData.inProgress,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#ffd86f' },
                      { offset: 1, color: '#ffb52e' }
                    ])
                  }
                }
              ]
            }]
          })
        }
        
        // 更新在办线索数量（超时未办结和按期办理中）
        // 使用接口返回的doStats数据
        if (res.doStats && res.doStats.length > 0) {
          // 查找在办-未超时和在办-超时的数据
          const onTimeInProgress = res.doStats.find(item => item.name === '在办-未超时');
          const overdueInProgress = res.doStats.find(item => item.name === '在办-超时');
          
          // 更新clueProgressData数据
          clueProgressData[0].value = overdueInProgress ? overdueInProgress.amount : 0;  // 超时未办结
          clueProgressData[1].value = onTimeInProgress ? onTimeInProgress.amount : 0;  // 按期办理中
          
          // 更新在办线索数量图表
          if (clueProgressChart) {
            clueProgressChart.setOption({
              series: [{
                data: [
                  {
                    name: '超时未办结',
                    value: clueProgressData[0].value,
                    itemStyle: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#ff5252' },
                        { offset: 1, color: '#ff9e3d' }
                      ])
                    }
                  },
                  {
                    name: '按期办理中',
                    value: clueProgressData[1].value,
                    itemStyle: {
                      color: gradients[1]
                    }
                  }
                ]
              }]
            })
          }
        }
        
        // 更新已办线索数据（按期办结和超时办结）
        // 使用接口返回的doneStats数据
        if (res.doneStats && res.doneStats.length > 0) {
          // 查找已办-未超时和已办-超时的数据
          const onTimeCompleted = res.doneStats.find(item => item.name === '已办-未超时');
          const overdueCompleted = res.doneStats.find(item => item.name === '已办-超时');
          
          // 保存原始数据用于百分比计算
          clueCompletedOriginalData[0].value = onTimeCompleted ? onTimeCompleted.amount : 0;  // 按期办结
          clueCompletedOriginalData[1].value = overdueCompleted ? overdueCompleted.amount : 0;  // 超时办结
          
          // 更新clueCompletedData数据，即使数据不存在或为0，也设置默认值0
          clueCompletedData[0].value = onTimeCompleted ? onTimeCompleted.amount : 0;  // 按期办结
          clueCompletedData[1].value = overdueCompleted ? overdueCompleted.amount : 0;  // 超时办结
          
          // 计算总数
          const totalCompleted = (onTimeCompleted ? onTimeCompleted.amount : 0) + 
                                (overdueCompleted ? overdueCompleted.amount : 0);
          
          // 始终显示图表，即使数据全为0
          showClueCompletedSection.value = true;
          
          // 如果图表已经显示，更新图表数据
          if (showClueCompletedSection.value && clueCompletedChart) {
            clueCompletedChart.setOption({
              title: {
                text: `已办线索总数: ${totalCompleted}件`
              },
              series: [{
                data: [
                  {
                    name: '按期办结',
                    value: clueCompletedData[0].value || 0.001, // 使用微小值替代0，确保图表能正常渲染
                    itemStyle: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#00ff8f' },
                        { offset: 1, color: '#01cc72' }
                      ])
                    }
                  },
                  {
                    name: '超时办结',
                    value: clueCompletedData[1].value || 0.001, // 使用微小值替代0，确保图表能正常渲染
                    itemStyle: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#ff5252' },
                        { offset: 1, color: '#ff9e3d' }
                      ])
                    }
                  }
                ]
              }]
            });
          }
        } else {
          // 即使没有已办线索数据，也显示图表
          clueCompletedOriginalData[0].value = 0;
          clueCompletedOriginalData[1].value = 0;
          clueCompletedData[0].value = 0;
          clueCompletedData[1].value = 0;
          showClueCompletedSection.value = true;
        }
      }
      
      // 处理线索处置方式数据
      if (res.disposalMethodStats && res.disposalMethodStats.length > 0) {
        // 清空原有数据
        disposalMethodData.splice(0, disposalMethodData.length)
        
        // 添加新数据
        res.disposalMethodStats.forEach(item => {
          if (item && typeof item.name === 'string' && (typeof item.amount === 'number' || !isNaN(Number(item.amount)))) {
            disposalMethodData.push({
              name: item.name,
              value: Number(item.amount)
            })
          }
        })
        
        // 更新线索处置方式图表
        if (disposalChart) {
          disposalChart.setOption({
            legend: {
              data: disposalMethodData.map(item => item.name)
            },
            series: [{
              data: disposalMethodData.map((item, index) => ({
                name: item.name,
                value: item.value,
                itemStyle: {
                  color: disposalMethodGradients[index % disposalMethodGradients.length]
                }
              }))
            }]
          })
        }
      }
      
      // 处理线索处置结果数据
      if (res.disposalResultsStats && res.disposalResultsStats.length > 0) {
        // 清空原有数据
        disposalResultData.splice(0, disposalResultData.length)
        
        // 添加新数据
        res.disposalResultsStats.forEach(item => {
          if (item && typeof item.name === 'string' && (typeof item.amount === 'number' || !isNaN(Number(item.amount)))) {
            disposalResultData.push({
              name: item.name,
              value: Number(item.amount)
            })
          }
        })
        
        // 清除现有的图表实例，强制重新构建
        if (disposalResultChart) {
          disposalResultChart.dispose();
          disposalResultChart = null;
        }
        
        // 重新执行初始化图表的函数，这会重新判断是否有数据并处理
        // 由于数据已经更新，图表会基于新数据渲染
        nextTick(() => {
          initDisposalResultChart();
        });
      }
      
      // 处理线索办理主体分布数据
      handleClueDeptData(res)
    }
  } catch (error) {
    console.error('获取线索件统计信息失败:', error)
  }
}

// 获取立案件统计数据
const fetchBpmFilingStatistics = async (params) => {
  try {
    // 发送请求获取立案件统计数据
    console.log('获取立案件统计信息，使用参数:', params)
    const res = await getBpmFilingStatistics(params)
    console.log('获取到立案件统计信息：', res)
    
    // 默认先隐藏立案件办理主体分布图表，直到获取到有效数据
    showCaseDeptSection.value = false
    
    // 更新立案相关图表数据
    if (res) {
      // 处理立案件总数和分类数据
      if (res.totalAmountStats && res.totalAmountStats.length > 0) {
        // 更新caseData对象中的数据
        const totalData = res.totalAmountStats.find(item => item.name === '总数')
        const completedData = res.totalAmountStats.find(item => item.name === '办结数')
        const inProgressData = res.totalAmountStats.find(item => item.name === '在办数')
        
        // 确保数据存在才进行更新
        if (totalData) {
          caseData.total = totalData.amount || 0
        }
        
        if (completedData) {
          caseData.completed = completedData.amount || 0
        }
        
        if (inProgressData) {
          caseData.inProgress = inProgressData.amount || 0
        }
        
        // 更新案件数量统计图表
        if (caseCountChart) {
          caseCountChart.setOption({
            title: {
              text: `立案件总数: ${caseData.total}件`
            },
            series: [{
              data: [
                {
                  name: '办结数',
                  value: caseData.completed,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#00ffd8' },
                      { offset: 1, color: '#00b69b' }
                    ])
                  }
                },
                {
                  name: '在办数',
                  value: caseData.inProgress,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#f896ff' },
                      { offset: 1, color: '#ca5cec' }
                    ])
                  }
                }
              ]
            }]
          })
        }
      }
      
      // 处理立案件办理主体分布数据
      try {
        console.log('开始处理立案件办理主体分布数据')
        if (res.deptStats && Array.isArray(res.deptStats)) {
          // 确保deptStats是数组且不为空
          console.log(`deptStats是数组，长度为${res.deptStats.length}`)
          
          // 检查是否有非零数据
          const hasNonZeroData = res.deptStats.some(item => item && item.amount > 0)
          console.log(`存在非零数据: ${hasNonZeroData}`)
          
          if (hasNonZeroData) {
            // 只有在有非零数据时才显示图表并更新数据
            console.log('设置showCaseDeptSection为true')
            showCaseDeptSection.value = true
            
            // 清空原有数据
            caseDeptData.splice(0, caseDeptData.length)
            
            // 添加新数据，并进行额外验证确保数据有效
            res.deptStats.forEach(item => {
              if (item && typeof item.name === 'string' && typeof item.amount !== 'undefined') {
                const value = Number(item.amount)
                if (!isNaN(value)) {
                  caseDeptData.push({
                    name: item.name,
                    value: value
                  })
                }
              }
            })
            
            console.log('处理后的立案件办理主体数据:', caseDeptData)
            
            // 确保至少有一个有效数据
            if (caseDeptData.length > 0) {
              // 对数据进行排序（按值降序）
              const sortedData = [...caseDeptData].sort((a, b) => b.value - a.value);
              
              // 数据处理完成，图表初始化和渲染将由watch(showCaseDeptSection)处理
            } else {
              console.log('处理后没有有效数据，隐藏图表')
              showCaseDeptSection.value = false
            }
          } else {
            // 如果所有数据都是0，隐藏图表
            console.log('所有数据都是0，隐藏图表')
            showCaseDeptSection.value = false
          }
        } else {
          // 如果deptStats不是数组或为空，隐藏图表
          console.log('deptStats不是数组或为空，隐藏图表')
          showCaseDeptSection.value = false
        }
      } catch (error) {
        console.error('处理立案件办理主体分布数据时出错:', error)
        showCaseDeptSection.value = false
      }
      
      // 处理政务处分结果数据
      if (res.affairsResultStats && res.affairsResultStats.length > 0) {
        // 清空原有数据
        punishmentData.splice(0, punishmentData.length)
        
        // 添加新数据
        res.affairsResultStats.forEach(item => {
          if (item && typeof item.name === 'string' && (typeof item.amount === 'number' || !isNaN(Number(item.amount)))) {
            punishmentData.push({
              name: item.name,
              value: Number(item.amount)
            })
          }
        })
        
        // 更新政务处分结果图表
        if (punishmentChart) {
          punishmentChart.setOption({
            xAxis: {
              data: punishmentData.map(item => item.name)
            },
            series: [{
              data: punishmentData.map(item => item.value)
            }]
          })
        }
      }
      
      // 处理党纪处分结果数据
      if (res.disciplineResultStats && res.disciplineResultStats.length > 0) {
        // 清空原有数据
        partyPunishmentData.splice(0, partyPunishmentData.length)
        
        // 添加新数据
        res.disciplineResultStats.forEach(item => {
          if (item && typeof item.name === 'string' && (typeof item.amount === 'number' || !isNaN(Number(item.amount)))) {
            partyPunishmentData.push({
              name: item.name,
              value: Number(item.amount)
            })
          }
        })
        
        // 更新党纪处分结果图表
        if (partyPunishmentChart) {
          partyPunishmentChart.setOption({
            xAxis: {
              data: partyPunishmentData.map(item => item.name)
            },
            series: [{
              data: partyPunishmentData.map(item => item.value)
            }]
          })
        }
      }
    }
  } catch (error) {
    console.error('获取立案件统计信息失败:', error)
    // 错误情况下确保隐藏图表
    showCaseDeptSection.value = false
  }
}

// 添加以下监视器：
// 监听线索办理主体分布图表的显示状态
watch(
  showClueDeptSection,
  (newVal) => {
    if (newVal) {
      // 当容器显示时，延迟一下再重新初始化图表，确保DOM已经渲染
      setTimeout(() => {
        if (clueDeptChartRef.value) {
          // 如果图表存在，先销毁它
          if (clueDeptChart) {
            clueDeptChart.dispose();
          }
          
          // 重新初始化图表
          clueDeptChart = echarts.init(clueDeptChartRef.value);
          
          // 对数据进行排序
          const sortedData = [...clueDeptData].sort((a, b) => b.value - a.value);
          
          // 设置完整配置
          clueDeptChart.setOption({
            backgroundColor: 'transparent',
            title: {
              text: '线索办理主体分布',
              left: 'center',
              top: 0,
              textStyle: {
                color: '#00ffd8',
                fontSize: 18,
                fontWeight: 'normal',
                textShadow: '0 0 5px rgba(0, 255, 216, 0.5)'
              }
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              formatter: '{b}: {c}件',
              backgroundColor: 'rgba(10, 20, 30, 0.8)',
              borderColor: '#00c6ff',
              borderWidth: 1,
              textStyle: {
                color: '#fff'
              }
            },
            grid: {
              top: '15%',
              left: '3%',
              right: '4%',
              bottom: '15%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: sortedData.map(item => item.name),
              axisLabel: {
                color: '#ffffff',
                fontSize: 13,
                interval: 0,
                rotate: 45,
                width: 100,
                overflow: 'truncate',
                ellipsis: '...',
                fontWeight: 'normal',
                textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
              },
              axisLine: {
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.8)',
                  width: 2
                }
              },
              axisTick: {
                show: true,
                length: 5,
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.8)'
                }
              }
            },
            yAxis: {
              type: 'value',
              name: '线索数量',
              nameTextStyle: {
                color: '#ffffff',
                fontSize: 14,
                fontWeight: 'normal',
                padding: [0, 0, 0, 5],
                textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.8)',
                  width: 2
                }
              },
              axisLabel: {
                color: '#ffffff',
                fontSize: 13,
                fontWeight: 'normal',
                textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.2)'
                }
              }
            },
            series: [
              {
                name: '线索数量',
                type: 'bar',
                barWidth: sortedData.length <= 3 ? 30 : 18,
                itemStyle: {
                  normal: {
                    color: function(params) {
                      return deptColorGradients[params.dataIndex % deptColorGradients.length];
                    },
                    borderRadius: [4, 4, 0, 0]
                  }
                },
                data: sortedData.map(item => item.value),
                label: {
                  show: true,
                  position: 'top',
                  formatter: '{c}',
                  fontSize: 13,
                  fontWeight: 'normal',
                  color: '#ffffff',
                  textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 198, 255, 0.7)',
                    borderWidth: 1,
                    borderColor: '#ffffff'
                  },
                  label: {
                    fontSize: 14,
                    color: '#00ffd8'
                  }
                }
              }
            ]
          });
          
          // 手动触发resize以确保图表正确渲染
          setTimeout(() => {
            if (clueDeptChart) {
              clueDeptChart.resize();
            }
          }, 100);
        }
      }, 300);
    }
  }
)

// 监听立案件办理主体分布图表的显示状态
watch(
  showCaseDeptSection,
  (newVal) => {
    if (newVal) {
      // 当容器显示时，延迟一下再重新初始化图表，确保DOM已经渲染
      setTimeout(() => {
        if (caseDeptChartRef.value) {
          console.log('立案件办理主体图表显示，准备初始化图表')
          // 如果图表存在，先销毁它
          if (caseDeptChart) {
            caseDeptChart.dispose();
          }
          
          // 对数据进行排序
          const sortedData = [...caseDeptData].sort((a, b) => b.value - a.value);
          console.log('立案件办理主体排序后数据:', sortedData)
          
          // 重新初始化图表
          caseDeptChart = echarts.init(caseDeptChartRef.value);
          
          // 设置完整配置
          const option = {
            backgroundColor: 'transparent',
            title: {
              text: '立案件办理主体分布',
              left: 'center',
              top: 0,
              textStyle: {
                color: '#00ffd8',
                fontSize: 18,
                fontWeight: 'normal',
                textShadow: '0 0 5px rgba(0, 255, 216, 0.5)'
              }
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              formatter: '{b}: {c}件',
              backgroundColor: 'rgba(10, 20, 30, 0.8)',
              borderColor: '#00c6ff',
              borderWidth: 1,
              textStyle: {
                color: '#fff'
              }
            },
            grid: {
              top: '15%',
              left: '3%',
              right: '4%',
              bottom: '15%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: sortedData.map(item => item.name),
              axisLabel: {
                color: '#ffffff',
                fontSize: 13,
                interval: 0,
                rotate: 45,
                width: 100,
                overflow: 'truncate',
                ellipsis: '...',
                fontWeight: 'normal',
                textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
              },
              axisLine: {
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.8)',
                  width: 2
                }
              },
              axisTick: {
                show: true,
                length: 5,
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.8)'
                }
              }
            },
            yAxis: {
              type: 'value',
              name: '案件数量',
              nameTextStyle: {
                color: '#ffffff',
                fontSize: 14,
                fontWeight: 'normal',
                padding: [0, 0, 0, 5],
                textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.8)',
                  width: 2
                }
              },
              axisLabel: {
                color: '#ffffff',
                fontSize: 13,
                fontWeight: 'normal',
                textShadow: '0 0 3px rgba(0, 0, 0, 0.5)'
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(0, 198, 255, 0.2)'
                }
              }
            },
            series: [
              {
                name: '案件数量',
                type: 'bar',
                barWidth: sortedData.length <= 3 ? 30 : 18,
                itemStyle: {
                  normal: {
                    color: function(params) {
                      return deptColorGradients[params.dataIndex % deptColorGradients.length];
                    },
                    borderRadius: [4, 4, 0, 0]
                  }
                },
                data: sortedData.map(item => item.value),
                label: {
                  show: true,
                  position: 'top',
                  formatter: '{c}',
                  fontSize: 13,
                  fontWeight: 'normal',
                  color: '#ffffff',
                  textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 198, 255, 0.7)',
                    borderWidth: 1,
                    borderColor: '#ffffff'
                  },
                  label: {
                    fontSize: 14,
                    color: '#00ffd8'
                  }
                }
              }
            ]
          };
          
          console.log('初始化立案件办理主体图表并应用配置')
          caseDeptChart.setOption(option);
          
          // 手动触发resize以确保图表正确渲染
          setTimeout(() => {
            if (caseDeptChart) {
              console.log('立案件办理主体图表resize')
              caseDeptChart.resize();
            }
          }, 100);
        } else {
          console.error('立案件办理主体图表DOM元素不存在')
        }
      }, 300);
    }
  }
)

// 监听已办线索数量图表的显示状态
watch(
  showClueCompletedSection,
  (newVal) => {
    if (newVal) {
      // 当容器显示时，延迟一下再重新初始化图表，确保DOM已经渲染
      setTimeout(() => {
        if (clueCompletedChartRef.value) {
          // 如果图表存在，先销毁它
          if (clueCompletedChart) {
            clueCompletedChart.dispose();
          }
          
          // 重新初始化图表
          clueCompletedChart = echarts.init(clueCompletedChartRef.value);
          
          // 计算总数
          const totalCompleted = clueCompletedData[0].value + clueCompletedData[1].value;
          
          // 处理数据全为0的情况
          const effectiveData = [
            {
              name: '按期办结',
              value: clueCompletedData[0].value || 0.001, // 使用微小值确保饼图能正常渲染
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#00ff8f' },
                  { offset: 1, color: '#01cc72' }
                ])
              }
            },
            {
              name: '超时办结',
              value: clueCompletedData[1].value || 0.001, // 使用微小值确保饼图能正常渲染
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#ff5252' },
                  { offset: 1, color: '#ff9e3d' }
                ])
              }
            }
          ];
          
          // 设置完整配置
          clueCompletedChart.setOption({
            backgroundColor: 'transparent',
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)',
              backgroundColor: 'rgba(10, 20, 30, 0.8)',
              borderColor: '#00c6ff',
              borderWidth: 1,
              textStyle: {
                color: '#fff'
              }
            },
            legend: {
              orient: 'vertical',
              right: 10,
              top: 'center',
              textStyle: {
                color: '#00c6ff'
              },
              data: clueCompletedData.map(item => item.name)
            },
            title: {
              text: `已办线索总数: ${totalCompleted}件`,
              left: 'center',
              top: 10,
              textStyle: {
                color: '#00ffd8',
                fontSize: 16,
                fontWeight: 'bold'
              }
            },
            series: [
              {
                name: '已办线索',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['40%', '55%'],
                itemStyle: {
                  borderRadius: 10,
                  borderColor: 'rgba(0, 198, 255, 0.2)',
                  borderWidth: 2
                },
                label: {
                  show: true,
                  position: 'outside',
                  formatter: function(params) {
                    // 获取原始数据用于正确计算百分比
                    const originalTotal = clueCompletedOriginalData[0].value + clueCompletedOriginalData[1].value;
                    let originalValue, correctPercent;
                    
                    if (params.name === '按期办结') {
                      originalValue = clueCompletedOriginalData[0].value;
                    } else {
                      originalValue = clueCompletedOriginalData[1].value;
                    }
                    
                    // 计算正确的百分比
                    if (originalTotal > 0) {
                      correctPercent = ((originalValue / originalTotal) * 100).toFixed(1);
                    } else {
                      correctPercent = 0;
                    }
                    
                    // 显示原始值和正确的百分比
                    return `${params.name}: ${originalValue} (${correctPercent}%)`;
                  },
                  fontSize: 14,
                  color: '#00c6ff',
                  textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
                },
                emphasis: {
                  label: {
                    fontSize: 16,
                    color: '#00ffd8',
                    fontWeight: 'bold'
                  },
                  itemStyle: {
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 198, 255, 0.5)',
                    borderWidth: 3,
                    borderColor: '#00ffd8'
                  }
                },
                data: effectiveData
              }
            ]
          });
          
          // 手动触发resize以确保图表正确渲染
          setTimeout(() => {
            if (clueCompletedChart) {
              clueCompletedChart.resize();
            }
          }, 100);
        }
      }, 300);
    }
  }
)

// 初始化已办线索数量图表
const initClueCompletedChart = () => {
  if (!clueCompletedChartRef.value) return
  clueCompletedChart = echarts.init(clueCompletedChartRef.value)

  // 处理数据全为0的情况
  const effectiveData = [
    {
      name: '按期办结',
      value: clueCompletedData[0].value || 0.001, // 使用微小值确保饼图能正常渲染
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#00ff8f' },
          { offset: 1, color: '#01cc72' }
        ])
      }
    },
    {
      name: '超时办结',
      value: clueCompletedData[1].value || 0.001, // 使用微小值确保饼图能正常渲染
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#ff5252' },
          { offset: 1, color: '#ff9e3d' }
        ])
      }
    }
  ];

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 30, 0.8)',
      borderColor: '#00c6ff',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#00c6ff'
      },
      data: clueCompletedData.map(item => item.name)
    },
    series: [
      {
        name: '已办线索',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '55%'],
        itemStyle: {
          borderRadius: 10,
          borderColor: 'rgba(0, 198, 255, 0.2)',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: function(params) {
            // 获取原始数据用于正确计算百分比
            const originalTotal = clueCompletedOriginalData[0].value + clueCompletedOriginalData[1].value;
            let originalValue, correctPercent;
            
            if (params.name === '按期办结') {
              originalValue = clueCompletedOriginalData[0].value;
            } else {
              originalValue = clueCompletedOriginalData[1].value;
            }
            
            // 计算正确的百分比
            if (originalTotal > 0) {
              correctPercent = ((originalValue / originalTotal) * 100).toFixed(1);
            } else {
              correctPercent = 0;
            }
            
            // 显示原始值和正确的百分比
            return `${params.name}: ${originalValue} (${correctPercent}%)`;
          },
          fontSize: 14,
          color: '#00c6ff',
          textShadow: '0 0 3px rgba(0, 0, 0, 0.7)'
        },
        emphasis: {
          label: {
            fontSize: 16,
            color: '#00ffd8',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 198, 255, 0.5)',
            borderWidth: 3,
            borderColor: '#00ffd8'
          }
        },
        data: effectiveData
      }
    ]
  }

  clueCompletedChart.setOption(option)
}

// 定义一个处理线索办理主体分布数据的辅助函数
// 这样可以避免在全局作用域中使用res变量
const handleClueDeptData = (dataResponse) => {
  if (dataResponse && dataResponse.depthStats && dataResponse.depthStats.length > 0) {
    console.log('更新线索办理主体分布数据:', dataResponse.depthStats)
    
    // 清空原有数据
    clueDeptData.splice(0, clueDeptData.length)
    
    // 添加新数据
    dataResponse.depthStats.forEach(item => {
      if (item && typeof item.name === 'string' && (typeof item.amount === 'number' || !isNaN(Number(item.amount)))) {
        clueDeptData.push({
          name: item.name,
          value: Number(item.amount)
        })
      }
    })
    
    // 对数据进行排序（按值降序）
    const sortedData = [...clueDeptData].sort((a, b) => b.value - a.value);
    
    // 更新线索办理主体分布图表
    if (clueDeptChart) {
      clueDeptChart.setOption({
        xAxis: {
          data: sortedData.map(item => item.name)
        },
        series: [{
          data: sortedData.map((item, index) => ({
            name: item.name,
            value: item.value,
            itemStyle: {
              color: deptColorGradients[index % deptColorGradients.length]
            }
          }))
        }]
      })
    }
    
    // 显示线索办理主体分布图表
    showClueDeptSection.value = true
  } else {
    // 如果数据为空或不存在，隐藏图表
    showClueDeptSection.value = false
  }
}

// 在fetchBpmClueStatistics函数内部调用这个辅助函数，传入res
// 修改fetchBpmClueStatistics函数,在处理线索办理主体分布数据部分
// 将原代码替换为调用这个辅助函数
// fetchBpmClueStatistics函数的代码不需要在这里修改，只需要在函数内部
// 找到处理res.depthStats部分，替换为handleClueDeptData(res)
</script>

<style scoped>
.report-methods-tech-container {
  margin-top: 20px;
  background: linear-gradient(135deg, #0a1a2a, #0e2a38);
  padding: 20px;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.report-methods-tech-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="none" stroke="%2300c6ff20" stroke-width="0.5"/></svg>');
  opacity: 0.1;
  pointer-events: none;
}

.section-title {
  font-size: 20px;
  font-weight: normal;
  color: #00ffd8;
  padding: 10px 20px;
  background: rgba(0, 188, 212, 0.15);
  border-left: 5px solid #00bcd4;
  margin-bottom: 15px;
  border-radius: 0 4px 4px 0;
  text-shadow: 0 0 6px rgba(0, 255, 216, 0.5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 188, 212, 0.5), transparent);
}

.tech-card {
  margin-bottom: 20px;
  border-radius: 8px;
  background: rgba(14, 30, 48, 0.9);
  border: 1px solid rgba(0, 198, 255, 0.3);
  color: #00c6ff;
  box-shadow: 0 0 20px rgba(0, 198, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.tech-card:hover {
  box-shadow: 0 0 30px rgba(0, 198, 255, 0.4);
  transform: translateY(-5px);
  border-color: rgba(0, 255, 216, 0.5);
}

.tech-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: normal;
  font-size: 18px;
  color: #00ffd8;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  padding: 8px 18px;
  background: rgba(0, 198, 255, 0.15);
  border-radius: 4px;
  border-left: 3px solid #00ffd8;
  text-shadow: 0 0 5px rgba(0, 255, 216, 0.5);
}

.chart-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart {
  width: 100%;
  height: 300px;
}

.tech-corner {
  position: absolute;
  width: 10px;
  height: 10px;
  border-color: #00c6ff;
}

.top-left {
  top: 0;
  left: 0;
  border-top: 2px solid;
  border-left: 2px solid;
}

.top-right {
  top: 0;
  right: 0;
  border-top: 2px solid;
  border-right: 2px solid;
}

.bottom-left {
  bottom: 0;
  left: 0;
  border-bottom: 2px solid;
  border-left: 2px solid;
}

.bottom-right {
  bottom: 0;
  right: 0;
  border-bottom: 2px solid;
  border-right: 2px solid;
}

.view-dashboard-btn {
  position: relative;
  float: right;
  margin-right: 10px;
  background: linear-gradient(135deg, #0288d1, #26c6da);
  border: none;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  color: #ffffff;
  font-weight: 500;
  font-size: 14px;
  padding: 6px 12px;
  height: auto;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.view-dashboard-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  transition: left 0.7s ease;
}

.view-dashboard-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(38, 198, 218, 0.5);
  background: linear-gradient(135deg, #039be5, #00acc1);
}

.view-dashboard-btn:hover::before {
  left: 100%;
}

.view-dashboard-btn .el-icon {
  margin-left: 6px;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.view-dashboard-btn:hover .el-icon {
  transform: translateX(3px);
}
</style> 
