<template>


  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="表单名" prop="formName">
        <el-input
          v-model="queryParams.formName"
          class="!w-240px"
          clearable
          placeholder="请输入表单名"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标题" prop="formTitle">
        <el-input
          v-model="queryParams.formTitle"
          class="!w-240px"
          clearable
          placeholder="请输入标题"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery"  v-hasPermi="['bpm:form-tem:query']">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>

      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="编号" prop="id" />
      <el-table-column align="center" label="标题" prop="formTitle" />
      <el-table-column align="center" label="状态" prop="status">
        <template #default="scope">
          <dict-tag type="bpmFormTempStatuses" :value="scope.row.status" />
        </template>
      </el-table-column>

      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="创建时间"
        prop="createTime"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="更新时间"
        prop="updateTime"
      />
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <!--<el-button v-hasPermi="['bpm:form:create']" link type="primary" @click="createForm(scope.row.id)">
            新增
          </el-button>-->
          <el-button
            v-hasPermi="['bpm:form-tem:create','bpm:form-tem:update']"
            link
            type="primary"
            @click="gotoProcessInstanceCreate(scope.row)"
          >
            编辑
          </el-button>
          <el-button v-hasPermi="['bpm:form-tem:publish']" link
                     @click="publishProcessInstance(scope.row)">
            发布
          </el-button>
         <el-button
           v-hasPermi="['bpm:form-tem:delete']"
           link
          type="danger"
          @click="handleDelete(scope.row.id)"
        >
           删除
         </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>


</template>

<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import * as FormApi from '@/api/bpm/form'
import { setConfAndFields2 } from '@/utils/formCreate'
import type { ApiAttrs } from '@form-create/element-ui/types/config'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { formTempApi} from "@/api/bpm/form-temp";
import { categoryRoute } from "@/api/bpm/category";

defineOptions({ name: 'FormTemCreate' })

const route = useRoute()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { currentRoute, push } = useRouter() // 路由
const fApi = ref<ApiAttrs>()
const { delView } = useTagsViewStore() // 视图操作
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const category = ref("")
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  processCategoryCode:"",
  formName:"",
  formTitle:"",
  updater:"",
  updateTime:""
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.processCategoryCode = category;
    const data = await formTempApi.getFormTemList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery =  async() => {
  queryParams.pageNo = 1
  await getList(category.value)
}

/** 重置按钮操作 */
const resetQuery =  async() => {
  queryFormRef.value.resetFields()
  await handleQuery()
}

/** 添加/修改操作 */
/*const openForm = (id?: number) => {
  const toRouter: { name: string; query?: { id: number } } = {
    name: 'BpmFormEditor'
  }
  // 表单新建的时候id传的是event需要排除
  if (typeof id === 'number') {
    toRouter.query = {
      id
    }
  }
  push(toRouter)
}*/
const getCategory= async () => {
  category.value = await  categoryRoute.getCategoryByRouteParam(route)
}
const gotoProcessInstanceCreate = async (row)=>{
  let routeName = "";
  if (category.value ==="oa_xf"){
    routeName = "BpmProcessInstanceCreateByXF";
  }
  if (category.value ==="oa_xs"){
    routeName = "BpmProcessInstanceCreateByXS";
  }
  if (category.value ==="oa_news"){
    routeName = "BpmProcessInstanceCreateByNews";
  }
  if (category.value ==="oa_task"){
    routeName = "BpmProcessInstanceCreateByTask";
  }
  if (category.value ==="oa_tr"){
    routeName = "BpmProcessInstanceCreateByTr";
  }
  if (category.value ==="oa_register"){
    routeName = "BpmProcessInstanceCreateByRegister";
  }
  await push({
    name:routeName,
    query:{
      formTempId:row.id,
      processDefinitionId:row.processDefinitionId,
      category:row.processCategoryCode
    }
  })
}

const publishProcessInstance = (row)=>{

}

/** 删除按钮操作 */
 const handleDelete = async (id: number) => {
   try {
     // 删除的二次确认
     await message.delConfirm()
     // 发起删除
     await formTempApi.deleteFormTem(id)
     message.success(t('common.delSuccess'))
     // 刷新列表
     await getList(category.value)
   } catch {}
 }

/** 详情操作 */
/*
const detailVisible = ref(false)
const detailData = ref({
  rule: [],
  option: {}
})
const openDetail = async (rowId: number) => {
  // 设置表单
  const data = await FormApi.getForm(rowId)
  setConfAndFields2(detailData, data.conf, data.fields)
  // 弹窗打开
  detailVisible.value = true
}
*/

/*const detailForm = ref({
  rule: [],
  option: {},
  value: {}
}) // 流程表单详情
const createForm = async (rowId: number) => {
  // 设置表单
  const data = await FormApi.getForm(rowId)
  setConfAndFields2(detailForm, data.conf, data.fields)
  // 弹窗打开
  detailVisible.value = true
}

/!** 提交按钮 *!/
const submitForm = async (formData) => {
  console.log(formData);
  try {
    // 提交请求
    await ProcessInstanceApi.saveProcessInstanceForm({
      //在表单提交时，需要指定表单的id，这个id是表单的id，不是表单模板的id
      formId:'',
      variables: formData,
    })
    // 提示
    message.success('表单保存成功')
    // 跳转回去
    delView(unref(currentRoute))
    await push({
      name: 'BpmFormTem'
    })
  } catch (error) {
    message.error('表单提交失败')
  }
}*/

/** 初始化 **/
onMounted(() => {
  /*
* 获取路由上的参数
* **/
//  console.info('this.route', JSON.stringify(route));
  getCategory().then(() => {
    if(!category.value){
      message.error("缺少访问参数！")
      return
    }
    getList(category.value)
  })
  //getList(category.value)
})
</script>
