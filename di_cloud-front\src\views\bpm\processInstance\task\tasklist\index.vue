<template>
  <ContentWrap>
    <!-- 主任务列表 -->
    <TaskListMain ref="taskListMainRef" @show-feedback-details="navigateToFeedbackDetails" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import type { TaskData } from './types'

// 导入拆分的组件
import TaskListMain from './components/TaskListMain.vue'

defineOptions({ name: 'BpmTaskList' })

// 路由和消息工具
const router = useRouter()
const message = useMessage()

// 组件引用
const taskListMainRef = ref()

/** 跳转到反馈详情页面 */
const navigateToFeedbackDetails = (row) => {
  // 将需要传递的数据作为查询参数
  router.push({
    name: 'BpmTaskFeedbackDetail', 
    params: { id: row.id },
    query: {
      taskName: row.taskName,
      taskType: row.taskType,
      taskTypeLabel: row.taskTypeLabel,
      completionRate: row.completionRate,
      feedbackCount: row.feedbackCount,
      totalCount: row.totalCount,
      createTime: row.createTime,
      dueTime: row.dueTime,
      receiverName: row.receiverName,
      description: row.description
    }
  })
}
</script>

<style scoped>
/* 保留必要的样式 */
.el-progress {
  margin-right: 0;
  margin-bottom: 5px;
}

/* 任务反馈表格样式 */
.task-feedback-dialog :deep(.completed-row) {
  background-color: #f0f9eb;
}

.task-feedback-dialog :deep(.returned-row) {
  background-color: #fdf6ec;
}

.task-feedback-dialog :deep(.urged-row) {
  background-color: #fef0f0;
}

.task-feedback-dialog :deep(.status-tag) {
  min-width: 60px;
}
</style>
