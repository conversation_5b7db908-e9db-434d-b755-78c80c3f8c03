<template>
  <ContentWrap>
    <div class="feedback-detail-page">
      <div class="bg-blue-50 p-4 rounded-md mb-4 flex justify-between items-center">
        <div>
          <h3 class="text-xl font-bold text-blue-700 mb-2">{{ currentTask?.taskName }}</h3>
          <div class="text-sm text-gray-500">
            <el-tag :type="currentTask?.taskType === '1' ? 'success' : currentTask?.taskType === '2' ? 'warning' : 'info'" class="mr-2">
              {{ currentTask?.taskTypeLabel || (currentTask?.taskType === '1' ? '会议通知' : currentTask?.taskType === '2' ? '工作提示' : '其他事项') }}
            </el-tag>
            <span>共计 {{ feedbackList.length }} 个单位</span>
          </div>
        </div>
        <div class="text-right">
          <div class="text-lg font-bold mb-1">
            <span class="text-success">{{ currentTask?.feedbackCount || 0 }}</span>
            <span class="text-gray-400 mx-1">/</span>
            <span class="text-gray-600">{{ currentTask?.totalCount || 0 }}</span>
          </div>
          <div class="text-sm text-gray-500">已完成数 / 总数</div>
        </div>
      </div>
      
      <div class="mb-4 flex justify-between items-center">
        <div>
          <el-button @click="goBack" plain>
            <Icon icon="ep:arrow-left" class="mr-1" /> 返回
          </el-button>
        </div>
        <div>
          <el-button 
            v-if="currentTask?.taskType === '1'" 
            type="primary" 
            class="mr-2" 
            @click="openAttendeesSummary"
          >
            <Icon icon="ep:user-filled" class="mr-1" /> 参会人员信息汇总
          </el-button>
          <el-button type="warning" @click="batchUrgeConfirm" :disabled="!hasUncompletedTasks">
            <Icon icon="ep:alarm-clock" class="mr-1" /> 催办
          </el-button>
        </div>
      </div>
      
      <el-table 
        :data="feedbackList" 
        border 
        stripe 
        size="large"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="催办标记" width="120" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.processInstanceUrgingNum > 0" type="danger" size="small">
              已催办 {{ scope.row.processInstanceUrgingNum }} 次
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="工作任务名称" prop="taskName" min-width="180" align="center" show-overflow-tooltip />
        <el-table-column label="工作项目" align="center" width="100">
          <template #default="scope">
            <el-tag 
              :type="getTaskProjectType(scope.row.taskType)"
              :effect="scope.row.taskType === '6' ? 'plain' : 'dark'"
              size="small"
            >
              {{ scope.row.taskType === '1' ? '会议通知' : scope.row.taskType === '2' ? '工作提示' : scope.row.taskType === '3' ? '专项工作' : scope.row.taskType === '4' ? '队伍建设' : scope.row.taskType === '5' ? '日常报表' : '其他事项' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          label="下发日期" 
          prop="sendTime" 
          width="150" 
          align="center"
          :formatter="dateFormatter2" 
        />
        <el-table-column label="接收单位/承办人员" prop="receiverName" min-width="150" align="center" show-overflow-tooltip>
          <template #default="scope">
            <div class="font-bold">{{ scope.row.receiverName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="当前处理人" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <!-- 如果任务状态是审核通过，显示'-' -->
            <span v-if="scope.row.processInstanceState === true">-</span>
            <!-- 其他情况显示处理人姓名 -->
            <span v-else-if="scope.row.tasks && scope.row.tasks.length > 0">
              {{ scope.row.tasks[0].assigneeName || '暂无处理人' }}
            </span>
            <span v-else>暂无处理人</span>
          </template>
        </el-table-column>
        <el-table-column 
          label="办理时限" 
          prop="dueTime" 
          width="150" 
          align="center"
          :formatter="dateFormatter" 
        />
        <el-table-column label="任务状态" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="getTaskStatusType(scope.row)"
              effect="dark"
              size="small"
              class="status-tag"
            >
              {{ getTaskStatusLabel(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="流程状态" width="100" align="center" v-if="false">
          <template #default="scope">
            <el-tag 
              :type="scope.row.processInstanceState ? 'success' : 'info'"
              effect="plain"
              size="small"
            >
              {{ scope.row.processInstanceState ? '已结束' : '进行中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="任务明细" width="100" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="viewDetail(scope.row)">
              查看明细
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button 
              v-if="!scope.row.processInstanceState"
              link 
              type="primary" 
              @click="handleReassign(scope.row)"
            >
              重新分配
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="mt-4 flex justify-between items-center">
        <div class="text-sm text-gray-500">
          共 {{ feedbackList.length }} 条记录，已反馈 {{ feedbackList.filter(t => t.processInstanceState === true).length }} 条，
          未反馈 {{ feedbackList.filter(t => t.processInstanceState === false).length }} 条
        </div>
      </div>
    </div>
    
    <!-- 催办确认对话框 -->
    <el-dialog
      v-model="urgeDialogVisible"
      title="催办确认"
      width="600px"
      :destroy-on-close="true"
    >
      <div class="p-4">
        <div class="flex items-center mb-4 text-red-500">
          <Icon icon="ep:warning" class="mr-2 text-2xl" />
          <span class="font-bold">确认发送催办通知</span>
        </div>
        
        <p class="mb-4">请选择需要催办的单位，催办记录将会影响单位考核，请确认：</p>
        
        <el-table
          ref="multipleTableRef"
          :data="pendingTasksForUrge"
          style="width: 100%"
          @selection-change="handleUrgeSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="单位名称" prop="receiverName" />
          <el-table-column label="任务状态" width="120">
            <template #default="scope">
              <el-tag 
                :type="getTaskStatusType(scope.row)"
                size="small"
              >
                {{ getTaskStatusLabel(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="催办次数" width="120">
            <template #default="scope">
              <span>{{ scope.row.processInstanceUrgingNum > 0 ? scope.row.processInstanceUrgingNum : 0 }} 次</span>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="mt-4 flex justify-between">
          <el-button size="small" @click="selectAllPending">选择所有未反馈</el-button>
          <div>
            <span class="text-sm text-orange-500">已选择 {{ urgeSelectedTasks.length }} 个单位</span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="urgeDialogVisible = false">取消</el-button>
          <el-button type="warning" @click="confirmUrge" :disabled="urgeSelectedTasks.length === 0">
            确认催办
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重新分配对话框 -->
    <el-dialog
      v-model="reassignDialogVisible"
      title="重新分配"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="mb-4">
        <div class="text-base text-gray-600 mb-2">
          当前任务：{{ currentReassignTask?.taskName }}
        </div>
        <div class="text-base text-gray-600 mb-2">
          当前处理人：{{ getCurrentAssigneeName() }}
        </div>
        <div class="text-base text-gray-600 mb-4">
          当前接收单位：{{ currentReassignTask?.receiverName }}
        </div>
        
        <el-form :model="userQueryParams" :inline="true" label-width="100px">
          <el-form-item label="组织名称">
            <el-select
              v-model="selectedDeptId"
              placeholder="请选择组织"
              clearable
              style="width: 300px"
              @change="handleDeptChange"
            >
              <el-option
                v-for="dept in deptList"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getUserList" :disabled="!selectedDeptId">
              <Icon icon="ep:search" class="mr-5px" /> 搜索
            </el-button>
            <el-button @click="resetUserQuery">
              <Icon icon="ep:refresh" class="mr-5px" /> 重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table
        v-loading="userLoading"
        :data="userList"
        max-height="400"
      >
        <el-table-column width="55" align="center">
          <template #default="scope">
            <el-radio 
              v-model="selectedUserId" 
              :label="scope.row.id"
              @change="handleUserRadioChange(scope.row)"
            >
              &nbsp;
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="账号" prop="username" width="120" />
        <el-table-column label="用户名" prop="nickname" width="120" />
        <el-table-column label="部门名称" prop="deptName" width="150" />
<!--        <el-table-column label="邮箱" prop="email" width="180" />-->
        <el-table-column label="手机号" prop="mobile" width="120" />
        <el-table-column label="状态" prop="status" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
              {{ scope.row.status === 0 ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 用户分页 -->
      <div class="mt-4">
        <Pagination
          :total="userTotal"
          v-model:page="userQueryParams.pageNo"
          v-model:limit="userQueryParams.pageSize"
          @pagination="getUserList"
        />
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reassignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReassign" :disabled="!selectedUser">
            确定分配
          </el-button>
        </div>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watch, onMounted, reactive } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { dateFormatter } from '@/utils/formatTime'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { urgeProcessInstances, getTaskEngineListByTaskEngineId, taskReassign } from '@/api/bpm/processInstance'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { useRoute, useRouter } from 'vue-router'
import type { TaskData, TaskFeedback } from '@/views/bpm/processInstance/task/tasklist/types'

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 当前任务数据
const currentTask = ref<TaskData>({} as TaskData)
const feedbackList = ref<TaskFeedback[]>([])
const taskEngineId = computed(() => route.params.id as string)

// 催办相关数据
const urgeDialogVisible = ref(false)
const urgeSelectedTasks = ref<TaskFeedback[]>([])
const pendingTasksForUrge = ref<TaskFeedback[]>([])
const multipleTableRef = ref()

// 重新分配相关数据
const reassignDialogVisible = ref(false)
const userLoading = ref(false)
const userTotal = ref(0)
const userList = ref<any[]>([])
const selectedUsers = ref([])
const selectedUserId = ref<number | undefined>(undefined)
const currentReassignTask = ref<TaskFeedback | null>(null)
const userQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  deptId: undefined as string | undefined
})

// 部门列表
const deptList = ref<any[]>([])
const selectedDeptId = ref<number | undefined>(undefined)
const selectedUser = computed(() => {
  return userList.value.find(user => user.id === selectedUserId.value)
})

// 计算是否有未完成的任务，用于催办按钮禁用状态
const hasUncompletedTasks = computed(() => {
  return feedbackList.value.some(item => 
    // 未反馈(0)或已退回(2)的任务都可以催办
    item.processInstanceTaskState === 0 || item.processInstanceTaskState === 2
  )
})

// 加载反馈列表数据
const loadFeedbackList = async () => {
  try {
    const response = await getTaskEngineListByTaskEngineId({
      taskEngineId: taskEngineId.value
    })
    
    if (!response || response.length === 0) {
      message.warning('未获取到任务反馈列表数据')
      feedbackList.value = []
    } else {
      feedbackList.value = response.map(item => ({
        ...item,
        taskName: item.name || currentTask.value.taskName,
        taskType: currentTask.value.taskType,
        taskTypeLabel: currentTask.value.taskTypeLabel,
        sendTime: item.taskEngineReleaseDate || currentTask.value.createTime,
        dueTime: item.taskEngineTimeLimit || currentTask.value.dueTime,
        receiverName: item.deptName,
        description: currentTask.value.description
      }))
    }
  } catch (error) {
    console.error('获取任务反馈详情失败:', error)
    message.error('获取任务反馈详情失败')
    feedbackList.value = []
  }
}

// 加载任务详情数据
const loadTaskDetail = async () => {
  try {
    // 从路由参数获取任务数据
    currentTask.value = {
      id: taskEngineId.value,
      taskName: route.query.taskName as string || '任务详情',
      taskType: route.query.taskType as string || '',
      taskTypeLabel: route.query.taskTypeLabel as string || '',
      completionRate: Number(route.query.completionRate) || 0,
      feedbackCount: Number(route.query.feedbackCount) || 0,
      totalCount: Number(route.query.totalCount) || 0,
      createTime: route.query.createTime as string || '',
      dueTime: route.query.dueTime as string || '',
      receiverName: route.query.receiverName as string || '',
      description: route.query.description as string || ''
    }
  } catch (error) {
    console.error('加载任务详情失败', error)
    message.error('加载任务详情失败')
  }
}

// 获取任务项目类型
const getTaskProjectType = (taskType: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  switch (taskType) {
    case '1': return 'warning'  // 会议通知
    case '2': return 'info'     // 工作提示
    case '3': return 'primary'  // 专项工作
    case '4': return 'success'  // 队伍建设
    case '5': return 'info'     // 日常报表
    default: return 'info'      // 其他事项
  }
}

// 获取任务状态标签
const getTaskStatusLabel = (row) => {
  // 先检查流程是否完成
  if (row?.processInstanceState === true) {
    return '审核通过'  // 这个特殊状态保留硬编码
  }
  
  // 使用字典数据获取标签
  return getDictLabel('system_task_status', row?.processInstanceTaskState) || '未知状态'
}

// 获取任务状态类型
const getTaskStatusType = (row) => {
  // 先检查流程是否完成，再检查任务状态
  if (row.processInstanceState === true) {
    return 'success' // 审核通过
  }
  
  // 根据任务状态返回对应的类型
  switch (row.processInstanceTaskState) {
    case 0: return 'danger'  // 未反馈
    case 1: return 'primary' // 已反馈
    case 2: return 'warning' // 已退回
    default: return 'info'   // 其他状态
  }
}

// 查看任务明细
const viewDetail = (row) => {
  router.push({
    name: 'BpmTaskDetail',
    params: { id: row.id },
    query: {
      taskName: row.taskName,
      taskType: row.taskType,
      taskTypeLabel: row.taskTypeLabel,
      processInstanceId: row.processInstanceId,
      dueTime: row.dueTime,
      receiverName: row.receiverName,
      description: row.description,
      processInstanceUrgingNum: row.processInstanceUrgingNum,
      groupAttachments: row.groupAttachments
    }
  })
}

// 重新分配按钮操作
const handleReassign = (row) => {
  currentReassignTask.value = row
  reassignDialogVisible.value = true
  // 重置用户查询参数
  userQueryParams.pageNo = 1
  userQueryParams.pageSize = 10
  
  selectedUserId.value = undefined // 清空选中的用户
  
  // 先获取部门列表，然后设置默认选中的部门
  getDeptList().then(() => {
    // 确保deptId是number类型
    const deptId = Number(row.deptId)
    selectedDeptId.value = deptId
    userQueryParams.deptId = String(deptId) // API参数仍需要字符串
    
    // 获取用户列表
    if (deptId) {
      getUserList()
    }
  })
}

// 获取部门列表
const getDeptList = async () => {
  try {
    const data = await DeptApi.getSimpleDeptList()
    deptList.value = data
  } catch (error) {
    console.error('获取部门列表失败:', error)
    message.error('获取部门列表失败')
  }
}

// 部门选择变化
const handleDeptChange = (value: number) => {
  selectedDeptId.value = value
  userQueryParams.pageNo = 1
  userQueryParams.deptId = String(value) // API参数需要字符串
  selectedUserId.value = undefined // 清空选中的用户
  getUserList()
}

// 获取用户列表
const getUserList = async () => {
  userLoading.value = true
  try {
    const params: any = {
      pageNo: userQueryParams.pageNo,
      pageSize: userQueryParams.pageSize
    }
    // 如果有选中的部门，添加deptId参数
    if (selectedDeptId.value) {
      params.deptId = String(selectedDeptId.value)
    }
    
    const data = await UserApi.getUserPage(params)
    userList.value = data.list
    userTotal.value = data.total
  } finally {
    userLoading.value = false
  }
}

// 重置用户查询
const resetUserQuery = () => {
  // 重置为当前任务的部门ID
  if (currentReassignTask.value?.deptId) {
    const deptId = Number(currentReassignTask.value.deptId)
    selectedDeptId.value = deptId
    userQueryParams.deptId = String(deptId) // API参数需要字符串
  } else {
    selectedDeptId.value = undefined
    userQueryParams.deptId = undefined
  }
  
  userQueryParams.pageNo = 1
  selectedUserId.value = undefined // 清空选中的用户
  
  // 如果有选中的部门，则获取用户列表
  if (selectedDeptId.value) {
    getUserList()
  }
}

// 用户选择变化
const handleUserSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 用户单选变化
const handleUserRadioChange = (user: any) => {
  selectedUserId.value = user.id
}

// 确认重新分配
const confirmReassign = async () => {
  if (!selectedUser.value) {
    message.warning('请选择要分配的用户')
    return
  }
  
  // 检查是否有有效的任务ID
  if (!currentReassignTask.value?.tasks || currentReassignTask.value.tasks.length === 0) {
    message.error('当前任务没有可分配的任务')
    return
  }
  
  try {
    // 调用重新分配API，使用tasks[0].id作为taskId
    await taskReassign(currentReassignTask.value.tasks[0].id, String(selectedUser.value.id))
    
    message.success('重新分配成功')
    reassignDialogVisible.value = false
    selectedUserId.value = undefined
    await loadFeedbackList()
  } catch (error) {
    message.error('重新分配失败')
  }
}

// 催办确认
const batchUrgeConfirm = () => {
  // 获取所有未反馈和已退回的任务
  const uncompletedTasks = feedbackList.value.filter(
    item => item.processInstanceTaskState === 0 || item.processInstanceTaskState === 2
  )
  
  if (uncompletedTasks.length === 0) {
    message.warning('没有需要催办的任务')
    return
  }
  
  // 设置催办对象并打开催办对话框
  pendingTasksForUrge.value = [...uncompletedTasks]
  urgeSelectedTasks.value = [] // 清空已选择的任务
  urgeDialogVisible.value = true
  
  // 延迟执行选择所有未反馈任务的操作
  nextTick(() => {
    selectAllPending()
  })
}

// 打开参会人员信息汇总页面
const openAttendeesSummary = () => {
  router.push({
    name: 'TaskAttendeesSummary',
    params: { 
      id: taskEngineId.value,
      processInstanceId: taskEngineId.value  // 在这个页面中，任务引擎ID等同于流程实例ID
    },
    query: {
      taskName: currentTask.value?.taskName,
      taskType: currentTask.value?.taskType,
      receiverName: '全部接收单位',  // 表示汇总所有接收单位的参会人员信息
      isAllUnits: 'true'  // 添加标记表示是所有单位的汇总
    }
  })
}

// 催办选择变更
const handleUrgeSelectionChange = (selection) => {
  urgeSelectedTasks.value = selection
}

// 选择所有未反馈任务
const selectAllPending = () => {
  if (!multipleTableRef.value) return
  
  const pendingTasks = pendingTasksForUrge.value.filter(
    task => task.processInstanceTaskState === 0 || task.processInstanceTaskState === 2
  )
  
  pendingTasks.forEach(task => {
    if (multipleTableRef.value) {
      multipleTableRef.value.toggleRowSelection(task, true)
    }
  })
}

// 确认催办
const confirmUrge = async () => {
  if (!urgeSelectedTasks.value || urgeSelectedTasks.value.length === 0) {
    message.warning('请至少选择一个单位进行催办')
    return
  }
  
  // 获取所有选中任务的流程实例ID
  const processInstanceIds = urgeSelectedTasks.value
    .filter(task => task && task.processInstanceId)
    .map(task => task.processInstanceId)
  
  if (processInstanceIds.length === 0) {
    message.warning('所选任务中没有有效的流程实例ID')
    return
  }
  
  try {
    // 调用催办API，将taskEngineId一起发送过去
    const response = await urgeProcessInstances({ 
      processInstanceIds, 
      taskEngineId: taskEngineId.value 
    })
    
    // 判断响应是否为 true
    if (response === true) {
      message.success(`已成功向 ${urgeSelectedTasks.value.length} 个单位发送催办通知`)
      urgeDialogVisible.value = false
      // 刷新数据
      loadFeedbackList()
    } else {
      message.error('催办失败')
    }
  } catch (error) {
    console.error('催办操作失败:', error)
    message.error('催办失败，请稍后重试')
  }
}

// 刷新列表
const refreshList = async () => {
  await loadFeedbackList()
  message.success('反馈列表已刷新')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取当前任务的处理人名称
const getCurrentAssigneeName = () => {
  // 首先尝试从路由参数获取assigneeName
  const assigneeFromRoute = route.query.assigneeName as string
  if (assigneeFromRoute) {
    return assigneeFromRoute
  }
  
  // 从当前重新分配任务的tasks数组中获取assigneeName
  if (currentReassignTask.value?.tasks && currentReassignTask.value.tasks.length > 0) {
    return currentReassignTask.value.tasks[0].assigneeName || '暂无处理人'
  }
  
  return '暂无处理人'
}

// 页面加载时获取数据
onMounted(async () => {
  await loadTaskDetail()
  await loadFeedbackList()
  await getDeptList() // 初始化部门列表
})
</script>

<style scoped>
/* 任务反馈表格样式 */
.feedback-detail-page :deep(.status-tag) {
  min-width: 60px;
}
</style> 
