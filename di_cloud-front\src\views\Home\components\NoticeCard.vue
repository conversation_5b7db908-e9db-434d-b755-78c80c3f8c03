<template>
  <el-card shadow="never" class="mt-8px">
    <template #header>
      <div class="h-3 flex justify-between">
        <span>{{ t('workplace.notice') }}</span>
        <el-link type="primary" :underline="false">{{ t('action.more') }}</el-link>
      </div>
    </template>
    <el-skeleton :loading="loading" animated>
      <div v-for="(item, index) in notice" :key="`dynamics-${index}`">
        <div class="flex items-center">
          <el-avatar :src="avatar" :size="35" class="mr-16px">
            <img src="@/assets/imgs/gmlogo.jpg" alt="" />
          </el-avatar>
          <div>
            <div class="text-14px">
              <Highlight :keys="item.keys.map((v) => t(v))">
                {{ item.type }} : {{ item.title }}
              </Highlight>
            </div>
            <div class="mt-16px text-12px text-gray-400">
              {{ formatTime(item.date, 'yyyy-MM-dd') }}
            </div>
          </div>
        </div>
        <el-divider />
      </div>
    </el-skeleton>
  </el-card>
</template>

<script lang="ts" setup>
import { formatTime } from '@/utils'
import type { Notice } from '../types'

defineProps({
  loading: {
    type: Boolean,
    required: true
  },
  notice: {
    type: Array as PropType<Notice[]>,
    required: true
  },
  avatar: {
    type: String,
    required: true
  }
})

const { t } = useI18n()
</script>

<style scoped>
</style> 