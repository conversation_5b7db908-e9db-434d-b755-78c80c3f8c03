<template>


  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入任务名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          end-placeholder="结束日期"
          start-placeholder="开始日期"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column align="center" label="流程" prop="processInstance.name" min-width="180"/>
      <el-table-column align="center" label="流程标题" min-width="200">
        <template #default="scope">
          {{ scope.row.processInstanceExt?.processInstanceTitle || '-' }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="当前任务" prop="name" width="180" />
      <!-- 新增办理时限列 -->
      <el-table-column
        label="办理时限"
        align="center"
        width="180"
        v-if="category === 'oa_xs'"
      >
        <template #default="scope">
          {{ scope.row.processInstance?.formVariables?.blsx || '-' }}
        </template>
      </el-table-column>
      <!-- 新增剩余时限列 -->
      <el-table-column
        label="剩余时限"
        align="center"
        width="180"
        v-if="category === 'oa_xs'"
      >
        <template #default="scope">
          <div v-if="scope.row.processInstance?.formVariables?.blsx" style="display: flex; align-items: center; justify-content: center;">
            <span :style="{ color: getTimeLeftColor(scope.row.processInstance.formVariables.blsx).color }">
              {{ getTimeLeftText(scope.row.processInstance.formVariables.blsx) }}
            </span>
            <div :style="{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              backgroundColor: getTimeLeftColor(scope.row.processInstance.formVariables.blsx).bgColor,
              marginLeft: '8px'
            }"></div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="发起人"
        prop="processInstance.startUser.nickname"
        width="100"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="发起时间"
        prop="createTime"
        width="180"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="任务时间"
        prop="createTime"
        width="180"
      />
      <!-- <el-table-column align="center" label="流程编号" prop="id" :show-overflow-tooltip="true" /> -->
      <!-- <el-table-column align="center" label="任务编号" prop="id" :show-overflow-tooltip="true" /> -->
      <el-table-column align="center" label="操作" fixed="right" width="160">
        <template #default="scope">
          <div class="button-group">
            <el-button link type="primary" @click="handleAudit(scope.row)">办理</el-button>

            <!-- 申请延期按钮，只在有办理时限的任务显示 -->
            <el-button 
              v-hasPermi="['bpm:process-instance:apply-delay']"
              v-if="scope.row.processInstance?.formVariables?.blsx" 
              link 
              type="primary" 
              @click="handleApplyDelay(scope.row)"
            >
              申请延期
            </el-button>
            <!-- 问题线索申请延期流程的查看父流程按钮 -->
            <el-button
              v-if="scope.row.processInstance?.processDefinitionId?.split(':')[0] === 'wtxssqyq_bpm'"
              link
              type="primary"
              @click="handleViewParentDetail(scope.row)"
            >
              查看上级流程
            </el-button>
            <!-- 查看历史申请按钮，只在线索件(oa_xs)类型的任务中显示 -->
            <el-button 
              v-if="scope.row.processInstance?.formVariables?.blsx" 
              link 
              type="primary" 
              @click="handleViewHistory(scope.row)"
            >
             延期历史记录
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
    
    <!-- 申请延期对话框 -->
    <TaskDelayForm ref="taskDelayFormRef" @success="getList(category.value)" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import {categoryRoute} from '@/api/bpm/category'
import TaskDelayForm from '@/views/bpm/processInstance/detail/dialog/TaskDelayForm.vue'
import { useUserStore } from '@/store/modules/user'
import { getUserProfile } from '@/api/system/user/profile'
defineOptions({ name: 'BpmTodoTask' })

const { push } = useRouter() // 路由
const route = useRoute()
const message = useMessage() // 消息弹窗
const userStore = useUserStore() // 用户 store
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const category = ref(null)// 流程分类
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  createTime: [],
  processCategoryCode:''
})
const queryFormRef = ref() // 搜索的表单
const taskDelayFormRef = ref() // 申请延期表单的 Ref

// 添加检查当前用户岗位的方法
// 判断当前用户是否具有特定岗位
const hasRequiredPost = ref(false) // 是否有所需岗位的标志

// 获取当前用户的岗位信息
const getCurrentUserPosts = async () => {
  try {
    // 通过查询个人信息获取当前用户岗位
    const userProfile = await getUserProfile()
    if (userProfile && userProfile.posts) {
      // 检查用户是否有"二级集团案管专员"或"项目经理"岗位
      const requiredPosts = ['二级集团案管专员', '项目经理']
      const userPostNames = userProfile.posts.map(post => post.name)
      
      // 检查用户的岗位是否包含任一所需岗位
      const hasPost = requiredPosts.some(postName => userPostNames.includes(postName))
      hasRequiredPost.value = hasPost
      
      console.log('当前用户岗位:', userPostNames, '是否有所需岗位:', hasRequiredPost.value)
    }
  } catch (error) {
    console.error('获取用户岗位信息失败:', error)
    hasRequiredPost.value = false
  }
}

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.processCategoryCode = category
    const data = await TaskApi.getTaskTodoPage(queryParams)
    list.value = data.list
    total.value = data.total
    
    // 处理流程数据
    if (list.value && list.value.length > 0) {
      list.value.forEach((task, index) => {
        // 处理流程标题 - 确保processInstanceExt对象存在
        if (!task.processInstanceExt) {
          task.processInstanceExt = {};
        }
        
        // 从旧的数据结构中获取流程标题（如果存在）
        if (task.processInstance?.processInstanceTitle && !task.processInstanceExt.processInstanceTitle) {
          task.processInstanceExt.processInstanceTitle = task.processInstance.processInstanceTitle;
          console.log(`设置任务[${task.id}]的流程标题: ${task.processInstanceExt.processInstanceTitle}`);
        }
        
        // 处理办理时限数据，使用processInstanceExt.processInstanceLimitDate字段
        if (task.processInstanceExt && 
            task.processInstanceExt.processInstanceLimitDate) {
          
          // 获取日期字符串，格式为YYYY-MM-DD
          const formattedDate = task.processInstanceExt.processInstanceLimitDate;
          
          // 将办理时限添加到formVariables中
          if (!task.processInstance.formVariables) {
            task.processInstance.formVariables = {};
          }
          task.processInstance.formVariables.blsx = formattedDate;
          console.log(`设置任务[${task.id}]的流程[${task.processInstance.id}]的办理时限: ${formattedDate}`);
        } else {
          console.log(`任务[${task.id}]的流程没有processInstanceExt.processInstanceLimitDate数据`);
        }
        
        // 如果是申请延期流程，获取流程的流程定义ID
        if (task.processInstance.processDefinition?.key) {
          task.processInstance.processDefinitionId = 
            `${task.processInstance.processDefinition.key}:${task.processInstance.processDefinition.version}:${task.processInstance.processDefinition.id}`;
        }
      });
      console.log("所有任务数据处理完成");
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList(category.value)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id,
      taskId: row.id
    }
  })
}

/** 申请延期操作 */
const handleApplyDelay = (row) => {
  if (row.processInstance && row.processInstance.id) {
    // 获取办理时限值
    const deadline = row.processInstance?.formVariables?.blsx || ''
    // 打开申请延期表单，传递任务ID、流程实例ID和办理时限
    taskDelayFormRef.value?.open(row.id, row.processInstance.id, deadline)
    console.log('打开申请延期表单, 任务ID:', row.id, '流程实例ID:', row.processInstance.id, '办理时限:', deadline)
  } else {
    message.warning('无法获取流程信息')
  }
}

/** 查看父流程详情操作 */
const handleViewParentDetail = async (row) => {
  try {
    if (!row.processInstance || !row.processInstance.id) {
      message.warning('无法获取流程信息')
      return
    }
    
    // 先调用API获取详细数据
    console.log("查看父流程详情，获取实例数据中...", row.processInstance.id)
    const processDetail = await ProcessInstanceApi.getProcessInstance(row.processInstance.id)
    
    // 从返回的数据中提取lcid字段
    const parentProcessId = processDetail.formVariables?.lcid
    if (!parentProcessId) {
      message.warning('无法获取父流程信息')
      return
    }
    
    console.log("获取到父流程ID:", parentProcessId)
    
    // 跳转到流程实例详情页面
    push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: parentProcessId,
        hideApproval: 'true'
      }
    })
  } catch (error) {
    console.error("获取流程详情失败:", error)
    message.error('获取流程详情失败')
  }
}

/** 查看历史申请记录 */
const handleViewHistory = (row) => {
  if (!row.processInstance || !row.processInstance.id) {
    message.warning('无法获取流程信息')
    return
  }
  
  console.log('查看历史申请记录', row.processInstance.id)
  
  // 跳转到历史申请列表页面
  push({
    name: 'BpmProcessInstanceHistory',
    query: {
      processInstanceId: row.processInstance.id
    }
  })
}

const getCategory= async () => {
  category.value = await  categoryRoute.getCategoryByRouteParam(route)
}

/** 获取剩余时限的文本 */
const getTimeLeftText = (deadline) => {
  if (!deadline) return '-'
  
  // 将办理时限转为Date对象
  const deadlineDate = new Date(deadline)
  // 获取当前日期
  const today = new Date()
  // 移除时间部分，仅保留日期
  today.setHours(0, 0, 0, 0)
  deadlineDate.setHours(0, 0, 0, 0)
  
  // 计算时间差（毫秒）
  const timeDiff = deadlineDate.getTime() - today.getTime()
  // 转换为天数
  const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
  
  if (daysLeft < 0) {
    return `超时${Math.abs(daysLeft)}天`
  } else {
    return `剩余${daysLeft}天`
  }
}

/** 获取剩余时限的颜色 */
const getTimeLeftColor = (deadline) => {
  if (!deadline) return { color: '#909399', bgColor: '#909399' } // 灰色
  
  // 将办理时限转为Date对象
  const deadlineDate = new Date(deadline)
  // 获取当前日期
  const today = new Date()
  // 移除时间部分，仅保留日期
  today.setHours(0, 0, 0, 0)
  deadlineDate.setHours(0, 0, 0, 0)
  
  // 计算时间差（毫秒）
  const timeDiff = deadlineDate.getTime() - today.getTime()
  // 转换为天数
  const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
  
  if (daysLeft < 0) {
    return { color: '#F56C6C', bgColor: '#F56C6C' } // 红色
  } else if (daysLeft <= 30) {
    return { color: '#E6A23C', bgColor: '#E6A23C' } // 黄色
  } else {
    return { color: '#67C23A', bgColor: '#67C23A' } // 绿色
  }
}

/** 初始化 **/
onMounted(() => {
  getCategory().then(() => {
    if(!category.value){
      message.error("缺少访问参数！")
      return
    }
    getList(category.value)
  })
  // 获取用户岗位信息
  // getCurrentUserPosts()
})
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 5px;
}
</style>
