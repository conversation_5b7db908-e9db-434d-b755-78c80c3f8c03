<template>

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <!-- <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="流程标题" prop="queryVariables.form_title">
        <el-input
          v-model="queryParams.queryVariables.form_title"
          placeholder="请输入流程标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="登记单位" prop="queryVariables.djdw">
        <el-input
          v-model="queryParams.queryVariables.djdw"
          placeholder="请输入登记单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->

      <!-- <el-table-column label="信访来源" prop="queryVariables.xfly" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.queryVariables.xfly" />
        </template>
      </el-table-column> -->
        <!-- <el-form-item v-if="queryParams.category== xf" label="信访编号" prop="queryVariables.xfbh">
          <el-input
            v-model="queryParams.queryVariables.xfbh"
            placeholder="请输入信访编号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="反映人姓名" prop="queryVariables.fyrxm">
          <el-input
            v-model="queryParams.queryVariables.fyrxm"
            placeholder="请输入反映人姓名"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="反映人单位" prop="queryVariables.fyrdw">
          <el-input
            v-model="queryParams.queryVariables.fyrdw"
            placeholder="请输入反映人单位"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="反映人职务" prop="queryVariables.fyrdw">
          <el-input
            v-model="queryParams.queryVariables.fyrdw"
            placeholder="请输入反映人职务"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="queryVariables.lxfs">
          <el-input
            v-model="queryParams.queryVariables.lxfs"
            placeholder="请输入联系方式"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="被反映人姓名" prop="queryVariables.bfyrxm">
          <el-input
            v-model="queryParams.queryVariables.bfyrxm"
            placeholder="请输入被反映人姓名"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="被反映人单位" prop="queryVariables.bfyrdw">
          <el-input
            v-model="queryParams.queryVariables.bfyrdw"
            placeholder="请输入被反映人单位"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="被反映人职务" prop="queryVariables.bfyrzw">
          <el-input
            v-model="queryParams.queryVariables.bfyrzw"
            placeholder="请输入被反映人职务"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->



<!--      <el-form-item label="所属流程" prop="processDefinitionKey">
        <el-input
          v-model="queryParams.processDefinitionKey"
          placeholder="请输入流程定义的标识"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>-->
<!--      <el-form-item label="流程分类" prop="category">
        <el-select
          v-model="queryParams.category"
          placeholder="请选择流程分类"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </el-select>
      </el-form-item>-->
      <el-form-item label="流程状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择流程状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="新闻标题" prop="queryVariables.news_title" v-if="category !== 'oa_task'">
        <el-input
          v-model="queryParams.queryVariables.news_title"
          placeholder="请输入新闻标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="新闻类别" prop="category" v-if="category !== 'oa_task'">
        <el-select
          v-model="queryParams.queryVariables.news_category"
          placeholder="请选择新闻类别"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_NEWS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
<!--        <el-button
          type="primary"
          plain
          v-hasPermi="['bpm:process-instance:create']"
          @click="handleCreate(undefined)"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 发起流程
        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="序号" width="50" type="index" fixed="left" />
      <el-table-column label="流程名称" align="center" prop="name" min-width="120px" fixed="left" />
      <el-table-column label="流程标题" align="center" prop="processInstanceExt.processInstanceTitle" min-width="200px" fixed="left" :show-overflow-tooltip="false" class-name="wrap-cell">
        <template #default="scope">
          <div class="wrap-text">{{ scope.row.processInstanceExt?.processInstanceTitle || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="当前审批任务" align="center" prop="tasks" min-width="200px" fixed="left" :show-overflow-tooltip="false" class-name="wrap-cell">
        <template #default="scope">
          <div class="wrap-task-buttons">
            <el-button type="primary" v-for="task in [...new Map(scope.row.tasks?.map(task => [task.name, task]) || []).values()]" :key="task.id" link>
              <span>{{ task.name }}</span>
            </el-button>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="流程分类"
        align="center"
        prop="categoryName"
        min-width="100"
        fixed="left"
      /> -->
      <el-table-column label="流程状态" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="发起时间"
        align="center"
        prop="startTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column align="center" label="耗时" prop="durationInMillis" width="160">
        <template #default="scope">
          {{ scope.row.durationInMillis > 0 ? formatPast2(scope.row.durationInMillis) : '-' }}
        </template>
      </el-table-column>
<!--      <el-table-column label="流程编号" align="center" prop="id" min-width="320px" />-->
      <el-table-column label="操作" align="center" fixed="right" width="180">
        <template #default="scope">
          <el-button
            link
            type="primary"
            v-hasPermi="['bpm:process-instance:query']"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
<!--          <el-button
            link
            type="primary"
            v-if="scope.row.status === 1"
            v-hasPermi="['bpm:process-instance:query']"
            @click="handleCancel(scope.row)"
          >
            取消
          </el-button>-->
<!--          <el-button link type="primary" v-else @click="handleCreate(scope.row)">-->
<!--            重新发起-->
<!--          </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, formatPast2 } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { CategoryApi, categoryRoute } from '@/api/bpm/category'
import { ProcessInstanceVO } from '@/api/bpm/processInstance'
import * as DefinitionApi from '@/api/bpm/definition'
import { cloneDeep } from 'lodash-es'

defineOptions({ name: 'BpmProcessInstanceMy' })

const router = useRouter() // 路由
const route = useRoute() // 路由
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const category=ref(null)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  processDefinitionKey: undefined,
  category: undefined,
  status: undefined,
  createTime: [],
  queryVariables:{
    form_title:'',//流程标题
    news_title: '', // 新闻标题
    news_category:'' //新闻类别
  }
})

const queryFormRef = ref() // 搜索的表单
const categoryList = ref([]) // 流程分类列表

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.category = category.value
    // 将queryVariable中的对象转化为json字符串便于后端处理
    const query = cloneDeep(queryParams)
    // console.log("query.queryVariables.wtlb",query.queryVariables.wtlb)
    // query.queryVariables.wtlb = query.queryVariables.wtlb+'' //转换为字符串方便后端处理
    const json = JSON.stringify(query.queryVariables)
    query.queryVariables = json
    const data = await ProcessInstanceApi.getProcessInstanceMyPage(query)
    
    // 对列表数据进行处理 - 仅处理办理时限
    list.value = data.list.map(item => {
      // 处理办理时限，使用processInstanceExt.processInstanceLimitDate
      if (item.processInstanceExt && item.processInstanceExt.processInstanceLimitDate) {
        // 如果没有formVariables，则初始化为空对象
        if (!item.formVariables) {
          item.formVariables = {};
        }
        // 设置办理时限
        item.formVariables.blsx = item.processInstanceExt.processInstanceLimitDate;
      }
      
      return item;
    });
    
    total.value = data.total
    console.info("list",JSON.stringify(list.value))
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  // 手动重置queryVariables,因为有些深层嵌套的属性resetFields可能处理不完全
  queryParams.queryVariables.news_title = ''
  queryParams.queryVariables.category = ''
  queryParams.queryVariables.form_title = ''
  queryParams.queryVariables.news_category = ''
  handleQuery()
}

/** 发起流程操作 **/
const handleCreate = async (row?: ProcessInstanceVO) => {
  // 如果是【业务表单】，不支持重新发起
  if (row?.id) {
    const processDefinitionDetail = await DefinitionApi.getProcessDefinition(
      row.processDefinitionId
    )
    debugger
    if (processDefinitionDetail.formType === 20) {
      message.error('重新发起流程失败，原因：该流程使用业务表单，不支持重新发起')
      return
    }
  }
  // 跳转发起流程界面
  await router.push({
    name: 'BpmProcessInstanceCreate',
    query: { processInstanceId: row?.id }
  })
}

/** 查看详情 */
const handleDetail = (row) => {
  console.log("row.id",row.id)
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消按钮操作 */
const handleCancel = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.id, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

const getCategory=async ()=>{
  console.log('获取分类参数, 路由信息:', route.path, route.query)
  category.value=await categoryRoute.getCategoryByRouteParam(route)
  console.log('获取到分类:', category.value)
}

/** 激活时 **/
onActivated(() => {
  // 检查是否需要刷新数据
  console.log('新闻列表页激活, 路由参数:', route.query)
  if (route.query.refresh === 'true') {
    console.log('新闻列表页检测到refresh参数，刷新数据...', category.value)
    // 获取数据
    getList()
    // 移除refresh参数，避免后续在此页面刷新时反复触发刷新逻辑
    console.log('移除refresh参数, 原始参数:', route.query)
    
    // 构建新的查询参数，显式排除refresh
    const newQuery = { ...route.query }
    delete newQuery.refresh
    
    router.replace({
      path: route.path,
      query: newQuery
    }).then(() => {
      console.log('路由参数已更新:', router.currentRoute.value.query)
    }).catch(err => {
      console.error('路由参数更新失败:', err)
    })
  } else {
    // 防止重复调用
    console.log('没有refresh参数, loading状态:', loading.value)
    if (!loading.value) {
      getList()
    }
  }
})

/** 初始化 **/
onMounted(async () => {
  console.log('新闻列表页初始化, 路由参数:', route.query)
  // 确保先获取分类信息
  await getCategory()
  console.log('新闻列表页当前分类:', category.value)
  
  // 检查是否需要刷新数据
  if (route.query.refresh === 'true') {
    console.log('新闻列表页初始化时检测到refresh参数，刷新数据...', category.value)
    // 获取数据
     getList()
    // 移除refresh参数，避免后续在此页面刷新时反复触发刷新逻辑
    console.log('移除refresh参数, 原始参数:', route.query)
    
    // 构建新的查询参数，显式排除refresh
    const newQuery = { ...route.query }
    delete newQuery.refresh
    
    router.replace({
      path: route.path,
      query: newQuery
    }).then(() => {
      console.log('路由参数已更新:', router.currentRoute.value.query)
    }).catch(err => {
      console.error('路由参数更新失败:', err)
    })
  } else {
    console.log('没有refresh参数, 正常获取数据')
    getList()
  }

 // categoryList.value = await CategoryApi.getCategorySimpleList()
})
</script>

<style scoped>
.wrap-cell {
  white-space: normal;
  line-height: 1.5;
}

.wrap-text {
  word-break: break-word;
  white-space: normal;
}

.wrap-task-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 5px;
}
</style>
