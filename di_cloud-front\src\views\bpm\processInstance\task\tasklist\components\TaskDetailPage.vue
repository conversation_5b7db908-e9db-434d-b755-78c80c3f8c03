<template>
  <ContentWrap>
    <div class="task-detail-page">
      <!-- 页面标题区域 -->
      <div class="page-header mb-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-button @click="goBack" plain>
              <Icon icon="ep:arrow-left" class="mr-1" /> 返回
            </el-button>
          </div>
        </div>
      </div>

      <div v-if="currentTaskDetail" class="official-document">
        <div class="document-header">
          <h2 class="document-title">{{ currentTaskDetail.taskName }}</h2>
        </div>
        
        <div class="document-body">
          <div class="content-section basic-info">
            <div class="info-grid">
              <div class="grid-item">
                <div class="item-label">工作项目：</div>
                <div class="item-value">{{ currentTaskDetail?.taskTypeLabel || '未指定' }}</div>
              </div>
              <div class="grid-item">
                <div class="item-label">办理时限：</div>
                <div class="item-value">{{ formatDateTime(currentTaskDetail?.dueTime) || '未设置' }}</div>
              </div>
              <div class="grid-item">
                <div class="item-label">接收单位：</div>
                <div class="item-value">{{ currentTaskDetail?.receiverName || '未指定' }}</div>
              </div>
              <div class="grid-item">
                <div class="item-label">催办标记：</div>
                <div class="item-value">
                  <span v-if="currentTaskDetail?.processInstanceUrgingNum > 0" class="urgent-label">
                    已催办 {{ currentTaskDetail.processInstanceUrgingNum }} 次
                  </span>
                  <span v-else>未催办</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="content-section task-requirements">
            <div class="section-header">
              <span class="section-title">任务要求</span>
            </div>
            <div class="section-content">
              <div class="content-text">
                {{ processData?.formVariables?.task_demand || currentTaskDetail?.description || '无任务要求' }}
              </div>
              
              <div class="attachment-subsection">
                <div class="subsection-title">附件列表：</div>
                <div
                  v-if="(processData?.formVariables?.task_file && processData.formVariables.task_file.length > 0) || (currentTaskDetail?.groupAttachments && currentTaskDetail.groupAttachments.length > 0)" 
                  class="content-box attachment-list">
                  <div
                    v-for="(file, index) in processData?.formVariables?.task_file || currentTaskDetail?.groupAttachments" 
                    :key="index" 
                    class="attachment-item"
                    @click="downloadAttachment(file)">
                    <i class="document-icon"></i>
                    <span class="file-name">{{ file.name }}</span>
                  </div>
                </div>
                <div v-else class="no-attachments">
                  无集团上传附件
                </div>
              </div>
            </div>
          </div>
          
          <div class="content-section company-progress">
            <div class="section-header">
              <span class="section-title">二级公司办理情况</span>
            </div>
            <div class="section-content">
              <div class="content-text">
                {{ getSecondaryCompanyContent || '二级公司尚未填写任务落实情况' }}
              </div>
              
              <div class="attachment-subsection">
                <div class="subsection-title">附件列表：</div>
                <div
                  v-if="getSecondaryCompanyAttachments && getSecondaryCompanyAttachments.length > 0" 
                  class="content-box attachment-list">
                  <div
                    v-for="(file, index) in getSecondaryCompanyAttachments" 
                    :key="index" 
                    class="attachment-item"
                    @click="downloadAttachment(file)">
                    <i class="document-icon"></i>
                    <span class="file-name">{{ file.name }}</span>
                  </div>
                </div>
                <div v-else class="no-attachments">
                  无二级公司上传附件
                </div>
              </div>
              
              <div v-if="getAttendees && getAttendees.length > 0" class="attendees-section">
                <h4 class="attendees-title">参会人员信息：</h4>
                <el-table :data="getAttendees" style="width: 100%">
                  <el-table-column prop="name" label="姓名" />
                  <el-table-column prop="unit" label="单位" />
                  <el-table-column prop="position" label="职务" />
                  <el-table-column prop="phone" label="手机号" />
                </el-table>
              </div>
              <div v-if="getSecondaryCompanyTask" class="feedback-meta">
                <div class="feedback-time">
                  反馈时间: {{ formatDateTime(getSecondaryCompanyTask.endTime) }}
                </div>
                <div v-if="getSecondaryCompanyTask.assigneeUser" class="feedback-person">
                  反馈人: {{ getSecondaryCompanyTask.assigneeUser.nickname }}
                </div>
              </div>
            </div>
          </div>
          
          <div class="content-section approval-opinions">
            <div class="section-header">
              <span class="section-title">任务发起人审批意见</span>
            </div>
            <div class="section-content">
              <div class="content-text">
                {{ getApprovalOpinion || '暂无审批意见' }}
              </div>
              <div v-if="getApprovalTask" class="feedback-meta">
                <div class="feedback-time">
                  审批时间: {{ formatDateTime(getApprovalTask.endTime) }}
                </div>
                <div v-if="getApprovalTask.assigneeUser" class="feedback-person">
                  审批人: {{ getApprovalTask.assigneeUser.nickname }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as TaskApi from '@/api/bpm/task'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 当前任务详情
const currentTaskDetail = ref<any>(null)
// 流程数据
const processData = ref<any>(null)
const taskListData = ref<any[]>([])

// 监听路由参数变化
watch(
  () => route.query,
  async (newQuery) => {
    if (newQuery && newQuery.processInstanceId) {
      try {
        // 获取流程实例数据
        const processInstanceData = await ProcessInstanceApi.getProcessInstance(newQuery.processInstanceId as string)
        processData.value = processInstanceData

        // 获取任务列表数据
        const taskListResponse = await TaskApi.getTaskListByProcessInstanceId(newQuery.processInstanceId as string)
        taskListData.value = taskListResponse

        // 找到当前任务的详细信息
        const currentTask = taskListResponse?.find(task => task.id === route.params.id)

        // 从路由参数和流程实例数据中构建任务详情
        const taskDetail = {
          id: route.params.id,
          taskName: newQuery.taskName,
          taskType: newQuery.taskType,
          taskTypeLabel: newQuery.taskTypeLabel,
          processInstanceId: newQuery.processInstanceId,
          dueTime: newQuery.dueTime,
          receiverName: newQuery.receiverName,
          description: processInstanceData?.formVariables?.task_demand || newQuery.description,
          processInstanceUrgingNum: Number(newQuery.processInstanceUrgingNum) || 0,
          groupAttachments: processInstanceData?.formVariables?.task_file || 
            (Array.isArray(newQuery.groupAttachments) ? newQuery.groupAttachments : [])
        }
        currentTaskDetail.value = taskDetail

      } catch (error: any) {
        console.error('获取数据失败:', error)
        message.error('获取任务详情失败')
      }
    }
  },
  { immediate: true }
)

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  try {
    const timestamp = typeof dateTime === 'string' ? parseInt(dateTime) : dateTime
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) return ''
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  } catch (e) {
    console.error('日期格式化错误:', e)
    return ''
  }
}

// 下载附件
const downloadAttachment = (file) => {
  if (file && file.url) {
    fetch(file.url)
      .then(response => response.blob())
      .then(blob => {
        const blobUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = blobUrl
        link.download = file.name
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(blobUrl)
        message.success(`开始下载 ${file.name}`)
      })
      .catch(error => {
        console.error('下载文件失败:', error)
        message.error('下载文件失败，请重试')
      })
  } else {
    message.error('附件下载链接无效')
  }
}

// 获取二级公司办理任务的数据
const getSecondaryCompanyTask = computed(() => {
  if (!taskListData.value?.length) return null
  return taskListData.value.find(task => 
    task.formId === 75 || task.formId === 82
  )
})

// 获取二级公司办理情况
const getSecondaryCompanyContent = computed(() => {
  const task = getSecondaryCompanyTask.value
  if (!task) return null

  if (task.formId === 75) {
    return task.formVariables?.F4tfm8e5fijiagc || ''
  } else if (task.formId === 82) {
    return task.formVariables?.Fty5m8ih6k8xadc || ''
  }
  return ''
})

// 获取二级公司附件
const getSecondaryCompanyAttachments = computed(() => {
  const task = getSecondaryCompanyTask.value
  if (!task) return []

  if (task.formId === 75) {
    return task.formVariables?.['eedc3c4f-7759-4bd8-b449-25ae336885e1'] || []
  } else if (task.formId === 82) {
    return task.formVariables?.['6a8b1ca7-4798-49f3-beef-a8c31ca2da56'] || []
  }
  return []
})

// 获取参会人员信息
const getAttendees = computed(() => {
  const task = getSecondaryCompanyTask.value
  if (!task || task.formId !== 75) return []
  return task.formVariables?.attendees?.attendees || []
})

// 获取审批意见
const getApprovalOpinion = computed(() => {
  if (!taskListData.value?.length) return null
  const approvalTask = taskListData.value.find(task => task.formId === 79)
  return approvalTask?.formVariables?.opinion || ''
})

// 获取审批任务
const getApprovalTask = computed(() => {
  if (!taskListData.value?.length) return null
  return taskListData.value.find(task => task.formId === 79)
})

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
.task-detail-page {
  font-family: PingFang SC, Microsoft YaHei UI, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  padding: 12px 0;
}

.official-document {
  width: 100%;
  background-color: white;
  position: relative;
  font-size: 14px;
  line-height: 1.8;
  color: #334155;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  overflow: hidden;
}

.document-header {
  text-align: center;
  padding: 40px 0 30px;
  border-bottom: 2px solid #c9151e;
  position: relative;
  margin: 0 40px;
  background-color: #fcfcfc;
}

.document-title {
  font-size: 28px;
  font-weight: 600;
  color: #c9151e;
  margin: 0;
  letter-spacing: 1.5px;
  text-shadow: 0 1px 2px rgba(201, 21, 30, 0.1);
}

.document-body {
  padding: 40px 48px 48px;
  background-color: #fcfcfc;
}

.content-section {
  margin-bottom: 48px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px;
  border: 1px solid #f0f0f0;
}

.section-header {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 20px;
  padding-bottom: 16px;
  position: relative;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  position: relative;
  padding-left: 16px;
  color: #1e293b;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 20px;
  background-color: #c9151e;
  border-radius: 3px;
}

.section-content {
  padding: 8px 16px;
  line-height: 1.8;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  padding: 20px 0;
}

.grid-item {
  display: flex;
  align-items: flex-start;
}

.item-label {
  font-weight: 500;
  width: 160px;
  flex-shrink: 0;
  text-align: right;
  color: #64748b;
}

.item-value {
  flex: 1;
  color: #334155;
  font-weight: 500;
}

.urgent-label {
  color: #dc2626;
  font-weight: 600;
  background-color: #fee2e2;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

.content-text {
  min-height: 60px;
  padding: 20px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  white-space: pre-wrap;
  border-radius: 8px;
  color: #334155;
  line-height: 1.7;
  margin-bottom: 16px;
}

.attachment-subsection {
  margin-top: 16px;
}

.subsection-title {
  font-weight: 500;
  margin-bottom: 12px;
  color: #475569;
  padding-left: 8px;
  border-left: 3px solid #c9151e;
}

.no-attachments {
  color: #94a3b8;
  font-style: italic;
  padding: 8px 0 8px 8px;
}

.content-box {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
}

.no-data {
  text-align: center;
  color: #94a3b8;
  font-style: normal;
  padding: 32px 0;
}

.feedback-meta {
  display: flex;
  justify-content: flex-end;
  gap: 32px;
  margin-top: 16px;
  font-size: 14px;
  color: #64748b;
  padding: 0 12px;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 45%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.attachment-item:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
}

.document-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3Cpath fill='%23c9151e' d='M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm160-14.1v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-size: cover;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #334155;
}

.attendees-section {
  margin-top: 24px;
  margin-bottom: 16px;
}

.attendees-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #475569;
}

/* 表格样式优化 */
.attendees-section :deep(.el-table) {
  --el-table-border-color: #e2e8f0;
  --el-table-header-bg-color: #f8fafc;
  --el-table-row-hover-bg-color: #f1f5f9;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.attendees-section :deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
  color: #475569;
  padding: 14px 12px;
  border-bottom: 1px solid #e2e8f0;
}

.attendees-section :deep(.el-table td) {
  padding: 14px 12px;
  color: #334155;
  border-bottom: 1px solid #e2e8f0;
}

.attendees-section :deep(.el-table--border) {
  border: 1px solid #e2e8f0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .document-body {
    padding: 24px;
  }
  
  .content-section {
    padding: 16px;
    margin-bottom: 32px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .item-label {
    width: 130px;
  }
  
  .attachment-item {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .document-header {
    padding: 24px 0 20px;
    margin: 0 20px;
  }
  
  .document-title {
    font-size: 20px;
  }
  
  .feedback-meta {
    flex-direction: column;
    gap: 8px;
  }
}

/* 完成、退回和催办行样式 */
:deep(.completed-row) {
  background-color: #f0f9eb;
}

:deep(.returned-row) {
  background-color: #fdf6ec;
}

:deep(.urged-row) {
  background-color: #fef0f0;
}

:deep(.status-tag) {
  min-width: 60px;
}
</style> 
