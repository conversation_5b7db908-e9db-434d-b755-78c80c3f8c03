<template>
  <div class="home-container">
    <!-- 轮播图 -->
    <div class="carousel-container mb-20px">
      <CarouselForm :imgURLs="imgURLs" @click="handleCarouselClick" />
    </div>

    <el-row :gutter="20" justify="space-between">
      <!-- 新闻区域 - 在大屏幕上占据更多空间 -->
      <el-col :xl="18" :lg="18" :md="24" :sm="24" :xs="24" class="mb-20px">
        <NewsSection 
          :loading="loading" 
          :news-type="news_type" 
          :projects="projectsList" 
          @view-more="handleMore" 
          @view-detail="handleDetail"
        />
      </el-col>
      
      <!-- 右侧边栏 - 在大屏幕上占据较少空间 -->
      <el-col :xl="6" :lg="6" :md="24" :sm="24" :xs="24">
        <div class="right-sidebar">
          <!-- 待办任务 -->
          <TodoCard :loading="loading" class="mb-20px" />
          
          <!-- 信访件链接 -->
          <!-- <EmailCard :loading="loading" /> -->
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { formatTime } from '@/utils'
import CarouselForm from './components/CarouseForm.vue'
import { useUserStore } from '@/store/modules/user'
import type { WorkplaceTotal, Project } from './types'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getNewsHomeData } from '@/api/bpm/processInstance'
import NewsSection from './components/NewsSection.vue'
import EmailCard from './components/EmailCard.vue'
import TodoCard from './components/TodoCard.vue'

// 通用状态
const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const loading = ref(true)
const username = userStore.getUser.nickname

// 自定义类型
interface CarouselItem {
  url: string
  id: string
}

// 新闻相关数据
const news_type = ref<any[]>([])
const imgURLs = ref<CarouselItem[]>([])
const projectsList = ref<Project[]>([])

/**
 * 查看新闻详情
 */
const handleDetail = (item: Project) => {
  router.push({
    name: 'newsPage',
    query: {
      id: item.news_id
    }
  })
}

/**
 * 查看更多新闻
 */
const handleMore = (category: string | number) => {
  // 先强制滚动到顶部，然后再导航
  window.scrollTo(0, 0);
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
  
  // 设置一个标志，标记来源
  localStorage.setItem('fromMoreButton', 'true');
  
  // 使用replace而不是push，防止历史记录堆栈的问题
  router.replace({
    name: 'newsCategoryPage',
    query: {
      category: category
    }
  });
}

/**
 * 获取所有数据
 */
const getAllApi = async () => {
  loading.value = true
  
  try {
    await fetchNewsHomeData()
  } catch (error) {
    console.error('获取首页数据时发生错误:', error)
  } finally {
    loading.value = false
  }
}

// 轮播图点击处理
const handleCarouselClick = (id: string) => {
  if (id) {
    router.push({
      name: 'newsPage',
      query: {
        id: id
      }
    })
  }
}

/**
 * 获取新闻首页数据并处理轮播图和新闻列表数据
 */
const fetchNewsHomeData = async () => {
  try {
    const response = await getNewsHomeData({})
    
    // 处理不同的响应结构可能性
    const responseData = response?.data || response || {};
    
    // 1. 处理轮播图数据
    const carouselData = responseData.newsCarousel || [];
    if (Array.isArray(carouselData) && carouselData.length > 0) {
      imgURLs.value = carouselData.map(item => {
        // 检查carouselCoverUrl格式并提取URL
        let url = '';
        const coverUrlStr = item.carouselCoverUrl;
        
        if (typeof coverUrlStr === 'string') {
          // 处理字符串格式 "[{name=xxx.png, url=https://...}]"
          if (coverUrlStr.includes('url=')) {
            // 使用正则表达式提取URL部分
            const urlMatch = coverUrlStr.match(/url=(https?:\/\/[^,\s}]+)/);
            if (urlMatch && urlMatch[1]) {
              url = urlMatch[1];
            }
          } else {
            // 如果没有url=格式，可能是直接的URL
            url = coverUrlStr;
          }
        }
        
        return {
          url: url,
          id: String(item.id)
        }
      })
    } else {
      imgURLs.value = [] // 设置为空数组避免错误
    }

    // 2. 处理新闻类别和列表
    const categoryData = responseData.newsCategory || [];
    if (Array.isArray(categoryData) && categoryData.length > 0) {
      // 更新新闻类别
      news_type.value = categoryData.map(category => ({
        value: category.category,
        label: category.name
      }))
      
      // 更新新闻列表
      let allNews: Project[] = []
      categoryData.forEach(category => {
        if (category.list && Array.isArray(category.list)) {
          const newsItems = category.list.map(item => ({
            name: item.title,
            icon: item.hot,
            message: 'https://www.brightfood.com/',
            personal: item.publisher,
            time: new Date(item.publishDate || item.createTime || new Date()),
            news_id: String(item.id),
            news_category: category.category
          }))
          allNews = [...allNews, ...newsItems]
        }
      })
      projectsList.value = allNews
    } else {
      news_type.value = []
      projectsList.value = []
    }
  } catch (error) {
    console.error('获取新闻首页数据出错:', error)
    // 出现错误时设置默认空数据
    imgURLs.value = []
    news_type.value = []
    projectsList.value = []
  } finally {
    // 无论成功失败都完成加载
    loading.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  getAllApi()
})
</script>

<style scoped>
.home-container {
  padding: 0 12px;
  max-width: 1600px;
  margin: 0 auto;
}

.carousel-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.mb-20px {
  margin-bottom: 20px;
}

.right-sidebar {
  height: 100%;
}

.el-carousel__item h3 {
  color: #475669;
  opacity: 0.75;
  line-height: 300px;
  margin: 0;
  text-align: center;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

@media (max-width: 768px) {
  .home-container {
    padding: 0 8px;
  }
  
  .mb-20px {
    margin-bottom: 16px;
  }
}
</style>
