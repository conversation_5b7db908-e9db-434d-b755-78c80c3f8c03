<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'
import * as NotifyMessageApi from '@/api/system/notify/message'
import { useUserStoreWithOut } from '@/store/modules/user'

defineOptions({ name: 'Message' })

const { push } = useRouter()
const userStore = useUserStoreWithOut()
const activeName = ref('notice')
const unreadCount = ref(0) // 未读消息数量
const list = ref<any[]>([]) // 消息列表

// 获得消息列表
const getList = async () => {
  list.value = await NotifyMessageApi.getUnreadNotifyMessageList()
  // 强制设置 unreadCount 为 0，避免小红点因为轮询太慢，不消除
  unreadCount.value = 0
}

// 获得未读消息数
const getUnreadCount = async () => {
  NotifyMessageApi.getUnreadNotifyMessageCount().then((data) => {
    unreadCount.value = data
  })
}

// 跳转我的站内信
const goMyList = () => {
  push({
    name: 'MyNotifyMessage'
  })
}

// 点击消息项处理
const handleMessageClick = async (item) => {
  // 如果是流程相关消息，跳转到流程详情页
  if (
    (item.templateCode === 'SYS_BPM_PROCESS_INSTANCE_TASK_URGING' || 
     item.templateCode === 'bpm_process_instance_approve') && 
    item.templateParams?.processInstanceId
  ) {
    // 如果未读，标记为已读
    if (!item.readStatus) {
      await NotifyMessageApi.updateNotifyMessageRead(item.id)
      // 刷新列表和未读消息数
      await getUnreadCount()
      await getList()
    }
    
    push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: item.templateParams.processInstanceId
      }
    })
  } else {
    // 其他类型消息直接跳转到站内信页面
    goMyList()
  }
}

// ========== 初始化 =========
onMounted(() => {
  // 首次加载小红点
  getUnreadCount()
  // 轮询刷新小红点
  setInterval(
    () => {
      if (userStore.getIsSetUser) {
        getUnreadCount()
      } else {
        unreadCount.value = 0
      }
    },
    // 1000 * 60 * 2
    1000 * 60 * 2
  )
})
</script>
<template>
  <div class="message">
    <ElPopover :width="400" placement="bottom" trigger="click">
      <template #reference>
        <ElBadge :is-dot="unreadCount > 0" class="item">
          <Icon :size="18" class="cursor-pointer" icon="ep:bell" @click="getList" />
        </ElBadge>
      </template>
      <ElTabs v-model="activeName">
        <ElTabPane label="我的站内信" name="notice">
          <el-scrollbar class="message-list">
            <template v-for="item in list" :key="item.id">
              <div class="message-item" @click="handleMessageClick(item)">
                <img alt="" class="message-icon" src="@/assets/imgs/avatar.gif" />
                <div class="message-content">
                  <span class="message-title">
                    <template v-if="item.templateCode === 'SYS_BPM_PROCESS_INSTANCE_TASK_URGING'">
                      催办提醒: {{ item.templateParams?.title }}
                    </template>
                    <template v-else-if="item.templateCode === 'bpm_process_instance_approve'">
                      {{ item.templateContent }}
                    </template>
                    <template v-else>
                      {{ item.templateNickname }}：{{ item.templateContent }}
                    </template>
                  </span>
                  <span class="message-date">
                    {{ formatDate(item.createTime) }}
                  </span>
                </div>
              </div>
            </template>
          </el-scrollbar>
        </ElTabPane>
      </ElTabs>
      <!-- 更多 -->
      <div style="margin-top: 10px; text-align: right">
        <XButton preIcon="ep:view" title="查看全部" type="primary" @click="goMyList" />
      </div>
    </ElPopover>
  </div>
</template>
<style lang="scss" scoped>
.message-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 260px;
  line-height: 45px;
}

.message-list {
  display: flex;
  height: 400px;
  flex-direction: column;

  .message-item {
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--el-border-color-light);
    cursor: pointer;

    &:last-child {
      border: none;
    }

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    .message-icon {
      width: 40px;
      height: 40px;
      margin: 0 20px 0 5px;
    }

    .message-content {
      display: flex;
      flex-direction: column;

      .message-title {
        margin-bottom: 5px;
      }

      .message-date {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
</style>
