<template>

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="流程标题" prop="queryVariables.form_title">
        <el-input
          v-model="queryParams.queryVariables.form_title"
          placeholder="请输入流程标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item v-if="['oa_xf', 'oa_xs'].includes(queryParams.category)" label="登记单位" prop="queryVariables.djdw">
        <el-input
          v-model="queryParams.queryVariables.djdw"
          placeholder="请输入登记单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <!-- <el-table-column label="信访来源" prop="queryVariables.xfly" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.queryVariables.xfly" />
        </template>
      </el-table-column> -->
        <el-form-item v-if="queryParams.category== xf" label="信访编号" prop="queryVariables.xfbh">
          <el-input
            v-model="queryParams.queryVariables.xfbh"
            placeholder="请输入信访编号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <!-- <el-form-item label="反映人姓名" prop="queryVariables.fyrxm">
          <el-input
            v-model="queryParams.queryVariables.fyrxm"
            placeholder="请输入反映人姓名"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->
        <!-- <el-form-item label="反映人单位" prop="queryVariables.fyrdw">
          <el-input
            v-model="queryParams.queryVariables.fyrdw"
            placeholder="请输入反映人单位"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->
        <!-- <el-form-item label="反映人职务" prop="queryVariables.fyrdw">
          <el-input
            v-model="queryParams.queryVariables.fyrdw"
            placeholder="请输入反映人职务"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->
        <!-- <el-form-item label="联系方式" prop="queryVariables.lxfs">
          <el-input
            v-model="queryParams.queryVariables.lxfs"
            placeholder="请输入联系方式"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->

        <el-form-item v-if="['oa_xf', 'oa_xs'].includes(queryParams.category)" label="被反映人姓名" prop="queryVariables.bfyrxm">
          <el-input
            v-model="queryParams.queryVariables.bfyrxm"
            placeholder="请输入被反映人姓名"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item v-if="['oa_xf', 'oa_xs'].includes(queryParams.category)" label="被反映人单位" prop="queryVariables.bfyrdw">
          <el-input
            v-model="queryParams.queryVariables.bfyrdw"
            placeholder="请输入被反映人单位"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item v-if="['oa_xf', 'oa_xs'].includes(queryParams.category)" label="被反映人职务" prop="queryVariables.bfyrzw">
          <el-input
            v-model="queryParams.queryVariables.bfyrzw"
            placeholder="请输入被反映人职务"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>



<!--      <el-form-item label="所属流程" prop="processDefinitionKey">
        <el-input
          v-model="queryParams.processDefinitionKey"
          placeholder="请输入流程定义的标识"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>-->
<!--      <el-form-item label="流程分类" prop="category">
        <el-select
          v-model="queryParams.category"
          placeholder="请选择流程分类"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </el-select>
      </el-form-item>-->
      <!-- <el-form-item label="问题类别" prop="status" v-if="queryParams.category== xf">
        <el-select
          v-model="queryParams.queryVariables.wtlb"
          placeholder="请选择问题类别"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_MODEL_FORM_TYPE_XFBD_WTLB)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="问题类别" prop="status" v-if="queryParams.category== xs">
        <el-cascader
          v-model="queryParams.queryVariables.wtlb"
          :options="options"
          :props="props"
        />
      </el-form-item> -->
      <el-form-item label="流程状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择流程状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
<!--        <el-button
          type="primary"
          plain
          v-hasPermi="['bpm:process-instance:create']"
          @click="handleCreate(undefined)"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 发起流程
        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="序号" width="50" type="index" fixed="left" />
      <el-table-column label="流程名称" align="center" prop="name" min-width="120px" fixed="left" />
      <el-table-column label="流程标题" align="center" prop="processInstanceExt.processInstanceTitle" min-width="200px" fixed="left" :show-overflow-tooltip="false" class-name="wrap-cell">
        <template #default="scope">
          <div class="wrap-text">{{ scope.row.processInstanceExt?.processInstanceTitle || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="当前审批任务" align="center" prop="tasks" min-width="200px" :show-overflow-tooltip="false">
        <template #default="scope">
          <div class="wrap-task-buttons">
            <el-button 
              type="primary" 
              v-for="task in [...new Map(scope.row.tasks?.map(task => [task.name, task]) || []).values()]" 
              :key="task.id" 
              link
            >
              <span class="task-name">{{ task.name }}</span>
            </el-button>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="流程分类"
        align="center"
        prop="categoryName"
        min-width="100"
        fixed="left"
      /> -->
      <el-table-column label="流程状态" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="办理时限"
        align="center"
        width="180"
        v-if="category === 'oa_xs'"
      >
        <template #default="scope">
          {{ scope.row.formVariables?.blsx || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="发起时间"
        align="center"
        prop="startTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column align="center" label="耗时" prop="durationInMillis" width="160">
        <template #default="scope">
          {{ scope.row.durationInMillis > 0 ? formatPast2(scope.row.durationInMillis) : '-' }}
        </template>
      </el-table-column>
<!--      <el-table-column label="流程编号" align="center" prop="id" min-width="320px" />-->
      <el-table-column label="操作" align="center" fixed="right" width="160">
        <template #default="scope">
          <div class="button-group">
            <el-button
              link
              type="primary"
              @click="handleDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="category === 'oa_xs' && scope.row.status === 1 && userStore.getUser.id === scope.row.processInstanceExt?.reviewerUserId"
              link
              type="warning"
              @click="handleUrge(scope.row)"
            >
              催办
            </el-button>
            <el-button
              v-if="scope.row.formVariables?.blsx && scope.row.status !== 3 && scope.row.status !== 4"
              v-hasPermi="['bpm:process-instance:apply-delay']"
              link
              type="primary"
              @click="handleApplyDelay(scope.row)"
            >
              申请延期
            </el-button>
            <el-button
              v-if="scope.row.formVariables?.blsx"
              link
              type="primary"
              @click="handleViewHistory(scope.row)"
            >
            延期历史记录
            </el-button>
            <el-button
              v-if=" scope.row.processDefinition.id?.split(':')[0] === 'wtxssqyq_bpm'"
              link
              type="primary"
              @click="handleViewParentDetail(scope.row)"
            >
              查看上级流程
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <!-- 申请延期对话框 -->
  <TaskDelayForm ref="taskDelayFormRef" @success="getList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, formatPast2 } from '@/utils/formatTime'
import { ElMessageBox, ElMessage } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { CategoryApi, categoryRoute } from '@/api/bpm/category'
import { ProcessInstanceVO } from '@/api/bpm/processInstance'
import * as DefinitionApi from '@/api/bpm/definition'
import { cloneDeep } from 'lodash-es'
import { childSelector } from 'markmap-common'
import TaskDelayForm from '@/views/bpm/processInstance/detail/dialog/TaskDelayForm.vue'
import { useUserStore } from '@/store/modules/user'
import { getUserProfile } from '@/api/system/user/profile'

defineOptions({ name: 'BpmProcessInstanceMy' })
const message = useMessage() // 消息弹窗
const router = useRouter() // 路由
const route = useRoute() // 路由
const { t } = useI18n() // 国际化
const userStore = useUserStore() // 用户 store
const { push } = useRouter() // 路由
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const category=ref(null)
const xsbd_wtlb = ref(null) //线索表单问题类别
const dict_xf_wtlb =ref(null) //信访表单问题类别
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  processDefinitionKey: undefined,
  category: undefined,
  status: undefined,
  createTime: [],
  queryVariables:{
    form_title:'',//流程标题
    djdw:'', //登记单位
    xfly:'',//信访来源
    xfbh:'', //信访编号
    fyrxm:'',//反映人姓名
    fyrdw:'',//反映人单位
    fyrzw:'',//反映人职务
    lxfs:'',//联系方式
    bfyrxm:'',//被反映人姓名
    bfyrdw:'',//被反映人单位
    bfyrzw:'',//被反映人职务
    wtlb:'',//问题类别
  }
})
const xf = ref("oa_xf")// 类别为信访
const xs = ref("oa_xs")//类别为线索
const queryFormRef = ref() // 搜索的表单
const categoryList = ref([]) // 流程分类列表
const props = {
  expandTrigger: 'hover' as const,
}
const taskDelayFormRef = ref() // 申请延期表单的 Ref

// 添加检查当前用户岗位的方法
// 判断当前用户是否具有特定岗位
const hasRequiredPost = ref(false) // 是否有所需岗位的标志

// 获取当前用户的岗位信息
const getCurrentUserPosts = async () => {
  try {
    // 通过查询个人信息获取当前用户岗位
    const userProfile = await getUserProfile()
    if (userProfile && userProfile.posts) {
      // 检查用户是否有"二级集团案管专员"或"项目经理"岗位
      const requiredPosts = ['二级集团案管专员', '项目经理']
      const userPostNames = userProfile.posts.map(post => post.name)
      
      // 检查用户的岗位是否包含任一所需岗位
      const hasPost = requiredPosts.some(postName => userPostNames.includes(postName))
      hasRequiredPost.value = hasPost
      
      console.log('当前用户岗位:', userPostNames, '是否有所需岗位:', hasRequiredPost.value)
    }
  } catch (error) {
    console.error('获取用户岗位信息失败:', error)
    hasRequiredPost.value = false
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.category = category.value
    // 将queryVariable中的对象转化为json字符串便于后端处理
    const query = cloneDeep(queryParams)
    const json = JSON.stringify(query.queryVariables)
    query.queryVariables = json
    const data = await ProcessInstanceApi.getProcessInstanceMyPage(query)
    
    // 对列表数据进行处理
    list.value = data.list.map(item => {
      // 只处理办理时限，不再处理流程标题
      // 处理办理时限，优先使用processInstanceExt.processInstanceLimitDate
      if (item.processInstanceExt && item.processInstanceExt.processInstanceLimitDate) {
        // 如果没有formVariables，则初始化为空对象
        if (!item.formVariables) {
          item.formVariables = {};
        }
        // 设置办理时限，直接使用processInstanceExt.processInstanceLimitDate，它已经是'YYYY-MM-DD'格式
        item.formVariables.blsx = item.processInstanceExt.processInstanceLimitDate;
        console.log(`从processInstanceExt设置流程[${item.id}]的办理时限: ${item.processInstanceExt.processInstanceLimitDate}`);
      } 
      // 如果processInstanceExt中没有办理时限，则尝试使用原来的processedInstanceLimitDate作为备选
      else if (item.processedInstanceLimitDate && Array.isArray(item.processedInstanceLimitDate) && item.processedInstanceLimitDate.length === 3) {
        const [year, month, day] = item.processedInstanceLimitDate;
        // 格式化为YYYY-MM-DD格式
        const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        
        // 将办理时限添加到formVariables中
        if (!item.formVariables) {
          item.formVariables = {};
        }
        item.formVariables.blsx = formattedDate;
        console.log(`使用备选processedInstanceLimitDate设置流程[${item.id}]的办理时限: ${formattedDate}`);
      }
      
      return item;
    });
    
    total.value = data.total
    console.info("处理后的流程列表数据:", JSON.stringify(list.value))
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 发起流程操作 **/
const handleCreate = async (row?: ProcessInstanceVO) => {
  // 如果是【业务表单】，不支持重新发起
  if (row?.id) {
    const processDefinitionDetail = await DefinitionApi.getProcessDefinition(
      row.processDefinitionId
    )
    debugger
    if (processDefinitionDetail.formType === 20) {
      ElMessage.error('重新发起流程失败，原因：该流程使用业务表单，不支持重新发起')
      return
    }
  }
  // 跳转发起流程界面
  await router.push({
    name: 'BpmProcessInstanceCreate',
    query: { processInstanceId: row?.id }
  })
}

/** 查看详情 */
const handleDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消按钮操作 */
const handleCancel = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.id, value)
  ElMessage.success('取消成功')
  // 刷新列表
  await getList()
}

const getCategory=async ()=>{
  category.value=await categoryRoute.getCategoryByRouteParam(route)
}

const options = [
  {
    value: '1',
    label: '违纪行为',
    children: [
      {
        value: '2',
        label: '违反政治纪律',
      },
      {
        value: '3',
        label: '违反组织纪律',
      },
      {
        value: '4',
        label: '违反廉洁纪律',
      },
      {
        value: '5',
        label: '违反群众纪律',
      },
      {
        value: '6',
        label: '违反工作纪律',
      },
      {
        value: '7',
        label: '违反生活纪律',
      }
    ],
  },
  {
    value: '8',
    label: '职务违法犯罪行为',
    children: [
      {
        value: '9',
        label: '贪污贿赂',
      },
      {
        value: '10',
        label: '滥用职权',
      },  
      {
        value: '11',
        label: '玩忽职守',
      },  
      {
        value: '12',
        label: '徇私舞弊',
      },  
      {
        value: '13',
        label: '重大责任事故',
      },
      {
        value: '14',
        label: '其他职务违法犯罪行为',
      }       
    ]
  }, 
  {
    value: '15',
    label: '其他违法犯罪行为',
    children: [
      {
        value: '16',
        label: '侵犯财产',
      },
      {
        value: '17',
        label: '破坏社会主义市场经济秩序',
      },  
      {
        value: '18',
        label: '妨害社会管理秩序',
      },  
      {
        value: '19',
        label: '侵犯公民人身权利民主权利',
      }  
    ]
  },  
  {
    value: '20',
    label: '其他',
    children:[
      {
        value: '21',
        label: '是否违反中央八项规定精神',
      }  
    ]
  }    
  
]
/** 激活时 **/
onActivated(() => {
  // 首先获取最新的 category 参数
  getCategory().then(() => {
    console.log('激活时获取到的 category:', category.value)
    
    // 检查是否需要刷新数据
    if (route.query.refresh === 'true') {
      console.log('检测到 refresh 参数，刷新数据...')
      // 获取数据
      getList()
      // 移除 refresh 参数，避免后续在此页面刷新时反复触发刷新逻辑
      router.replace({
        name: route.name,
        query: { 
          ...route.query, 
          refresh: undefined,
          category: category.value // 保留 category 参数
        }
      })
    } else {
      // 防止重复调用
      if (!loading.value) {
        getList()
      }
    }
  })
})

/** 初始化 **/
onMounted(async () => {
  // 获取分类参数
  await getCategory()
  console.log('初始化时获取到的 category:', category.value)
  
  // 检查路由是否有 refresh 参数
  if (route.query.refresh === 'true') {
    console.log('初始化时检测到 refresh 参数，刷新数据...')
    // 获取数据
    await getList()
    // 移除 refresh 参数，避免后续刷新
    router.replace({
      name: route.name,
      query: { 
        ...route.query, 
        refresh: undefined,
        category: category.value // 保留 category 参数 
      }
    })
  } else {
    // 正常加载数据
    await getList()
  }
  
  // 获取用户岗位信息
  // await getCurrentUserPosts()
  
  // 添加路由变化监听，确保 category 变化时刷新数据
  watch(() => route.query.category, async (newCategory) => {
    if (newCategory && newCategory !== category.value) {
      console.log('检测到 category 参数变化:', newCategory)
      category.value = newCategory as string
      await getList()
    }
  })
})

/** 申请延期操作 */
const handleApplyDelay = (row) => {
  // 获取流程实例中当前正在运行的任务
  if (row.tasks && row.tasks.length > 0) {
    // 获取流程实例ID和任务ID
    const processInstanceId = row.id
    const taskId = row.tasks[0].id
    // 获取办理时限值
    const deadline = row.formVariables?.blsx || ''
    // 打开申请延期表单，同时传递任务ID、流程实例ID和办理时限
    taskDelayFormRef.value?.open(taskId, processInstanceId, deadline)
    console.log('打开申请延期表单, 任务ID:', taskId, '流程实例ID:', processInstanceId, '办理时限:', deadline)
  } else {
    ElMessage.warning('该流程没有可延期的任务')
  }
}

/** 查看原流程详情操作 */
const handleViewParentDetail = async (row) => {
  try {
    // 先调用API获取详细数据
    console.log("查看原流程详情，获取实例数据中...", row.id)
    const processDetail = await ProcessInstanceApi.getProcessInstance(row.id)
    
    // 从返回的数据中提取lcid字段
    const parentProcessId = processDetail.formVariables?.lcid
    if (!parentProcessId) {
      ElMessage.warning('无法获取原流程信息')
      return
    }
    
    console.log("获取到原流程ID:", parentProcessId)
    
    // 跳转到流程实例详情页面
    router.push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: parentProcessId,
        hideApproval: 'true'
      }
    })
  } catch (error) {
    console.error("获取流程详情失败:", error)
    ElMessage.error('获取流程详情失败')
  }
}

/** 催办操作 */
const handleUrge = async (row) => {
  try {
    await ElMessageBox.confirm('确定要催办该流程吗？', '催办确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 调用催办API
    await ProcessInstanceApi.urgeProcessInstances({
      processInstanceIds: [row.id]
    })
    
    ElMessage.success('催办成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('催办失败:', error)
      ElMessage.error('催办失败，请稍后重试')
    }
  }
}

/** 查看历史申请记录 */
const handleViewHistory = (row) => {
  console.log('查看历史申请记录', row.id)
  if (!row.id ) {
    message.warning('无法获取流程信息')
    return
  }
  
  console.log('查看历史申请记录', row.id)
  
  // 跳转到历史申请列表页面
  push({
    name: 'BpmProcessInstanceHistory',
    query: {
      processInstanceId: row.id
    }
  })
}
</script>

<style scoped>
.wrap-cell {
  white-space: normal;
  line-height: 1.5;
}

.wrap-text {
  word-break: break-word;
  white-space: normal;
}

.wrap-task-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 5px;
}

.task-name {
  word-break: break-all;
  white-space: normal;
  line-height: 1.5;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 5px;
}
</style>
