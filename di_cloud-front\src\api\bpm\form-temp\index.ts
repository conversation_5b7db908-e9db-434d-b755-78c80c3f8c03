import request from '@/config/axios'

export const formTempApi = {

  /**
   *更新表单暂缓数据
   */
   updateFormTem : async (data: FormVO) => {
    return await request.put({
      url: '/bpm/form-tem/update',
      data: data
    })
  },

  /**
   *更新表单暂缓数据状态* 注：在暂存实例提交流程引擎成功后进行调用，更新暂存数据状态
   */
   updateStatusFormByTemp : async (params) => {
    return await request.put({
      url: '/bpm/form-tem/update-status',
      params
    })
  },

  /**
   * 保存暂存流程表单
   * @param data
   */
  saveProcessInstanceForm : async (data) => {
    return await request.post({url: '/bpm/form-tem/save', data: data})
  },


  /*

   */
   deleteFormTem : async (id: number) => {
    return await request.delete({
      url: '/bpm/form-tem/delete?id=' + id
    })
  },

  /**
   * 获取流程表单
   * @param id
   */
  getFormTem : async (id: number) => {
    return await request.get({
      url: '/bpm/form-tem/get?id=' + id
    })
  },


  /**
   * 获取暂存流程列表
   * @param params
   */
  getFormTemList : async (params) => {
    return await request.get({
      url: '/bpm/form-tem/list',
      params
    })
  },

}
