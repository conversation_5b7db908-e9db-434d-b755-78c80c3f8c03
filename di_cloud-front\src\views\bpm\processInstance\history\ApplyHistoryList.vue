<template>
  <div class="apply-history-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>历史申请记录</span>
          <el-button @click="goBack" type="primary" text>返回</el-button>
        </div>
      </template>
      
      <el-table v-loading="loading" :data="historyList">
        <el-table-column label="延期申请标题" prop="childrenProcessInstanceTitle" align="center" show-overflow-tooltip />   
        <el-table-column label="申请原因" prop="applyDelayReason" align="center" show-overflow-tooltip />             
        <el-table-column label="延期天数" prop="applyDelayDays" align="center" width="120" />
        <el-table-column label="状态" align="center" width="120">
        <!-- <el-table-column label="父流程实例ID" prop="parentProcessInstanceId" align="center" show-overflow-tooltip />
        <el-table-column label="父流程任务ID" prop="parentProcessInstanceTaskId" align="center" show-overflow-tooltip /> -->
        <!-- <el-table-column label="延期申请流程ID" prop="childrenProcessInstanceId" align="center" show-overflow-tooltip /> -->


          <template #default="scope">
            {{ formatState(scope.row.childrenProcessInstanceState) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button type="primary" link @click="viewDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空数据显示 -->
      <el-empty v-if="!loading && historyList.length === 0" description="暂无历史申请记录" />
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'ApplyHistoryList' })

const route = useRoute()
const router = useRouter()
const message = useMessage()

const loading = ref(true)
const historyList = ref<any[]>([])
const processInstanceId = ref('')

// 格式化状态
const formatState = (state) => {
  const stateMap = {
    1: '审批中',
    2: '审批通过',
    3: '审批不通过',
    4: '取消'
  }
  return stateMap[state] || '未知状态'
}

// 获取历史申请列表
const getHistoryList = async () => {
  loading.value = true
  try {
    // 获取路由参数中的流程实例ID
    processInstanceId.value = route.query.processInstanceId as string
    
    if (!processInstanceId.value) {
      message.error('缺少流程实例ID参数')
      return
    }
    
    // 调用API获取申请延期历史列表
    const result = await ProcessInstanceApi.getApplyDelayHistory({ processInstanceId: processInstanceId.value })
    
    if (Array.isArray(result)) {
      historyList.value = result
      console.log('获取到历史申请记录:', result)
    } else {
      console.error('API返回的数据格式不符合预期:', result)
      historyList.value = []
    }
  } catch (error) {
    console.error('获取历史申请记录失败:', error)
    message.error('获取历史申请记录失败')
    historyList.value = []
  } finally {
    loading.value = false
  }
}

// 查看详情
const viewDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.childrenProcessInstanceId
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 初始化
onMounted(() => {
  getHistoryList()
})
</script>

<style lang="scss" scoped>
.apply-history-list {
  padding: 16px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style> 
