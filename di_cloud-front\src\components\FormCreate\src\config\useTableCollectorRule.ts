import { generateUUID } from '@/utils'
import { localeProps, makeRequiredRule } from '@/components/FormCreate/src/utils'

/**
 * 表格收集器规则
 */
export const useTableCollectorRule = () => {
  const label = '参会人员信息收集'
  const name = 'TableCollector'
  
  // 修改默认列定义为参会人员信息收集所需的字段，并添加提交字段名
  const defaultColumns = [
    {
      prop: 'name',
      label: '姓名',
      type: 'input',
      field: 'attendeeName', // 提交字段名
      required: true, // 必填项
      placeholder: '请输入姓名'
    },
    {
      prop: 'unit',
      label: '单位',
      type: 'input',
      field: 'attendeeUnit', // 提交字段名
      required: true, // 必填项
      placeholder: '请输入单位名称'
    },
    {
      prop: 'position',
      label: '职务',
      type: 'input',
      field: 'attendeePosition', // 提交字段名
      required: false, // 非必填项
      placeholder: '请输入职务'
    },
    {
      prop: 'phone',
      label: '手机号',
      type: 'input',
      field: 'attendeePhone', // 提交字段名
      required: true, // 必填项
      placeholder: '请输入11位手机号',
      regExp: '^1[3-9]\\d{9}$', // 手机号正则表达式
      regExpMessage: '请输入正确的手机号格式', // 正则验证失败提示消息
      maxLength: 11 // 最大长度
    }
  ]
  
  return {
    icon: 'icon-table',
    label,
    name,
    rule() {
      return {
        type: name,
        field: generateUUID(),
        title: label,
        info: '',
        $required: false,
        value: [], // 确保值始终是数组
        props: {
          columns: JSON.parse(JSON.stringify(defaultColumns)), // 深拷贝，避免引用问题
          minRows: 1,
          maxRows: 10,
          showOperation: true,
          showAddButton: true,
          fieldName: 'attendees', // 默认字段名为attendees
          // 添加处理程序来正确查找嵌套数据结构
          processFormData: true, // 启用处理formVariables中嵌套数据的功能
          nestedFormat: true // 启用双层嵌套结构，形如：attendees.attendees
        },
        validate: [
          {
            required: false,
            type: 'array',
            message: '请填写参会人员信息'
          }
        ]
      }
    },
    props(_, { t }) {
      // 选项的基本配置
      const columnProps = [
        {
          type: 'input',
          field: 'prop',
          title: '字段名',
          value: '',
          props: {
            placeholder: '如：name'
          },
          validate(val) {
            return !!val
          }
        },
        {
          type: 'input',
          field: 'label',
          title: '显示名称',
          value: '',
          props: {
            placeholder: '如：姓名'
          },
          validate(val) {
            return !!val
          }
        },
        {
          type: 'input',
          field: 'field',
          title: '提交字段名(可选)',
          value: '',
          props: {
            placeholder: '如：userName'
          }
        },
        {
          type: 'select',
          field: 'type',
          title: '字段类型',
          value: 'input',
          options: [
            { label: '文本输入框', value: 'input' },
            { label: '数字输入框', value: 'number' },
            { label: '日期选择器', value: 'date' },
            { label: '下拉选择框', value: 'select' },
            { label: '纯文本', value: 'text' }
          ]
        },
        {
          type: 'switch',
          field: 'required',
          title: '是否必填',
          value: false,
          info: '设置字段是否为必填项'
        },
        {
          type: 'input',
          field: 'width',
          title: '列宽',
          value: '',
          props: {
            placeholder: '如：120px'
          }
        },
        {
          type: 'input',
          field: 'placeholder',
          title: '占位文本',
          value: '',
          props: {
            placeholder: '请输入占位文本'
          }
        },
        {
          type: 'input',
          field: 'defaultValue',
          title: '默认值',
          value: '',
          props: {
            placeholder: '字段默认值'
          }
        },
        {
          type: 'inputNumber',
          field: 'maxLength',
          title: '最大长度',
          value: 0,
          info: '设置为0表示不限制长度',
          props: {
            min: 0,
            placeholder: '输入字符最大长度'
          }
        },
        {
          type: 'input',
          field: 'regExp',
          title: '验证正则表达式',
          value: '',
          props: {
            placeholder: '如：^1[3-9]\\d{9}$'
          },
          info: '用于验证输入内容的正则表达式'
        },
        {
          type: 'input',
          field: 'regExpMessage',
          title: '验证失败提示',
          value: '',
          props: {
            placeholder: '如：请输入正确的手机号格式'
          },
          info: '正则表达式验证失败时的提示信息'
        }
      ];
      
      // 为select类型添加选项配置
      const optionsConfig = {
        type: 'group',
        field: 'options',
        title: '选项配置(仅下拉框有效)',
        value: [],
        props: {
          min: 0,
          defaultValue: []
        },
        children: {
          column: [
            {
              type: 'input',
              field: 'label',
              title: '选项名称',
              value: '',
              props: {
                placeholder: '如：选项1'
              }
            },
            {
              type: 'input',
              field: 'value',
              title: '选项值',
              value: '',
              props: {
                placeholder: '如：option1'
              }
            }
          ]
        }
      };
      
      // 将选项配置添加到列属性中
      columnProps.push(optionsConfig as any); // 使用类型断言解决类型不匹配问题
      
      // 表格配置
      const tableProps = [
        makeRequiredRule(),
        {
          type: 'group',
          field: 'columns',
          title: '表格列配置',
          value: JSON.parse(JSON.stringify(defaultColumns)), // 深拷贝，避免引用问题
          props: {
            min: 1,
            max: 20,
            defaultValue: [],
            rules: [
              { required: true, message: '请至少添加一列', trigger: 'blur' }
            ]
          },
          validate(val) {
            return Array.isArray(val) && val.length > 0
          },
          children: {
            column: columnProps
          }
        },
        {
          type: 'inputNumber',
          field: 'minRows',
          title: '最小行数',
          value: 1,
          props: {
            min: 0
          }
        },
        {
          type: 'inputNumber',
          field: 'maxRows',
          title: '最大行数',
          value: 10,
          props: {
            min: 1
          }
        },
        {
          type: 'switch',
          field: 'showOperation',
          title: '显示操作列',
          value: true
        },
        {
          type: 'switch',
          field: 'showAddButton',
          title: '显示添加按钮',
          value: true
        },
        {
          type: 'switch',
          field: 'processFormData',
          title: '处理表单变量',
          value: true,
          info: '启用后将在formVariables中查找参会人员数据'
        },
        {
          type: 'input',
          field: 'fieldName',
          title: '表格字段名',
          value: 'attendees',
          props: {
            placeholder: '如：attendees'
          },
          info: '将表格数据以该字段名包装，留空则不包装'
        },
        {
          type: 'switch',
          field: 'nestedFormat',
          title: '使用嵌套结构',
          value: true,
          info: '启用后将使用双层嵌套结构(如attendees.attendees)，适配新版后端API'
        },
        {
          type: 'switch',
          field: 'flatSingleRow',
          title: '单行数据扁平化',
          value: false,
          info: '当表格只有一行数据时是否扁平化展示，不使用数组'
        }
      ];
      
      return localeProps(t, name + '.props', tableProps);
    }
  }
} 