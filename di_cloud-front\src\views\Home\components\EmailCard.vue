<template>
  <el-card shadow="never" class="mt-8px">
    <template #header>
      <div class="h-3 flex justify-between">
        <span>{{ t('workplace.jwEmail') }}</span>
      </div>
    </template>
    <el-skeleton :loading="loading" animated>
      <div class="flex items-center">
        <Icon icon="akar-icons:envelope" :size="25" class="mr-8px" />
        <el-link type="default" :underline="false" href="https://www.brightfood.com/">
          <img src="@/assets/imgs/jwemail.png" alt="" style="width: 100px; height: 100px;"/>
        </el-link>
      </div>
    </el-skeleton>
  </el-card>
</template>

<script lang="ts" setup>
defineProps({
  loading: {
    type: Boolean,
    required: true
  }
})

const { t } = useI18n()
</script>

<style scoped>
</style> 