<template>
  <div class="parent-dept-field-container">
    <!-- 文本显示模式 -->
    <div v-if="displayType === 'text'" class="dept-text-display">
      <el-descriptions :column="1" border>
        <el-descriptions-item :label="fieldLabel">
          <span v-if="loading">加载中...</span>
          <span v-else-if="deptInfo">{{ deptName }}</span>
          <span v-else>{{ defaultText }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    
    <!-- 下拉选择模式 -->
    <div v-else-if="displayType === 'select'" class="dept-select-display">
      <el-form-item :label="fieldLabel">
        <el-select
          v-model="selectedDeptValue"
          class="dept-select"
          :disabled="disabled"
          :loading="loading"
          @change="handleDeptChange"
        >
          <el-option
            v-for="item in deptOptions"
            :key="item.id"
            :label="item.name"
            :value="valueType === 'id' ? item.id : (valueType === 'name' ? item.name : JSON.stringify(item))"
          />
        </el-select>
      </el-form-item>
    </div>
    
    <!-- 隐藏字段模式 - 实际上什么都不显示，但会在表单中提交值 -->
    <div v-else-if="displayType === 'hidden'" style="display: none;"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getUserProfile } from '@/api/system/user/profile'
import { getDept } from '@/api/system/dept'

defineOptions({ name: 'ParentDeptField' })

type DisplayType = 'text' | 'hidden' | 'select'
type ValueType = 'id' | 'name' | 'both'

interface DeptInfo {
  id: number
  name: string
  parentId?: number
}

const props = defineProps({
  // 绑定值（表单数据对象的字段名）
  modelValue: {
    type: [String, Number, Object],
    default: ''
  },
  // 字段名称
  field: {
    type: String,
    default: ''
  },
  // 显示类型
  displayType: {
    type: String as () => DisplayType,
    default: 'text'
  },
  // 字段标签
  fieldLabel: {
    type: String,
    default: '上级部门'
  },
  // 值类型
  valueType: {
    type: String as () => ValueType,
    default: 'id'
  },
  // 是否禁用（用于select类型）
  disabled: {
    type: Boolean,
    default: false
  },
  // 无上级部门时的默认值
  defaultValue: {
    type: [String, Number],
    default: ''
  },
  // 无上级部门时的默认文本
  defaultText: {
    type: String,
    default: '无上级部门'
  },
  // 无上级部门时是否使用当前部门ID
  useCurrentDeptWhenNoParent: {
    type: Boolean,
    default: true
  },
  // 兼容formCreate的注入属性
  formCreateInject: {
    type: Object,
    default: () => ({})
  }
})

// 支持v-model
const emit = defineEmits(['update:modelValue', 'change'])

// 状态变量
const loading = ref(true)
const deptInfo = ref<DeptInfo | null>(null)
const deptOptions = ref<DeptInfo[]>([])
const selectedDeptValue = ref<string | number>('')
const userDeptInfo = ref<DeptInfo | null>(null)

// 计算属性：部门名称
const deptName = computed(() => deptInfo.value?.name || props.defaultText)

// 计算属性：部门ID
const deptId = computed(() => deptInfo.value?.id || props.defaultValue || null)

// 处理部门选择变更
const handleDeptChange = (value: string | number) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// 根据值类型格式化部门值
const formatDeptValue = (dept: DeptInfo | null): string | number | object | null => {
  if (!dept) {
    // 如果没有部门信息，返回默认值
    return props.defaultValue || null
  }
  
  switch (props.valueType) {
    case 'id':
      return dept.id
    case 'name':
      return dept.name
    case 'both':
      return { id: dept.id, name: dept.name }
    default:
      return dept.id
  }
}

// 加载当前用户的部门信息
const loadUserDeptInfo = async () => {
  loading.value = true
  try {
    // 1. 获取当前用户信息
    const userProfile = await getUserProfile()
    
    if (!userProfile || !userProfile.dept || !userProfile.dept.id) {
      console.warn('未能获取到用户部门信息')
      setEmptyValue()
      return
    }
    
    // 保存用户的部门信息
    userDeptInfo.value = {
      id: userProfile.dept.id,
      name: userProfile.dept.name
    }
    
    // 2. 获取用户部门的详细信息
    const deptDetail = await getDept(userProfile.dept.id)
    
    if (!deptDetail || !deptDetail.parentId) {
      console.warn('当前用户部门无上级部门')
      // 根据配置决定是否使用当前部门ID
      if (props.useCurrentDeptWhenNoParent) {
        console.log('使用当前部门ID替代上级部门')
        setCurrentDeptAsValue()
      } else {
        setEmptyValue()
      }
      return
    }
    
    // 3. 获取上级部门信息
    const parentDept = await getDept(deptDetail.parentId)
    
    if (!parentDept) {
      console.warn('无法获取上级部门信息')
      // 根据配置决定是否使用当前部门ID
      if (props.useCurrentDeptWhenNoParent) {
        console.log('使用当前部门ID替代上级部门')
        setCurrentDeptAsValue()
      } else {
        setEmptyValue()
      }
      return
    }
    
    // 设置部门信息
    deptInfo.value = {
      id: parentDept.id,
      name: parentDept.name,
      parentId: parentDept.parentId
    }
    
    // 将部门添加到下拉选项中
    deptOptions.value = [deptInfo.value]
    
    // 设置选中值
    const formattedValue = formatDeptValue(deptInfo.value)
    
    if (props.valueType === 'both') {
      selectedDeptValue.value = JSON.stringify(formattedValue)
    } else {
      selectedDeptValue.value = formattedValue as string | number
    }
    
    // 更新表单值
    emit('update:modelValue', formattedValue)
    emit('change', formattedValue)
    
    console.log('已加载用户上级部门信息:', deptInfo.value)
  } catch (error) {
    console.error('加载用户上级部门信息失败:', error)
    ElMessage.error('加载用户上级部门信息失败')
    if (userDeptInfo.value && props.useCurrentDeptWhenNoParent) {
      // 如果已获取用户部门信息且配置允许使用当前部门，则使用当前部门作为默认值
      setCurrentDeptAsValue()
    } else {
      setEmptyValue()
    }
  } finally {
    loading.value = false
  }
}

// 当无上级部门时，使用当前部门作为值
const setCurrentDeptAsValue = () => {
  if (!userDeptInfo.value) return setEmptyValue()
  
  // 使用当前部门信息
  deptInfo.value = userDeptInfo.value
  deptOptions.value = [userDeptInfo.value]
  
  // 格式化值
  const formattedValue = formatDeptValue(userDeptInfo.value)
  
  // 确保selectedDeptValue不会是null
  if (props.valueType === 'both') {
    selectedDeptValue.value = JSON.stringify(formattedValue)
  } else {
    selectedDeptValue.value = formattedValue as string | number || ''
  }
  
  // 更新表单值
  emit('update:modelValue', formattedValue)
  emit('change', formattedValue)
  
  console.log('使用当前部门ID作为默认值:', userDeptInfo.value)
}

// 当无上级部门时设置默认值
const setEmptyValue = () => {
  deptInfo.value = null
  deptOptions.value = []
  
  // 确保selectedDeptValue不会是null
  selectedDeptValue.value = props.defaultValue || ''
  
  // 更新表单值为默认值
  emit('update:modelValue', props.defaultValue || null)
  emit('change', props.defaultValue || null)
  
  loading.value = false
}

// 初始化
onMounted(() => {
  loadUserDeptInfo()
})

// 监听值类型变化，更新表单值
watch(() => props.valueType, () => {
  if (deptInfo.value) {
    const formattedValue = formatDeptValue(deptInfo.value)
    
    if (props.valueType === 'both') {
      selectedDeptValue.value = JSON.stringify(formattedValue)
    } else {
      selectedDeptValue.value = formattedValue as string | number
    }
    
    emit('update:modelValue', formattedValue)
    emit('change', formattedValue)
  }
})
</script>

<style scoped>
.parent-dept-field-container {
  width: 100%;
}

.dept-text-display {
  width: 100%;
}

.dept-select-display {
  width: 100%;
}

.dept-select {
  width: 100%;
}
</style> 