// 引入基础依赖
import 'bpmn-js/dist/assets/diagram-js.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css'

// 引入 Token Simulation 相关样式
import 'bpmn-js-token-simulation/assets/css/normalize.css'
import 'bpmn-js-token-simulation/assets/css/font-awesome.min.css'
import 'bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css'

// 引入组件
import MyProcessDesigner from './ProcessDesigner.vue'

// 引入 BPMN Token Simulation 相关样式
import '../theme/process-designer.scss'

MyProcessDesigner.install = function (Vue) {
  Vue.component(MyProcessDesigner.name, MyProcessDesigner)
}

// 流程图的设计器，可编辑
export default MyProcessDesigner
