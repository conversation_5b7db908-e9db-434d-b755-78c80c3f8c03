<template>
  <div v-if="!disabled" class="upload-file">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="uploadUrl"
      :auto-upload="autoUpload"
      :before-upload="beforeUpload"
      :disabled="disabled"
      :drag="drag"
      :http-request="httpRequest"
      :limit="props.limit"
      :multiple="props.limit > 1"
      :on-error="excelUploadError"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleFileSuccess"
      :show-file-list="true"
      class="upload-file-uploader"
      name="file"
    >
      <el-button type="primary">
        <Icon icon="ep:upload-filled"/>
        选取文件
      </el-button>
      <template v-if="isShowTip" #tip>
        <div style="font-size: 14px">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </div>
        <div style="font-size: 14px">
          格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b> 的文件
        </div>
      </template>
      <template #file="row">
        <div class="flex items-center">
          <span>{{ row.file.name }}</span>
          <div class="ml-10px">
            <el-link
              :href="row.file.url"
              :underline="false"
              @click.prevent="handleDownload(row.file)"
              type="primary"
            >
              下载
            </el-link>
          </div>
          <div class="ml-10px">
            <el-button link type="danger" @click="handleRemove(row.file)"> 删除</el-button>
          </div>
        </div>
      </template>
    </el-upload>
  </div>

  <!-- 上传操作禁用时 -->
  <div v-if="disabled" class="upload-file">
    <div v-for="(file, index) in fileList" :key="index" class="flex items-center file-list-item">
      <span>{{ file.name }}</span>
      <div class="ml-10px">
        <el-link :href="file.url" :underline="false" @click.prevent="handleDownload(file)" type="primary">
          下载
        </el-link>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { propTypes } from '@/utils/propTypes'
import type { UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus'
import { isString } from '@/utils/is'
import { useUpload } from '@/components/UploadFile/src/useUpload'
import { UploadFile } from 'element-plus/es/components/upload/src/upload'
import { ElMessage } from 'element-plus'

defineOptions({name: 'UploadFile'})

const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  modelValue: propTypes.oneOfType<string | string[]|UploadUserFile[]>([String, Array<String>]).isRequired,
  fileType: propTypes.array.def(['doc', 'xls', 'ppt', 'txt', 'pdf']), // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileSize: propTypes.number.def(5), // 大小限制(MB)
  limit: propTypes.number.def(5), // 数量限制
  autoUpload: propTypes.bool.def(true), // 自动上传
  drag: propTypes.bool.def(false), // 拖拽上传
  isShowTip: propTypes.bool.def(true), // 是否显示提示
  disabled: propTypes.bool.def(false) // 是否禁用上传组件 ==> 非必传（默认为 false）
})

// ========== 上传相关 ==========
const uploadRef = ref<UploadInstance>()
const uploadList = ref<UploadUserFile[]>([])
const fileList = ref<UploadUserFile[]>([])
const tmpFiles = ref<UploadUserFile>([])
const deleteFileIndex = ref<number>([])
const uploadNumber = ref<number>(0)

const {uploadUrl, httpRequest} = useUpload()

// 文件上传之前判断
const beforeUpload: UploadProps['beforeUpload'] = (file: UploadRawFile) => {
  let fileExtension = ''
  if (file.name.lastIndexOf('.') > -1) {
    fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1).toLowerCase()
  }
  
  // 压缩包格式的MIME类型
  const compressedMimeTypes = {
    'zip': ['application/zip', 'application/x-zip-compressed'],
    'rar': ['application/vnd.rar', 'application/x-rar-compressed'],
    '7z': ['application/x-7z-compressed']
  }
  
  // 检查文件类型是否允许上传
  const isFileTypeAllowed = props.fileType.some((type: string) => {
    // 标准MIME类型检查
    if (file.type && file.type.indexOf(type) > -1) {
      return true
    }
    
    // 扩展名检查
    if (fileExtension === type) {
      return true
    }
    
    // 特殊处理压缩文件的MIME类型
    if (compressedMimeTypes[type] && file.type && compressedMimeTypes[type].includes(file.type)) {
      return true
    }
    
    return false
  })
  
  const isLimit = file.size < props.fileSize * 1024 * 1024
  
  if (!isFileTypeAllowed) {
    ElMessage.error(`文件格式不正确, 请上传${props.fileType.join('/')}格式!`)
    return false
  }
  
  if (!isLimit) {
    ElMessage.error(`上传文件大小不能超过${props.fileSize}MB!`)
    return false
  }
  
  ElMessage.success('正在上传文件，请稍候...')
  uploadNumber.value++
}
// 处理上传的文件发生变化
// const handleFileChange = (uploadFile: UploadFile): void => {
//   uploadRef.value.data.path = uploadFile.name
// }
// 文件上传成功
const handleFileSuccess: UploadProps['onSuccess'] = (res: any): void => {
  ElMessage.success('上传成功')
  console.log("res",JSON.stringify(res))
  if (res.data instanceof Array) {
    tmpFiles.value = []
    res.data.forEach((item: any) => {
      if(isString(item)){
        tmpFiles.value.push({name: item, url: item})
      }else{
        tmpFiles.value.push({name: item.name, url: item.url})
      }

    })
  }
  if (isString(res.data)) {
    tmpFiles.value = [{name: res.data, url: res.data}]
  }
  console.log("tmpFiles",JSON.stringify(tmpFiles.value))
  deleteFileIndex.value = []
  tmpFiles.value.forEach((item) => {
    deleteFileIndex.value.push(fileList.value.findIndex((item2: any) => {
      if (item2.url == item.url|| item2.name == item.name) {
        return true
      }
    }))
  })
  console.log("deleteFileIndex",JSON.stringify(deleteFileIndex.value))
  deleteFileIndex.value.forEach((item) => {
    if (item > -1) {
      fileList.value.splice(item, 1)
    }
  })
  uploadList.value.push(...tmpFiles.value)
  console.log("uploadList",JSON.stringify(uploadList.value))
  console.log("fileList",JSON.stringify(fileList.value))
  // 删除自身
  //const index = fileList.value.findIndex((item) => item.response?.data === res.data)
  //fileList.value.splice(index, 1)
  //uploadList.value.push({ name: res.data, url: res.data })
  if (uploadList.value.length == uploadNumber.value) {
    fileList.value.push(...uploadList.value)
    console.log("fileList",JSON.stringify(fileList.value))
    uploadList.value = []
    uploadNumber.value = 0
    emitUpdateModelValue()
  }
}
// 文件数超出提示
const handleExceed: UploadProps['onExceed'] = (): void => {
  ElMessage.error(`上传文件数量不能超过${props.limit}个!`)
}
// 上传错误提示
const excelUploadError: UploadProps['onError'] = (): void => {
  ElMessage.error('导入数据失败，请您重新上传！')
}
// 删除上传文件
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.map((f) => f.name).indexOf(file.name)
  if (index > -1) {
    fileList.value.splice(index, 1)
    emitUpdateModelValue()
  }
}
const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
  console.log(uploadFile)
}

// 监听模型绑定值变动
watch(
  () => props.modelValue,
  (val: string | string[]|UploadUserFile[]) => {
    console.log("val",JSON.stringify(val))
    if (!val) {
      fileList.value = [] // fix：处理掉缓存，表单重置后上传组件的内容并没有重置
      return
    }

    fileList.value = [] // 保障数据为空
    // 情况1：字符串
    if (isString(val)) {
      fileList.value.push(
        ...val.split(',').map((url) => ({name: url.substring(url.lastIndexOf('/') + 1), url}))
      )
      return
    }
    // 情况2：数组
 /*   fileList.value.push(
      ...(val as string[]).map((url) => ({name: url.substring(url.lastIndexOf('/') + 1), url}))
    )*/
    fileList.value.push(
      ...(val as UploadUserFile[]).map((file: UploadUserFile) => ({name:file.name, url: file.url}))
    )
  },
  {immediate: true, deep: true}
)
// 发送文件链接列表更新
const emitUpdateModelValue = () => {
  // 情况1：数组结果

  let result: string | string[]|UploadUserFile[] = fileList.value.map((file) => {if(file){return{name: file.name, url: file.url}}else{return null}})
  console.log("result1",JSON.stringify(result))
  // 情况2：逗号分隔的字符串
  if (props.limit === 1 || isString(props.modelValue)) {
    result = result.join(',')
  }
  emit('update:modelValue', result)
}

// 添加下载处理函数
const handleDownload = async (file: any) => {
  try {
    // 使用fetch获取文件内容
    const response = await fetch(file.url)
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }
    
    // 获取文件Blob
    const blob = await response.blob()
    
    // 创建Blob URL
    const blobUrl = window.URL.createObjectURL(blob)
    
    // 创建一个隐藏的a标签用于下载
    const link = document.createElement('a')
    link.href = blobUrl
    // 使用原始文件名下载
    link.download = file.name
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(blobUrl)
    
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error(`下载失败: ${error.message || '未知错误'}`)
  }
}
</script>
<style lang="scss" scoped>
.upload-file-uploader {
  margin-bottom: 5px;
}

:deep(.upload-file-list .el-upload-list__item) {
  position: relative;
  margin-bottom: 10px;
  line-height: 2;
  border: 1px solid #e4e7ed;
}

:deep(.el-upload-list__item-file-name) {
  max-width: 250px;
}

:deep(.upload-file-list .ele-upload-list__item-content) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

:deep(.ele-upload-list__item-content-action .el-link) {
  margin-right: 10px;
}

.file-list-item {
  border: 1px dashed var(--el-border-color-darker);
  border-radius: 8px;
}
</style>
