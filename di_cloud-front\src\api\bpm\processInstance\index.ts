import request from '@/config/axios'
import { ProcessDefinitionVO } from '@/api/bpm/model'
import { NodeType } from '@/components/SimpleProcessDesignerV2/src/consts'
export type Task = {
  id: string
  name: string
}

export type ProcessInstanceVO = {
  id: number
  name: string
  processDefinitionId: string
  category: string
  result: number
  tasks: Task[]
  fields: string[]
  status: number
  remark: string
  businessKey: string
  createTime: string
  endTime: string
  processDefinition?: ProcessDefinitionVO
}

// 用户信息
export type User = {
  id: number,
  nickname: string,
  avatar: string
}

// 审批任务信息
export type ApprovalTaskInfo = {
  id: number,
  ownerUser: User,
  assigneeUser: User,
  status: number,
  reason: string

}

// 审批节点信息
export type ApprovalNodeInfo = {
  id : number
  name: string
  nodeType: NodeType
  status: number
  startTime?: Date
  endTime?: Date
  candidateUserList?: User[]
  tasks: ApprovalTaskInfo[]
}

export const getProcessInstanceMyPage = async (params: any) => {
  return await request.get({ url: '/bpm/process-instance/my-page', params })
}

// export const getProcessInstanceMyPage = async (data) => {
//   return await request.post({ url:'/bpm/process-instance/my-page', data:data })
// }


export const getProcessInstanceManagerPage = async (params: any) => {
  return await request.get({ url: '/bpm/process-instance/manager-page', params })
}

export const createProcessInstance = async (data) => {
  return await request.post({ url: '/bpm/process-instance/create', data: data })
}

//创建任务引擎实例

export const createTaskEngineProcessInstance = async (data) => {
  return await request.post({ url: '/bpm/taskEngine/create', data: data })
}

//获取任务引擎分页数据
export const getTaskEngineProcessInstancePage = async (params: any) => {
  return await request.get({ url: '/bpm/taskEngine/list', params})
}

//获取任务引擎对应的流程实例数据
export const getTaskEngineListByTaskEngineId = async (params: any) => {
  return await request.get({ url: '/bpm/taskEngine/list-by-taskEngine-id', params})
}

export const cancelProcessInstanceByStartUser = async (id: number, reason: string) => {
  const data = {
    id: id,
    reason: reason
  }
  return await request.delete({ url: '/bpm/process-instance/cancel-by-start-user', data: data })
}

export const cancelProcessInstanceByAdmin = async (id: number, reason: string) => {
  const data = {
    id: id,
    reason: reason
  }
  return await request.delete({ url: '/bpm/process-instance/cancel-by-admin', data: data })
}

export const getProcessInstance = async (id: string) => {
  return await request.get({ url: '/bpm/process-instance/get?id=' + id })
}

export const getProcessInstanceCopyPage = async (params: any) => {
  return await request.get({ url: '/bpm/process-instance/copy/page', params })
}

// 获取审批详情
export const getApprovalDetail = async (processInstanceId?:string, processDefinitionId?:string) => {
  const param = processInstanceId ? '?processInstanceId='+ processInstanceId : '?processDefinitionId='+ processDefinitionId
  return await request.get({ url: 'bpm/process-instance/get-approval-detail'+ param })
}

// 获取表单字段权限
export const getFormFieldsPermission = async (params: any) => {
  return await request.get({ url: '/bpm/process-instance/get-form-fields-permission', params })
}

//获取新闻列表分页数据
export const getNewsList = async (params: any) => {
  return await request.get({ url: '/news/manage/list', params })
}

//获取新闻单个数据
export const getNewsById = async (params: any) => {
  return await request.get({ url: '/news/manage/get', params })
}

// 更改新闻数据状态
export const updateNewsStatusById = async (data: any) => {
    return await request.put({ url:'/news/manage/update', data })
}

//获取新闻首页数据
export const getNewsHomeData = async (params: any) => {
  try {
    const response = await request.get({ url: '/home/<USER>', params })
    return response
  } catch (error) {
    console.error('获取新闻首页数据出错:', error)
    throw error
  }
}

// 催办任务
export const urgeProcessInstances = async (data: { processInstanceIds: string[], taskEngineId?: string }) => {
  return await request.put({ url: '/bpm/task/urging', data })
}

// 获取任务退回历史
export const getTaskReturnHistory = async (params: any) => {
  return await request.get({ url: '/bpm/task/task-return-history', params })
}

// 获取申请延期历史列表
export const getApplyDelayHistory = async (params: any) => {
  return await request.get({ url: '/bpm/task/apply-delay-history', params })
}

// 根据流程实例的taskDefinitionKey获取任务表单
export const getTaskFormByTaskDefinitionKey = (params: {
  processInstanceId: string
  taskDefinitionKey: string
}) => {
  return request.get({ url: '/bpm/task/get-by-task-definition-key', params })
}

// 根据流程实例ID和任务定义键获取表单数据
export const getFormByProcessInstanceIdAndTaskKey = (processInstanceId: string, taskDefinitionKey: string) => {
  return request.get({
    url: '/bpm/task/latest-complete',
    params: {
      processInstanceId,
      taskDefinitionKey
    }
  })
}

// 获取二级单位列表
export const getSecondaryDepartments = async () => {
  return await request.get({ url: '/system/dept/get-by-user?type=1' })
}

// 获取信访件统计信息
export const getBpmLettersStatistics = async (params?: {
  deptId?: string, // 流程发起人单位ID
  startDate?: string, // 时间开始
  endDate?: string // 时间截至
}) => {
  return await request.get({ url: '/bpm/process-instance/statistics/bpm-letters', params })
}

// 获取线索件统计信息
export const getBpmClueStatistics = async (params?: {
  deptId?: string, // 流程发起人单位ID
  startDate?: string, // 时间开始
  endDate?: string // 时间截至
}) => {
  return await request.get({ url: '/bpm/process-instance/statistics/bpm-clue', params })
}

// 获取立案件统计信息
export const getBpmFilingStatistics = async (params?: {
  deptId?: string, // 流程发起人单位ID
  startDate?: string, // 时间开始
  endDate?: string // 时间截至
}) => {
  return await request.get({ url: '/bpm/process-instance/statistics/bpm-filing', params })
}

// 创建出差报备
export const createTripReport = async (data: {
  startDate: string,
  endDate: string,
  place: string,
  notes: string
}) => {
  return await request.post({ url: '/bpm/trip-report/create', data })
}

// 获取我的报备列表
export const getMyTripReportPage = async (params: {
  pageNo: string,
  pageSize: string
}) => {
  return await request.get({ url: '/bpm/trip-report/my-page', params })
}

// 获取管理报备分页列表
export const getManagerTripReportPage = async (params: {
  pageNo: string,
  pageSize: string,
  deptId: string,
  startDate?: string,
  endDate?: string
}) => {
  return await request.get({ url: '/bpm/trip-report/manager-page', params })
}

// 获取二级数据看板的信访件统计列表
export const getBpmLettersStatisticsPage = async (params: {
  deptId?: string, // 流程发起人单位ID
  startDate?: string, // 时间开始
  endDate?: string, // 时间截至
  sysXfjbfs?: string, // 信访举报方式
  sysXflb?: string, // 信访举报类别
  sysXfjgfb?: string, // 信访结果分布
  sysSfcfj?: string, // 是否重复件
  finishStatus?: string, // 流程状态
  pageNo: string, // 页码(必填）
  pageSize: string // 每页条数（必填）
}) => {
  return await request.get({ url: '/bpm/process-instance/statistics/bpm-letters-page', params })
}

// 获取二级数据看板的线索件统计列表
export const getBpmClueStatisticsPage = async (params: {
  deptId?: string, // 流程发起人单位ID
  startDate?: string, // 时间开始
  endDate?: string, // 时间截至
  sysXsczfs?: string, // 线索处置方式分布
  sysCzjgtj?: string, // 处置结果统计
  sysSfcs?: string, // 是否超时
  finishStatus?: string, // 流程状态
  pageNo: string, // 页码(必填）
  pageSize: string // 每页条数（必填）
}) => {
  return await request.get({ url: '/bpm/process-instance/statistics/bpm-clue-page', params })
}

// 获取二级数据看板的立案件统计列表
export const getBpmFilingStatisticsPage = async (params: {
  deptId?: string, // 流程发起人单位ID
  startDate?: string, // 时间开始
  endDate?: string, // 时间截至
  sysZwcfjg?: string, // 政务处分结果
  sysDjcfjg?: string, // 党纪处分结果
  finishStatus?: string, // 流程状态
  pageNo: string, // 页码(必填）
  pageSize: string // 每页条数（必填）
}) => {
  return await request.get({ url: '/bpm/process-instance/statistics/bpm-filing-page', params })
}

// 任务重新分配
export const taskReassign = async (taskId: string, userId: string) => {
  return await request.get({ 
    url: '/bpm/task/task-reassign', 
    params: { 
      taskId: taskId, 
      userId: userId 
    } 
  })
}
