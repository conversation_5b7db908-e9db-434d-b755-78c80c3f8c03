import {resolve} from 'path'
import type {ConfigEnv, UserConfig} from 'vite'
import {loadEnv} from 'vite'
import {createVitePlugins} from './build/vite'
import {exclude, include} from "./build/vite/optimize"
import { createStyleImportPlugin } from 'vite-plugin-style-import'
// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd()

// 路径查找
function pathResolve(dir: string) {
    return resolve(root, '.', dir)
}

// https://vitejs.dev/config/
export default ({command, mode}: ConfigEnv): UserConfig => {
    let env = {} as any
    const isBuild = command === 'build'
    if (!isBuild) {
        env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
    } else {
        env = loadEnv(mode, root)
    }
    return {
        base: env.VITE_BASE_PATH,
        root: root,
        // 服务端渲染
        server: {
            port: env.VITE_PORT, // 端口号
            host: "0.0.0.0",
            open: env.VITE_OPEN === 'true',
            // 本地跨域代理. 目前注释的原因：暂时没有用途，server 端已经支持跨域
            // proxy: {
            //   ['/admin-api']: {
            //     target: env.VITE_BASE_URL,
            //     ws: false,
            //     changeOrigin: true,
            //     rewrite: (path) => path.replace(new RegExp(`^/admin-api`), ''),
            //   },
            // },
        },
        // 项目使用的vite插件。 单独提取到build/vite/plugin中管理
        plugins: [
            ...createVitePlugins(),
            createStyleImportPlugin({
                libs: [
                    {
                        libraryName: 'bpmn-js-token-simulation',
                        esModule: true,
                        resolveStyle: (name) => {
                            return `bpmn-js-token-simulation/assets/css/${name}.css`
                        }
                    }
                ]
            })
        ],
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData: '@use "@/styles/variables.scss" as *;',
                    javascriptEnabled: true
                }
            },
            modules: {
                localsConvention: 'camelCase'
            }
        },
        resolve: {
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'],
            alias: [
                {
                    find: 'vue-i18n',
                    replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
                },
                {
                    find: /\@\//,
                    replacement: `${pathResolve('src')}/`
                },
                {
                    find: /^bpmn-js-token-simulation/,
                    replacement: pathResolve('node_modules/bpmn-js-token-simulation')
                }
            ]
        },
        build: {
            minify: 'terser',
            outDir: env.VITE_OUT_DIR || 'dist',
            sourcemap: env.VITE_SOURCEMAP === 'true' ? 'inline' : false,
            cssCodeSplit: true, // 启用 CSS 代码分割
            chunkSizeWarningLimit: 1500, // 调整 chunk 大小警告限制
            terserOptions: {
                compress: {
                    drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
                    drop_console: env.VITE_DROP_CONSOLE === 'true'
                }
            },
            rollupOptions: {
                input: {
                    main: resolve(__dirname, 'index.html')
                },
                output: {
                    manualChunks: {
                        'vendor': ['vue', 'vue-router', 'pinia', '@vueuse/core'],
                        'element-plus': ['element-plus'],
                        'echarts': ['echarts'],
                        'bpmn': [
                            'bpmn-js',
                            'diagram-js',
                            'bpmn-js-token-simulation'
                        ]
                    },
                    entryFileNames: 'js/[name].[hash].js',
                    chunkFileNames: 'js/[name].[hash].js',
                    assetFileNames: (assetInfo) => {
                        if (assetInfo?.name == null) return 'assets/[name].[hash].[ext]'
                        const info = assetInfo.name.split('.')
                        let extType = info[info.length - 1]
                        if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
                            extType = 'media'
                        } else if (/\.(png|jpe?g|gif|svg|ico|webp)(\?.*)?$/i.test(assetInfo.name)) {
                            extType = 'images'
                        } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
                            extType = 'fonts'
                        } else if (/\.css$/i.test(assetInfo.name)) {
                            extType = 'css'
                        }
                        return `assets/${extType}/[name].[hash].[ext]`
                    }
                }
            }
        },
        optimizeDeps: {
            include: [
                ...include,
                'bpmn-js',
                'diagram-js',
                'bpmn-js-token-simulation'
            ],
            exclude: [
                ...exclude,
                'bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css',
                'bpmn-js-token-simulation/assets/css/font-awesome.min.css',
                'bpmn-js-token-simulation/assets/css/normalize.css'
            ]
        }
    }
}
