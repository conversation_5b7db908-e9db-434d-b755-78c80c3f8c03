<template>


  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
<!--      <el-form-item label="发起人" prop="startUserId">
        <el-select v-model="queryParams.startUserId" placeholder="请选择发起人" class="!w-240px">
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>-->
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="流程标题" prop="queryVariables.form_title">
        <el-input
          v-model="queryParams.queryVariables.form_title"
          placeholder="请输入流程标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="流程分类" prop="category">
        <el-select
          v-model="queryParams.category"
          placeholder="请选择流程分类"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="流程状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择流程状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="流程标题" align="center" prop="processInstanceExt.processInstanceTitle" min-width="200px" fixed="left" />
      <el-table-column label="流程名称" align="center" prop="name" min-width="200px" fixed="left" />
      <el-table-column
        label="流程分类"
        align="center"
        prop="categoryName"
        min-width="100"
        fixed="left"
      />
      <el-table-column label="流程发起人" align="center" prop="startUser.nickname" width="120" />
      <el-table-column label="发起部门" align="center" width="200">
        <template #default="scope">
          <el-tag 
            v-if="scope.row.startUser?.deptName" 
            type="success" 
            size="small"
          >
            {{ scope.row.startUser.deptName }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="当前处理人" align="center" width="150">
        <template #default="scope">
          <!-- 有处理人且处理人姓名不为空 -->
          <div v-if="scope.row.tasks && scope.row.tasks.length > 0 && scope.row.tasks.some(task => task.assigneeName && task.assigneeName.trim())">
            <el-tag 
              v-for="task in [...new Map(scope.row.tasks?.map(task => [task.assigneeName, task]) || []).values()]" 
              :key="task.id" 
              type="primary" 
              size="small"
              class="mr-1 mb-1"
            >
              {{ task.assigneeName || '-' }}
            </el-tag>
          </div>
          <!-- 流程进行中但没有有效处理人时显示待分配 -->
          <el-tag 
            v-else-if="scope.row.status === 1" 
            type="warning" 
            size="small"
          >
            待分配
          </el-tag>
          <!-- 其他情况 -->
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="当前处理组织" align="center" width="150">
        <template #default="scope">
          <div v-if="scope.row.tasks && scope.row.tasks.length > 0 && scope.row.tasks.some(task => task.deptName && task.deptName.trim())">
            <el-tag 
              v-for="task in [...new Map(scope.row.tasks?.map(task => [task.deptName, task]) || []).values()]" 
              :key="task.id" 
              type="info" 
              size="small"
              class="mr-1 mb-1"
            >
              {{ task.deptName || '-' }}
            </el-tag>
          </div>
          <!-- 流程进行中但没有有效处理部门时显示待分配 -->
          <el-tag 
            v-else-if="scope.row.status === 1" 
            type="warning" 
            size="small"
          >
            待分配
          </el-tag>
          <!-- 其他情况 -->
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="流程状态" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="发起时间"
        align="center"
        prop="startTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
        :formatter="dateFormatter"
      />
      <el-table-column align="center" label="耗时" prop="durationInMillis" width="169">
        <template #default="scope">
          {{ calculateDuration(scope.row) }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="当前审批任务" align="center" prop="tasks" min-width="120px">
        <template #default="scope">
          <el-button type="primary" v-for="task in [...new Map(scope.row.tasks?.map(task => [task.name, task]) || []).values()]" :key="task.id" link>
            <span>{{ task.name }}</span>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="流程编号" align="center" prop="id" min-width="320px" /> -->
      <el-table-column label="操作" align="center" fixed="right" width="240">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            link
            type="primary"
            v-if="scope.row.status === 1"
            @click="handleCancel(scope.row)"
          >
            取消
          </el-button>
          <el-button
            link
            type="primary"
            v-if="scope.row.status === 1"
            @click="handleReassign(scope.row)"
          >
            重新分配
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 重新分配对话框 -->
  <el-dialog
    title="重新分配"
    v-model="reassignDialogVisible"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="mb-4">
      <div class="text-base text-gray-600 mb-2">
        当前任务：{{ currentProcessInstance?.name }}
      </div>
      <div class="text-base text-gray-600 mb-2">
        当前处理人：{{ getCurrentAssigneeName() }}
      </div>
      <div class="text-base text-gray-600 mb-4">
        当前处理单位：{{ getCurrentTaskDeptName() }}
      </div>
      
      <el-form :model="userQueryParams" :inline="true" label-width="100px">
        <el-form-item label="组织名称">
          <el-select
            v-model="selectedDeptId"
            placeholder="请选择组织"
            clearable
            style="width: 300px"
            @change="handleDeptChange"
          >
            <el-option
              v-for="dept in deptList"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getUserList" :disabled="!selectedDeptId">
            <Icon icon="ep:search" class="mr-5px" /> 搜索
          </el-button>
          <el-button @click="resetUserQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table
      v-loading="userLoading"
      :data="userList"
      max-height="400"
    >
      <el-table-column width="55" align="center">
        <template #default="scope">
          <el-radio 
            v-model="selectedUserId" 
            :label="scope.row.id"
            @change="handleUserRadioChange(scope.row)"
          >
            &nbsp;
          </el-radio>
        </template>
      </el-table-column>
      <el-table-column label="账号" prop="username" width="120" />
      <el-table-column label="用户名" prop="nickname" width="120" />
      <el-table-column label="部门名称" prop="deptName" width="150" />
<!--      <el-table-column label="邮箱" prop="email" width="180" />-->
      <el-table-column label="手机号" prop="mobile" width="120" />
      <el-table-column label="状态" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
            {{ scope.row.status === 0 ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 用户分页 -->
    <div class="mt-4">
      <Pagination
        :total="userTotal"
        v-model:page="userQueryParams.pageNo"
        v-model:limit="userQueryParams.pageSize"
        @pagination="getUserList"
      />
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="reassignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReassign" :disabled="!selectedUser">
          确定分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter, formatPast2 } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { CategoryApi, categoryRoute } from '@/api/bpm/category'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { cancelProcessInstanceByAdmin } from '@/api/bpm/processInstance'
import { useUserStore } from "@/store/modules/user";
import { cloneDeep } from 'lodash-es'

// 它和【我的流程】的差异是，该菜单可以看全部的流程实例
defineOptions({ name: 'BpmProcessInstanceManager' })

const router = useRouter() // 路由
const message = useMessage() // 消息弹窗
const userStore = useUserStore() //获取当前用户信息
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  startUserId: undefined,
  name: '',
  processDefinitionId: undefined,
  category: undefined,
  status: undefined,
  createTime: [],
  queryVariables: {
    form_title: ''
  }
})
const queryFormRef = ref() // 搜索的表单
const categoryList = ref([]) // 流程分类列表
const userList = ref<any[]>([]) // 用户列表
const loginUserInfo = ref({})

// 重新分配相关变量
const reassignDialogVisible = ref(false) // 重新分配对话框显示状态
const userLoading = ref(false) // 用户列表加载状态
const userTotal = ref(0) // 用户总数
const selectedUser = ref(null) // 选中的用户（单选）
const selectedUserId = ref(null) // 选中的用户ID
const selectedDeptId = ref(undefined) // 选中的部门ID
const deptList = ref([]) // 部门列表
const currentProcessInstance = ref(null) // 当前要重新分配的流程实例
const userQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  deptId: undefined
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.startUserId = loginUserInfo.value.id
    // 将queryVariable中的对象转化为json字符串便于后端处理
    const query = cloneDeep(queryParams)
    
    // 过滤掉空值的queryVariables字段
    const filteredQueryVariables = {}
    if (query.queryVariables.form_title && query.queryVariables.form_title.trim()) {
      filteredQueryVariables.form_title = query.queryVariables.form_title.trim()
    }
    
    // 如果有有效的查询变量，则转换为JSON字符串；否则不传递该字段
    if (Object.keys(filteredQueryVariables).length > 0) {
      query.queryVariables = JSON.stringify(filteredQueryVariables)
    } else {
      delete query.queryVariables
    }
    
    const data = await ProcessInstanceApi.getProcessInstanceManagerPage(query)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查看详情 */
const handleDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消按钮操作 */
const handleCancel = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstanceByAdmin(row.id, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 重新分配按钮操作 */
const handleReassign = (row) => {
  currentProcessInstance.value = row
  reassignDialogVisible.value = true
  
  // 获取当前流程的任务部门ID
  let deptId = undefined
  if (row.tasks && row.tasks.length > 0) {
    // 使用第一个任务的deptId
    deptId = row.tasks[0].deptId
  }
  
  // 设置默认选中的部门
  selectedDeptId.value = deptId
  
  // 重置用户查询参数
  userQueryParams.pageNo = 1
  userQueryParams.pageSize = 10
  userQueryParams.deptId = deptId
  selectedUser.value = null
  selectedUserId.value = null // 重置选中的用户ID
  
  // 清空用户列表和总数
  userList.value = []
  userTotal.value = 0
  
  // 如果有部门ID，则获取用户列表；否则保持空状态
  if (deptId) {
    getUserList()
  }
}

/** 获取用户列表 */
const getUserList = async () => {
  userLoading.value = true
  try {
    const data = await UserApi.getUserPage(userQueryParams)
    userList.value = data.list
    userTotal.value = data.total
  } finally {
    userLoading.value = false
  }
}

/** 重置用户查询 */
const resetUserQuery = () => {
  // 获取当前流程的任务部门ID
  let deptId = undefined
  if (currentProcessInstance.value?.tasks && currentProcessInstance.value.tasks.length > 0) {
    deptId = currentProcessInstance.value.tasks[0].deptId
  }
  
  selectedDeptId.value = deptId
  userQueryParams.deptId = deptId
  userQueryParams.pageNo = 1
  selectedUser.value = null
  selectedUserId.value = null // 重置选中的用户ID
  
  // 清空用户列表和总数
  userList.value = []
  userTotal.value = 0
  
  // 如果有部门ID，则获取用户列表；否则保持空状态
  if (deptId) {
    getUserList()
  }
}

/** 用户单选变化 */
const handleUserRadioChange = (user) => {
  selectedUser.value = user
  selectedUserId.value = user.id
}

/** 用户选择变化（保留兼容性） */
const handleUserSelectionChange = (selection) => {
  selectedUser.value = selection
  selectedUserId.value = selection?.id || null
}

/** 部门选择变化 */
const handleDeptChange = () => {
  // 当部门选择变化时，重置用户列表和分页
  userQueryParams.deptId = selectedDeptId.value
  userQueryParams.pageNo = 1
  selectedUser.value = null
  selectedUserId.value = null // 重置选中的用户ID
  
  // 清空用户列表和总数
  userList.value = []
  userTotal.value = 0
  
  // 如果选择了部门，则获取用户列表；否则保持空状态
  if (selectedDeptId.value) {
    getUserList()
  }
}

/** 获取部门列表 */
const getDeptList = async () => {
  const data = await DeptApi.getSimpleDeptList()
  deptList.value = data
}

/** 获取当前流程任务的部门名称 */
const getCurrentTaskDeptName = () => {
  if (currentProcessInstance.value?.tasks && currentProcessInstance.value.tasks.length > 0) {
    const task = currentProcessInstance.value.tasks[0]
    return task.deptName || '-'
  }
  return '-'
}

/** 获取当前流程任务的处理人名称 */
const getCurrentAssigneeName = () => {
  if (currentProcessInstance.value?.tasks && currentProcessInstance.value.tasks.length > 0) {
    const task = currentProcessInstance.value.tasks[0]
    return task.assigneeName || '-'
  }
  return '-'
}

/** 计算流程耗时 */
const calculateDuration = (row) => {
  // 如果有现成的耗时数据，直接使用
  if (row.durationInMillis > 0) {
    return formatPast2(row.durationInMillis)
  }
  
  // 如果流程还在进行中（有发起时间但没有结束时间），计算当前耗时
  if (row.startTime && !row.endTime) {
    const startTime = new Date(row.startTime).getTime()
    const currentTime = Date.now()
    return formatPast2(currentTime - startTime)
  }
  
  // 其他情况返回 '-'
  return '-'
}

/** 确认重新分配 */
const confirmReassign = async () => {
  if (selectedUser.value === null) {
    message.warning('请选择要分配的用户')
    return
  }
  
  // 获取当前流程的任务ID - 使用tasks[0].id
  if (!currentProcessInstance.value?.tasks || currentProcessInstance.value.tasks.length === 0) {
    message.error('当前流程没有可分配的任务')
    return
  }
  
  // 使用数据返回的tasks[0].id作为taskId
  const taskId = currentProcessInstance.value.tasks[0].id
  const userId = selectedUser.value.id
  
  // 调试信息：确认taskId和userId的值
  console.log('重新分配参数:', {
    taskId: taskId,
    userId: userId,
    currentTask: currentProcessInstance.value.tasks[0],
    selectedUser: selectedUser.value
  })
  
  try {
    // 调用重新分配API，传递taskId和userId
    await ProcessInstanceApi.taskReassign(taskId, userId)
    
    message.success('重新分配成功')
    reassignDialogVisible.value = false
    selectedUser.value = null
    selectedUserId.value = null
    await getList()
  } catch (error) {
    message.error('重新分配失败')
  }
}

const getCategory=async () => {
  categoryList.value = await CategoryApi.getCategorySimpleList()
}
const getLoginUserInfo=async () => {
  loginUserInfo.value = userStore.getUser()
}
/** 激活时 **/
onActivated(() => {
  getList()
})

/** 初始化 **/
onMounted(async () => {
  await getCategory()
  await getLoginUserInfo
  await getList()
  await getDeptList() // 获取部门列表
 // categoryList.value = await CategoryApi.getCategorySimpleList()
  //userList.value = await UserApi.getSimpleUserList()
})
</script>
