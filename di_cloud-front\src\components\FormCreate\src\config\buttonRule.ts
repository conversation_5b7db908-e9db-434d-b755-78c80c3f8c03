export const buttonRule = {
  // 插入菜单位置
  menu: 'aide',
  // 图标
  icon: 'icon-button',
  // 名称
  label: '自定义按钮',
  // id，唯一!
  name: 'MyButton',
  // 是否可以操作
  mask: true,
  // 定义组件的事件
  event: ['click'],
  // 定义组件的渲染规则
  rule({  }) {
    return {
      type: 'MyButton',
      props: {},
    };
  },
  // 自定义组件的属性配置
  props(_, {  }) {
    return [
      {
        // 修改 rule.props.label
        type: 'input',
        title: '内容',
        field: 'label',
      },
      {
        // 修改 rule.props.size
        type: 'select',
        title: '尺寸',
        field: 'size',
        options: [
          { label: 'large', value: 'large' },
          { label: 'default', value: 'default' },
          { label: 'small', value: 'small' },
        ],
      },
    ];
  },
};

