<template>
  <div class="process-timeline-container">
    <div class="process-timeline-header">
      <span class="header-icon"><Icon icon="ep:files" /></span>
      <span class="header-title">流程记录</span>
    </div>
    
    <el-col :offset="4" :span="16">
      <div class="block timeline-scroll-container">
        <el-timeline>
          <el-timeline-item
            v-if="processInstance.endTime"
            :type="getProcessInstanceTimelineItemType(processInstance)"
            :size="'large'"
            :hollow="true"
            class="timeline-end-node"
          >
            <div class="timeline-content-wrapper timeline-end">
              <p class="timeline-title">
                <Icon icon="ep:finished" class="status-icon" />
                结束流程：在 {{ formatDate(processInstance?.endTime) }} 结束
                <dict-tag
                  :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS"
                  :value="processInstance.status"
                />
              </p>
            </div>
          </el-timeline-item>
          
          <el-timeline-item
            v-for="(item, index) in tasks"
            :key="index"
            :type="getTaskTimelineItemType(item)"
            :size="'large'"
            :hollow="false"
            class="timeline-task-node"
          >
            <div class="timeline-content-wrapper">
              <div class="task-header-container">
                <div class="timeline-title">
                  <Icon :icon="getTaskIcon(item)" class="task-icon" :class="getTaskIconClass(item)" />
                  <span class="task-name">{{ item.name }}</span>
                  <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="item.status" class="status-tag" />
                  
                  <div class="action-buttons">
                    <el-button
                      v-if="item.formId > 0 && item.status !== 5 && ![0, 1].includes(item.status)"
                      @click="toggleFormVisibility(index)"
                      size="small"
                      type="primary"
                      plain
                      class="action-button"
                    >
                      <Icon icon="ep:document" /> {{ formVisibility[index] ? '隐藏表单' : '查看表单' }}
                    </el-button>
                    <!-- <el-button
                      size="small"
                      type="warning"
                      plain
                      v-if="item.status === 5"
                      @click="handleReturnHistoryDetail(item)"
                      class="action-button"
                    >
                      <Icon icon="ep:info-filled" /> 退回详情
                    </el-button> -->
                  </div>
                </div>
              </div>
              
              <el-card 
                :body-style="{ padding: '20px' }" 
                class="task-card"
                :class="{ 
                  'completed-task': item.status === 2,
                  'rejected-task': item.status === 3,
                  'canceled-task': item.status === 4,
                  'returned-task': item.status === 5,
                  'approving-task': [0, 1, 6, 7].includes(item.status)
                }"
                shadow="hover"
              >
                <div class="task-status-badge" :class="`status-${item.status}`">
                  {{ getTaskStatusText(item.status) }}
                </div>
                
                <div class="task-info-container">
                  <div class="task-basic-info">
                    <div v-if="item.assigneeUser" class="info-item horizontal">
                      <span class="info-label"><Icon icon="ep:user" /> 处理人：</span>
                      <div class="assignee-info horizontal">
                        <span class="info-value">{{ item.assigneeUser.nickname }}</span>
                        <el-tag size="small" effect="plain" type="info" class="dept-tag">{{ item.assigneeUser.deptName }}</el-tag>
                      </div>
                    </div>
                    
                    <!-- 待审批任务的处理人显示 -->
                    <div v-else-if="[0, 1].includes(item.status)" class="info-item horizontal">
                      <span class="info-label"><Icon icon="ep:user" /> 处理人：</span>
                      <div class="assignee-info horizontal">
                        <template v-if="item.ownerUser">
                          <span class="info-value">{{ item.ownerUser.nickname }}</span>
                          <el-tag size="small" effect="plain" type="info" class="dept-tag">{{ item.ownerUser.deptName }}</el-tag>
                        </template>
                        <template v-else-if="item.assignee">
                          <span class="info-value">{{ item.assignee }}</span>
                        </template>
                        <template v-else-if="item.candidateUsers && item.candidateUsers.length > 0">
                          <el-tag size="small" type="warning" class="candidate-tag">候选处理人</el-tag>
                          <span class="info-value candidate-list">
                            <span v-for="(user, uIndex) in item.candidateUsers" :key="uIndex">
                              {{ user.nickname || user.username || user }}
                              <span v-if="uIndex < item.candidateUsers.length - 1">,</span>
                            </span>
                          </span>
                        </template>
                        <template v-else>
                          <el-tag size="small" type="info">待分配</el-tag>
                        </template>
                      </div>
                    </div>
                    
                    <div class="info-item time-info horizontal">
                      <div class="time-item-group">
                        <div class="time-item" v-if="item.createTime">
                          <span class="info-label"><Icon icon="ep:calendar" /> 创建时间：</span>
                          <span class="info-value time-value">{{ formatDate(item?.createTime) }}</span>
                        </div>
                        
                        <div class="time-item" v-if="item.endTime">
                          <span class="info-label"><Icon icon="ep:calendar-check" /> 处理时间：</span>
                          <span class="info-value time-value">{{ formatDate(item?.endTime) }}</span>
                        </div>
                        
                        <div class="time-item" v-if="item.durationInMillis">
                          <span class="info-label"><Icon icon="ep:timer" /> 耗时：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                          <span class="info-value time-value">{{ formatPast2(item?.durationInMillis) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div v-if="item.reason" class="reason-container" :class="{'reason-reject': item.status === 5, 'reason-approve': item.status !== 5}">
                    <div class="reason-header">
                      <Icon :icon="item.status === 5 ? 'ep:warning' : 'ep:chat-dot-round'" class="reason-icon" :class="{'reject-icon': item.status === 5}" />
                      <span v-if="item.status === 5" class="reject-reason">退回理由：</span>
                      <span v-else class="approve-reason">审批意见：</span>
                    </div>
                    <div class="reason-content" :class="{'reject-content': item.status === 5}">
                      {{ item.reason }}
                    </div>
                  </div>
                  
                  <!-- 操作记录区域 -->
                  <div v-if="item.commentList && item.commentList.length > 0" class="comments-container">
                    <div class="comments-header">
                      <Icon icon="ep:chat-dot-square" class="comments-icon" />
                      <span class="comments-title">操作记录</span>
                    </div>
                    <div class="comments-content">
                      <div v-for="(comment, cIndex) in item.commentList" :key="cIndex" class="comment-item">
                        <div class="comment-message">
                          <Icon icon="ep:chat-dot-round" class="comment-icon" />
                          <span>{{ comment.message || comment.fullMessage }}</span>
                        </div>
                        <div class="comment-time">{{ formatDate(comment.time) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 表单内容区域 - 直接显示，退回任务和待审批任务不显示表单 -->
                <div v-if="item.formId > 0 && formVisibility[index] && item.status !== 5 && ![0, 1].includes(item.status)" class="task-form-container">
                  <div class="form-header">
                    <Icon icon="ep:document" class="form-icon" />
                    <span class="form-title">表单信息</span>
                  </div>
                  <div class="task-form-content">
                    <form-create
                      v-model="taskForms[index].value"
                      :ref="setFormRef(index)"
                      :option="taskForms[index].option"
                      :rule="taskForms[index].rule"
                    />
                  </div>
                </div>
                
                <!-- 子任务区域 - 直接显示（使用递归方式） -->
                <div v-if="!isEmpty(item.children)" class="sub-tasks-container">
                  <div class="subtask-header">
                    <Icon icon="ep:share" class="subtask-icon" />
                    <span class="subtask-title">子任务信息</span>
                  </div>
                  <div class="sub-tasks-content">
                    <RecursiveTaskList 
                      :tasks="item.children" 
                      :level="1"
                    />
                  </div>
                </div>
              </el-card>
            </div>
          </el-timeline-item>
          
          <el-timeline-item type="success" :size="'large'" :hollow="true" class="timeline-start-node">
            <div class="timeline-content-wrapper timeline-start">
              <p class="timeline-title">
                <Icon icon="ep:video-play" class="start-icon" />
                发起流程：【{{ processInstance.startUser?.nickname }}】在
                {{ formatDate(processInstance?.startTime) }} 发起【 {{ processInstance.name }} 】流程
              </p>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-col>
    
    <!-- 弹窗和相关组件 -->
    <div class="hidden-dialogs">
      <!-- 弹窗：表单 - 保留为兼容性考虑 -->
      <Dialog title="表单详情" v-model="taskFormVisible" width="50%">
        <form-create
          ref="fApi"
          v-model="taskForm.value"
          :option="taskForm.option"
          :rule="taskForm.rule"
        />
      </Dialog>
      <!-- 弹窗：退回详情 -->
      <TaskReturnHistoryDetail ref="taskReturnHistoryDetailRef" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { formatDate, formatPast2 } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { DICT_TYPE } from '@/utils/dict'
import { isEmpty } from '@/utils/is'
import TaskReturnHistoryDetail from './dialog/TaskReturnHistoryDetail.vue'
import type { ApiAttrs } from '@form-create/element-ui/types/config'
import { setConfAndFields2 } from '@/utils/formCreate'
import { ref, watch, nextTick, h } from 'vue'
import RecursiveTaskList from './components/RecursiveTaskList.vue'

defineOptions({ name: 'BpmProcessInstanceTaskList' })

const props = defineProps({
  loading: propTypes.bool, // 是否加载中
  processInstance: propTypes.object, // 流程实例
  tasks: propTypes.arrayOf(propTypes.object) // 流程任务的数组
})

/** 获得流程实例对应的颜色 */
const getProcessInstanceTimelineItemType = (item: any) => {
  if (item.status === 2) {
    return 'success'
  }
  if (item.status === 3) {
    return 'danger'
  }
  if (item.status === 4) {
    return 'warning'
  }
  return ''
}

/** 获得任务对应的颜色 */
const getTaskTimelineItemType = (item: any) => {
  if ([0, 1, 6, 7].includes(item.status)) {
    return 'primary'
  }
  if (item.status === 2) {
    return 'success'
  }
  if (item.status === 3) {
    return 'danger'
  }
  if (item.status === 4) {
    return 'info'
  }
  if (item.status === 5) {
    return 'warning'
  }
  return ''
}

/** 表单相关 */
const fApi = ref<ApiAttrs>() // form-create 的 API 操作类
const taskForm = ref({
  rule: [],
  option: {},
  value: {}
}) // 流程任务的表单详情
const taskFormVisible = ref(false)

// 添加多个表单引用和表单可见性控制
const taskForms = ref<any[]>([])
const formVisibility = ref<boolean[]>([])
const formRefs = ref<any[]>([])

// 设置表单引用
const setFormRef = (index: number) => (el: any) => {
  formRefs.value[index] = el
}

// 监视tasks的变化，初始化表单数据
watch(() => props.tasks, (newTasks) => {
  if (newTasks && newTasks.length > 0) {
    // 初始化表单可见性数组，根据任务状态决定是否默认显示
    // 1. 审批中(status=0,1)的任务不显示表单
    // 2. 退回(status=5)的任务不显示表单
    // 3. 其他状态默认显示
    formVisibility.value = newTasks.map(task => ![0, 1, 5].includes(task.status))
    
    taskForms.value = new Array(newTasks.length).fill(null).map(() => ({
      rule: [],
      option: {},
      value: {}
    }))
    
    // 预加载所有表单数据，退回状态的任务也加载表单以保持索引一致
    newTasks.forEach((task, index) => {
      if (task.formId > 0) {
        setConfAndFields2(taskForms.value[index], task.formConf, task.formFields, task.formVariables)
      }
    })
    
    // 等待表单加载完成后，应用禁用和隐藏按钮设置
    nextTick(() => {
      // 延迟执行确保表单已渲染
      setTimeout(() => {
        formVisibility.value.forEach((isVisible, idx) => {
          // 只处理非退回状态的表单
          if (isVisible && newTasks[idx].status !== 5 && formRefs.value[idx]?.fapi) {
            formRefs.value[idx].fapi.btn.show(false)
            formRefs.value[idx].fapi.resetBtn.show(false)
            formRefs.value[idx].fapi.disabled(true)
          }
        })
      }, 100)
    })
  }
}, { immediate: true, deep: true })

// 切换表单可见性
const toggleFormVisibility = async (index: number) => {
  // 获取当前任务
  const currentTask = props.tasks[index]
  
  // 如果是退回状态的任务，不执行任何操作
  if (currentTask.status === 5) {
    console.log('退回状态的任务不能显示表单')
    return
  }
  
  // 切换表单可见性状态
  formVisibility.value[index] = !formVisibility.value[index]
  
  // 如果切换为可见，确保表单是只读的
  if (formVisibility.value[index]) {
    await nextTick()

    if (formRefs.value[index]?.fapi) {
      formRefs.value[index].fapi.btn.show(false)
      formRefs.value[index].fapi.resetBtn.show(false)
      formRefs.value[index].fapi.disabled(true)
      console.log('表单', index, '已设置为只读模式，并隐藏按钮')
    }
  }
}

// 修改保留的handleFormDetail方法以兼容原有功能
const handleFormDetail = async (row) => {
  // 设置表单
  setConfAndFields2(taskForm, row.formConf, row.formFields, row.formVariables)
  // 弹窗打开
  taskFormVisible.value = true
  // 隐藏提交、重置按钮，设置禁用只读
  await nextTick()
  // 添加延时确保DOM已更新

    fApi.value?.fapi?.btn.show(false)
    fApi.value?.fapi?.resetBtn.show(false)
    fApi.value?.fapi?.disabled(true)

}

/** 刷新数据 */
const emit = defineEmits(['refresh']) // 定义 success 事件，用于操作成功后的回调
const refresh = () => {
  emit('refresh')
}

/** 查看退回详情 */
const taskReturnHistoryDetailRef = ref()
const handleReturnHistoryDetail = (item) => {
  // 增加日志，便于调试
  console.log('打开退回详情，任务信息:', item)
  // 传递整个任务对象，包含reason字段
  taskReturnHistoryDetailRef.value.open(item)
}

/** 获得任务对应的图标 */
const getTaskIcon = (item: any) => {
  if (item.status === 2) {
    return 'ep:check-circle-filled'
  }
  if (item.status === 3) {
    return 'ep:close-circle-filled'
  }
  if (item.status === 4) {
    return 'ep:close-bold'
  }
  if (item.status === 5) {
    return 'ep:turn-off'
  }
  if ([0, 1].includes(item.status)) {
    return 'ep:loading'
  }
  if ([6, 7].includes(item.status)) {
    return 'ep:share'
  }
  return 'ep:document'
}

/** 获得任务图标的CSS类 */
const getTaskIconClass = (item: any) => {
  if (item.status === 2) {
    return 'success-icon'
  }
  if (item.status === 3) {
    return 'rejected-icon'
  }
  if (item.status === 4) {
    return 'canceled-icon'
  }
  if (item.status === 5) {
    return 'returned-icon'
  }
  if ([0, 1].includes(item.status)) {
    return 'approving-icon'
  }
  return ''
}

/** 获得任务状态文字 */
const getTaskStatusText = (status: number) => {
  switch (status) {
    case 0:
    case 1:
      return '审批中'
    case 2:
      return '已完成'
    case 3:
      return '已拒绝'
    case 4:
      return '已取消'
    case 5:
      return '已退回'
    case 6:
    case 7:
      return '已转交'
    default:
      return '未知状态'
  }
}
</script>

<style lang="scss" scoped>
.process-timeline-container {
  padding-top: 10px;
  margin-bottom: 20px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.process-timeline-header {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  padding: 8px 0;
  margin-bottom: 5px;
  border-bottom: 1px solid #ebeef5;
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 10;
}

.header-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409EFF;
}

.header-title {
  font-size: 14px;
  letter-spacing: 0.5px;
}

.timeline-scroll-container {
  max-height: 95vh;
  overflow-y: auto;
  padding-right: 10px;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 4px;
    
    &:hover {
      background: #909399;
    }
  }
  
  /* Firefox滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #c0c4cc #f1f1f1;
}

.timeline-content-wrapper {
  padding: 5px 0;
  position: relative;
  
  &.timeline-start {
    padding-bottom: 15px;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50%;
      height: 1px;
      background: linear-gradient(90deg, rgba(103, 194, 58, 0.3), rgba(103, 194, 58, 0));
    }
  }
  
  &.timeline-end {
    padding-top: 15px;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 50%;
      height: 1px;
      background: linear-gradient(90deg, rgba(64, 158, 255, 0.3), rgba(64, 158, 255, 0));
    }
  }
}

.timeline-title {
  font-weight: 700;
  margin-bottom: 15px;
  font-size: 15px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  color: #2c3e50;
}

.task-name {
  font-weight: 700;
  margin-right: 8px;
}

.task-header-container {
  margin-bottom: 12px;
}

.task-icon {
  margin-right: 8px;
  vertical-align: middle;
  font-size: 18px;
  
  &.success-icon {
    color: #67c23a;
  }
  
  &.rejected-icon {
    color: #f56c6c;
  }
  
  &.canceled-icon {
    color: #909399;
  }
  
  &.returned-icon {
    color: #e6a23c;
  }
  
  &.approving-icon {
    color: #409EFF;
  }
}

.status-icon {
  color: #67c23a;
  margin-right: 8px;
  vertical-align: middle;
  font-size: 18px;
}

.start-icon {
  color: #67c23a;
  margin-right: 8px;
  vertical-align: middle;
  font-size: 18px;
}

.status-tag {
  margin-left: 8px;
  vertical-align: middle;
}

.action-buttons {
  margin-left: auto;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-button {
  margin-left: 5px;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}

.task-card {
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;
  
  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    transition: all 0.3s ease;
  }
  
  &.completed-task::before {
    background-color: #67c23a;
  }
  
  &.rejected-task::before {
    background-color: #f56c6c;
  }
  
  &.canceled-task::before {
    background-color: #909399;
  }
  
  &.returned-task::before {
    background-color: #e6a23c;
  }
  
  &.approving-task::before {
    background-color: #409EFF;
  }
}

.task-status-badge {
  position: absolute;
  top: 20px;
  right: -30px;
  padding: 5px 30px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  transform: rotate(45deg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  
  &.status-0, &.status-1, &.status-6, &.status-7 {
    background-color: #409EFF;
  }
  
  &.status-2 {
    background-color: #67c23a;
  }
  
  &.status-3 {
    background-color: #f56c6c;
  }
  
  &.status-4 {
    background-color: #909399;
  }
  
  &.status-5 {
    background-color: #e6a23c;
  }
}

.task-info-container {
  padding-bottom: 15px;
  position: relative;
}

.task-basic-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  
  &.horizontal {
    flex-direction: row;
    align-items: center;
  }
}

.time-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.time-item-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 600;
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
  margin-right: 5px;
}

.info-value {
  color: #2c3e50;
  font-size: 13px;
  
  &.time-value {
    color: #8a909c;
  }
}

.dept-tag {
  margin-left: 8px;
}

.candidate-tag {
  margin-right: 5px;
}

.candidate-list {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 3px;
}

.reason-container {
  background-color: #f8f9fa;
  border-radius: 5px;
  padding: 12px;
  margin-top: 10px;
  border-left: 3px solid #409EFF;
  
  &.reason-reject {
    background-color: #fff8f6;
    border-left-color: #f56c6c;
  }
}

.reason-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.reason-icon {
  font-size: 16px;
  color: #409EFF;
  margin-right: 5px;
  
  &.reject-icon {
    color: #f56c6c;
  }
}

.reject-reason {
  font-weight: 600;
  color: #f56c6c;
}

.approve-reason {
  font-weight: 600;
  color: #409EFF;
}

.reason-content {
  padding: 5px 0;
  color: #2c3e50;
  font-size: 13px;
  line-height: 1.6;
  
  &.reject-content {
    color: #f56c6c;
  }
}

/* 评论区样式 */
.comments-container {
  background-color: #f8f9fa;
  border-radius: 5px;
  padding: 12px;
  margin-top: 15px;
  border-left: 3px solid #67c23a;
}

.comments-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.comments-icon {
  font-size: 16px;
  color: #67c23a;
  margin-right: 5px;
}

.comments-title {
  font-weight: 600;
  color: #67c23a;
}

.comments-content {
  padding: 0;
}

.comment-item {
  padding: 8px 0;
  border-bottom: 1px dashed #e1e4e8;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 5px;
  font-size: 13px;
  line-height: 1.5;
}

.comment-icon {
  font-size: 14px;
  color: #909399;
  margin-right: 5px;
  margin-top: 2px;
}

.comment-time {
  font-size: 12px;
  color: #909399;
  padding-left: 20px;
}

.task-form-container {
  margin-top: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  padding: 15px;
  border-left: 3px solid #409EFF;
}

.form-header, .subtask-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.form-icon, .subtask-icon {
  font-size: 18px;
  color: #409EFF;
  margin-right: 10px;
}

.form-title, .subtask-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.task-form-content {
  margin-top: 15px;
}

.sub-tasks-container {
  margin-top: 20px;
  padding: 20px;
  background-color: #f0f7ff;
  border-radius: 8px;
  border: 1px solid #d9ecff;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.03);
  max-width: 850px;
  margin-left: auto;
  margin-right: auto;
}

.sub-tasks-content {
  margin-top: 15px;
}

.timeline-start-node, .timeline-end-node, .timeline-task-node {
  :deep(.el-timeline-item__node) {
    transition: all 0.3s ease;
  }
  
  &:hover {
    :deep(.el-timeline-item__node) {
      transform: scale(1.2);
    }
  }
}

.timeline-start-node {
  :deep(.el-timeline-item__node) {
    background-color: #67c23a;
  }
}

.timeline-end-node {
  :deep(.el-timeline-item__node) {
    border-color: #409EFF;
  }
}

.ml-10px {
  margin-left: 10px;
}

.hidden-dialogs {
  display: none;
}
</style>

