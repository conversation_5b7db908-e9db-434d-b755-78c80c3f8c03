<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">外出报备</span>
          <el-button type="primary" @click="goToMyReports">
            我的报备
          </el-button>
        </div>
      </template>
      
      <el-form
        ref="reportFormRef"
        :model="reportForm"
        :rules="rules"
        label-width="120px"
        class="report-form"
      >
        <!-- 外出时间 -->
        <el-form-item label="外出时间" prop="timeRange" required>
          <el-date-picker
            v-model="reportForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            class="time-range-picker"
          />
        </el-form-item>
        
        <!-- 外出地点 -->
        <el-form-item label="外出地点" prop="location" required>
          <el-input
            v-model="reportForm.location"
            placeholder="请输入外出地点"
            clearable
          />
        </el-form-item>
        
        <!-- 外出事由 -->
        <el-form-item label="外出事由" prop="reason" required>
          <el-input
            v-model="reportForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入外出事由"
            clearable
          />
        </el-form-item>
        
        <!-- 表单操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { createTripReport } from '@/api/bpm/processInstance'

// 路由
const router = useRouter()

// 类型定义
interface ReportForm {
  timeRange: string[]
  location: string
  reason: string
}

// 表单数据
const reportFormRef = ref<FormInstance>()
const reportForm = reactive<ReportForm>({
  timeRange: [],  // 时间范围
  location: '',   // 外出地点
  reason: '',     // 外出事由
})

// 表单验证规则
const rules = reactive<FormRules>({
  timeRange: [
    { required: true, message: '请选择外出时间范围', trigger: 'change' }
  ],
  location: [
    { required: true, message: '请输入外出地点', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入外出事由', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ]
})

// 提交表单
const submitForm = async () => {
  if (!reportFormRef.value) return
  
  await reportFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        // 处理表单数据
        const formData = {
          startDate: reportForm.timeRange[0],
          endDate: reportForm.timeRange[1],
          place: reportForm.location,
          notes: reportForm.reason
        }
        
        // 调用接口提交数据
        const res = await createTripReport(formData)
        
        if (res) {
          ElMessage.success('报备提交成功！')
          
          // 重置表单
          resetForm()
          
          // 跳转到我的报备页面
          goToMyReports()
        } else {
          ElMessage.error('报备提交失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('报备提交失败，请稍后重试')
      }
    } else {
      console.log('表单验证失败:', fields)
      ElMessage.warning('表单填写有误，请检查')
    }
  })
}

// 重置表单
const resetForm = () => {
  if (reportFormRef.value) {
    reportFormRef.value.resetFields()
  }
}

// 跳转到我的报备页面
const goToMyReports = () => {
  router.push('/tr/leave/my')
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: bold;
  font-size: 16px;
}

.report-form {
  max-width: 800px;
  margin: 0 auto;
}

.time-range-picker {
  width: 100%;
}
</style>
