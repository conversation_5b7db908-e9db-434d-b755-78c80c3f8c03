<template>
  <el-card shadow="never">
    <template #header>
      <div class="h-3 flex justify-between">
        <span>{{ t('workplace.shortcutOperation') }}</span>
      </div>
    </template>
    <el-skeleton :loading="loading" animated>
      <el-row>
        <el-col v-for="item in shortcut" :key="`team-${item.name}`" :span="8" class="mb-8px">
          <div class="flex items-center">
            <Icon :icon="item.icon" class="mr-8px" />
            <el-link type="default" :underline="false" @click="handleClick(item.name)">
              {{ item.name }}
            </el-link>
          </div>
        </el-col>
      </el-row>
    </el-skeleton>
  </el-card>
</template>

<script lang="ts" setup>
import type { Shortcut } from '../types'

defineProps({
  loading: {
    type: Boolean,
    required: true
  },
  shortcut: {
    type: Array as PropType<Shortcut[]>,
    required: true
  }
})

const emit = defineEmits(['click'])
const { t } = useI18n()

/**
 * 处理点击事件
 */
const handleClick = (name: string) => {
  emit('click', name)
}
</script>

<style scoped>
</style> 