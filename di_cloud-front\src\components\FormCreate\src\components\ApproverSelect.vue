<template>
  <div class="approver-select">
    <el-form ref="formRef" :model="formData" :rules="rules">
      <!-- 审批人配置列表 -->
      <div v-for="(item, index) in formData.approvers" :key="index" class="approver-item">
        <el-row :gutter="12">
          <!-- 部门选择 -->
          <el-col :span="11">
            <el-form-item :label="'部门' + (index + 1)" :prop="'approvers.' + index + '.deptId'">
              <el-select
                v-model="item.deptId"
                style="width: 100%"
                placeholder="请选择部门"
                clearable
                @change="validateForm"
              >
                <template v-for="dept in deptOptions" :key="dept.id">
                  <el-option
                    v-if="dept.id !== undefined"
                    :label="dept.name"
                    :value="dept.id"
                  />
                </template>
              </el-select>
            </el-form-item>
          </el-col>
          
          <!-- 岗位选择 -->
          <el-col :span="11">
            <el-form-item :label="'岗位' + (index + 1)" :prop="'approvers.' + index + '.postIds'">
              <el-select
                v-model="item.postIds"
                multiple
                clearable
                style="width: 100%"
                placeholder="请选择岗位"
                @change="validateForm"
              >
                <template v-for="post in postOptions" :key="post.id">
                  <el-option
                    v-if="post.id !== undefined"
                    :label="post.name"
                    :value="post.id"
                  />
                </template>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 删除按钮 -->
          <el-col :span="2" class="flex items-center">
            <el-button 
              @click="removeApprover(index)" 
              type="danger" 
              :icon="Delete"
              circle
              :disabled="formData.approvers.length <= 1"
            />
          </el-col>
        </el-row>
      </div>
      
      <!-- 添加审批人按钮 -->
      <el-row class="mb-4">
        <el-col :span="24">
          <el-button @click="addApprover" type="primary" :icon="Plus">添加审批人</el-button>
        </el-col>
      </el-row>
      
      <!-- 审批方式 -->
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item label="审批方式" prop="approveType">
            <el-radio-group v-model="formData.approveType" @change="validateForm">
              <el-radio :label="1">依次审批</el-radio>
              <el-radio :label="2">会签（需全部同意）</el-radio>
              <el-radio :label="3">或签（一人同意即可）</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as PostApi from '@/api/system/post'
import type { PostVO } from '@/api/system/post'
import type { DeptVO } from '@/api/system/dept'
import { Delete, Plus } from '@element-plus/icons-vue'
import type { FormRules, FormInstance } from 'element-plus'

defineOptions({ name: 'ApproverSelect' })

interface ApproverValue {
  approvers: {
    deptId: number;
    postIds: number[];
  }[];
  approveType: number;
}

const props = defineProps({
  modelValue: {
    type: Object as PropType<ApproverValue>,
    default: () => ({
      approvers: [],
      approveType: 1
    })
  },
  // formCreate注入的属性
  formCreateInject: {
    type: Object,
    default: () => ({})
  }
})

// 添加formCreate所需的emit
const emit = defineEmits([
  'update:modelValue', 
  'change',
  // formCreate事件
  'fc.updateValue',
  'fc.el',
  'update'
])

interface ApproverItem {
  deptId: number;
  postIds: number[];
}

interface FormData {
  approvers: ApproverItem[];
  approveType: number;
}

// 表单数据
const formData = ref<FormData>({
  approvers: [{
    deptId: undefined as unknown as number,
    postIds: []
  }],
  approveType: 1
})

// 表单校验规则
const rules: FormRules = {
  'approvers.0.deptId': [{
    required: true,
    message: '请选择部门',
    trigger: 'change'
  }],
  'approvers.0.postIds': [{
    required: true,
    message: '请选择岗位',
    trigger: 'change'
  }],
  // approveType: [{
  //   required: true,
  //   message: '请选择审批方式',
  //   trigger: 'change'
  // }]
}

// 部门列表数据
const deptOptions = ref<DeptVO[]>([])
// 岗位列表数据
const postOptions = ref<PostVO[]>([])

// 表单实例引用
const formRef = ref<FormInstance>()

// 监听modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    if (val && val.approvers && val.approvers.length > 0) {
      formData.value = {
        approvers: val.approvers.map(item => ({
          deptId: item.deptId,
          postIds: item.postIds || []
        })),
        approveType: val.approveType || 1
      }
    }
  },
  { immediate: true, deep: true }
)

// 添加审批人
const addApprover = () => {
  const newIndex = formData.value.approvers.length
  formData.value.approvers.push({
    deptId: undefined as unknown as number,
    postIds: []
  })
  
  // 动态添加新的验证规则
  rules[`approvers.${newIndex}.deptId`] = [{
    required: true,
    message: '请选择部门',
    trigger: 'change'
  }]
  
  rules[`approvers.${newIndex}.postIds`] = [{
    required: true,
    message: '请选择岗位',
    trigger: 'change'
  }]
}

// 删除审批人
const removeApprover = (index: number) => {
  if (formData.value.approvers.length > 1) {
    formData.value.approvers.splice(index, 1)
    
    // 重新调整验证规则
    const newRules: FormRules = {
      approveType: rules.approveType
    }
    
    formData.value.approvers.forEach((_, idx) => {
      newRules[`approvers.${idx}.deptId`] = [{
        required: true,
        message: '请选择部门',
        trigger: 'change'
      }]
      
      newRules[`approvers.${idx}.postIds`] = [{
        required: true,
        message: '请选择岗位',
        trigger: 'change'
      }]
    })
    
    Object.assign(rules, newRules)
  }
}

// 验证表单
const validateForm = () => {
  nextTick(() => {
    formRef.value?.validate().catch(() => {
      // 忽略验证错误
    })
  })
}

// 监听表单数据变化
watch(
  formData,
  (newVal) => {
    // 在发送数据前过滤无效值
    const validData = {
      approvers: newVal.approvers
        .filter(item => item.deptId && item.postIds.length > 0)
        .map(item => ({
          deptId: item.deptId,
          postIds: item.postIds
        })),
      approveType: newVal.approveType
    }

    // 手动触发表单验证
    validateForm()

    // 更新值
    emit('update:modelValue', validData)
    emit('change', validData)
    
    // 触发formCreate事件
    emit('fc.updateValue', validData)
    emit('update', validData)
  },
  { deep: true }
)

// 初始化数据
const initData = async () => {
  try {
    // 获取部门列表
    const deptList = await DeptApi.getSimpleDeptList()
    deptOptions.value = deptList
    
    // 获取岗位列表
    const postList = await PostApi.getSimplePostList()
    postOptions.value = postList
  } catch (error) {
    console.error('初始化审批人选择器数据失败:', error)
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  initData()
})
</script>

<style scoped>
.approver-select {
  width: 100%;
  padding: 10px;
}

.approver-item {
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.mb-4 {
  margin-bottom: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}
</style> 
