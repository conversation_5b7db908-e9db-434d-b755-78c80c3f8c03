<template>
  <!-- 当有轮播图数据时显示轮播图 -->
  <el-carousel 
    v-if="limitedImgURLs.length > 0"
    :interval="3000" 
    arrow="always" 
    type="card" 
    height='25vw'  
    style="max-height:445px; background-color: #FFFFFF;"
  >
    <el-carousel-item v-for="(item, index) in limitedImgURLs" :key="item.id || index" @click="handleImageClick(item)">
      <div class="carousel-image-wrapper">
        <div class="carousel-image-container">
          <img :src="item.url" />
        </div>
      </div>
    </el-carousel-item>
  </el-carousel>
  
  <!-- 当没有轮播图数据时显示替代内容 -->
  <div 
    v-else 
    class="empty-carousel-container"
    style="height: 25vw; max-height: 445px;"
  >
    <div class="empty-content">
      <!-- <div class="system-icon">
        <el-icon size="80">
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path fill="currentColor" d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"/>
            <path fill="currentColor" d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.4-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.8 41.3-19.8 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0 1 30.9-44.8c59-22.7 97.1-74.7 97.1-132.5 0-39.3-17.2-76-48.4-103.3z"/>
            <path fill="currentColor" d="M512 732a40 40 0 1 0 0 80 40 40 0 0 0 0-80z"/>
          </svg>
        </el-icon>
      </div> -->
      <div class="system-title">纪检云系统上线</div>
      <div class="system-subtitle">欢迎使用纪检监察云平台</div>
      <div class="decorative-line"></div>
      <!-- <div class="system-description">
        系统正在为您准备最新资讯内容
      </div> -->
    </div>
    <div class="background-pattern">
      <div class="pattern-circle circle-1"></div>
      <div class="pattern-circle circle-2"></div>
      <div class="pattern-circle circle-3"></div>
    </div>
  </div>
</template>
  <script  lang="ts" setup>
    defineOptions({ name: 'CarouselForm' })
    
    interface ImageItem {
      id: string;
      url: string;
      title?: string;
      [key: string]: any;
    }
    
    const props = defineProps({
      imgURLs: {
        type: Array as () => ImageItem[],
        default: () => []
      }
    })
    
    // 计算属性，限制只显示前四个图片
    import { computed} from 'vue';
    const limitedImgURLs = computed(() => {
      return props.imgURLs?.slice(0, 4) || [];
    });
    
    // 定义 emit 事件
    const emit = defineEmits<{
      (e: 'click', imgURL: string): void;
    }>();

    // 点击图片时触发事件
    const handleImageClick = (item: ImageItem) => {
      emit('click', item.id); // 传递点击的新闻id传递给父组件
    };
  
  </script>
 <style lang="scss" scoped>
 .carousel-image-wrapper {
   width: 100%;
   height: 100%;
   max-height:445px;
   display: flex;
   justify-content: center;
   align-items: center;
 }
 
 .carousel-image-container {
   width: 100%;
   max-height: 100%;
   aspect-ratio: 16/9; /* 设置宽高比为16:9 */
   overflow: hidden;
   display: flex;
   justify-content: center;
   align-items: center;
   background-color: #f0f0f0; /* 容器背景色，当图片未完全填充时显示 */
 }
 
 .el-carousel__item img {
   width: 100%;
   height: 100%;
   object-fit: cover; /* 使用cover确保图片填满容器并裁剪多余部分 */
 }

 /* 为卡片式轮播添加额外样式 */
 .el-carousel[type="card"] .el-carousel__item {
   border-radius: 8px;
   overflow: hidden;
 }

 /* 空状态容器样式 */
 .empty-carousel-container {
   position: relative;
   display: flex;
   align-items: center;
   justify-content: center;
   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
   border-radius: 8px;
   overflow: hidden;
   box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
 }

 .empty-content {
   position: relative;
   z-index: 2;
   text-align: center;
   color: white;
   padding: 40px 20px;
 }

 .system-icon {
   margin-bottom: 24px;
   opacity: 0.9;
   animation: float 3s ease-in-out infinite;
 }

 .system-title {
   font-size: 32px;
   font-weight: 700;
   margin-bottom: 12px;
   text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
   letter-spacing: 2px;
 }

 .system-subtitle {
   font-size: 18px;
   font-weight: 400;
   margin-bottom: 20px;
   opacity: 0.9;
   letter-spacing: 1px;
 }

 .decorative-line {
   width: 80px;
   height: 3px;
   background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
   margin: 0 auto 20px;
   border-radius: 2px;
 }

 .system-description {
   font-size: 16px;
   opacity: 0.8;
   font-weight: 300;
   letter-spacing: 0.5px;
 }

 .background-pattern {
   position: absolute;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   z-index: 1;
   overflow: hidden;
 }

 .pattern-circle {
   position: absolute;
   border-radius: 50%;
   background: rgba(255, 255, 255, 0.1);
   animation: pulse 4s ease-in-out infinite;
 }

 .circle-1 {
   width: 120px;
   height: 120px;
   top: 20%;
   left: 10%;
   animation-delay: 0s;
 }

 .circle-2 {
   width: 80px;
   height: 80px;
   top: 60%;
   right: 15%;
   animation-delay: 1s;
 }

 .circle-3 {
   width: 60px;
   height: 60px;
   bottom: 20%;
   left: 20%;
   animation-delay: 2s;
 }

 @keyframes float {
   0%, 100% {
     transform: translateY(0px);
   }
   50% {
     transform: translateY(-10px);
   }
 }

 @keyframes pulse {
   0%, 100% {
     transform: scale(1);
     opacity: 0.1;
   }
   50% {
     transform: scale(1.1);
     opacity: 0.2;
   }
 }

 /* 响应式设计 */
 @media (max-width: 768px) {
   .system-title {
     font-size: 24px;
   }
   
   .system-subtitle {
     font-size: 16px;
   }
   
   .system-description {
     font-size: 14px;
   }
   
   .empty-content {
     padding: 30px 15px;
   }
 }
  </style>
