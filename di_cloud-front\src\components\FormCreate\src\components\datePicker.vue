<template>
  <div class="demo-date-picker">
    <div class="block">
      <el-date-picker
        v-model="value"
        type="date"
        placeholder="Pick a day"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
//import { ref } from 'vue'
defineOptions({ name: 'ShowDefaultDate' })

//创建一个表示当前时间的 Date 对象
const nowTime = new Date();
const year = nowTime.getFullYear();
const month = String(nowTime.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1 并补零
const day = String(nowTime.getDate()).padStart(2, '0');
const currentDate = ref(`${year}-${month}-${day}`);
const value = ref(currentDate.value);
// console.log(currentDate.value);
// 获取并格式化当前时间的年月日
//const value = ref(nowTime.toLocaleDateString());
//console.log(nowTime.toLocaleDateString());
//const value = ref('2021-01-29')

// 如果需要在组件挂载时动态设置当前日期
// onMounted(() => {
//   value.value = new Date().toLocaleDateString();
// });

</script>

